name: yalla_gai_driver
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using flutter pub publish. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.11+19

environment:
  sdk: ">=3.5.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running flutter pub upgrade --major-versions. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run flutter pub outdated.
dependencies:
  audioplayers: ^6.1.0
  dartz: ^0.10.1

  equatable: ^2.0.5
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_bloc: ^9.1.0
#  flutter_polyline_points: ^1.0.0
  #flutter_share_me: ^0.11.0
  #flutter_share_me: ^1.4.0
  flutter_svg: ^2.0.7
  geocoding: ^3.0.0

  go_router: ^14.6.0
  google_maps_flutter: ^2.9.0
  google_maps_webservice: any
  image_picker: ^1.1.2

  intl: 
  intl_phone_field: ^3.2.0
  circle_flags: ^5.0.0
  permission_handler: ^12.0.0+1
  pin_code_fields: ^8.0.1
  responsive_sizer: ^3.3.1

  url_launcher: ^6.3.1
  provider: ^6.1.2
  shared_preferences: ^2.3.3
  action_slider: ^0.7.0
  device_info_plus: ^11.0.0
  geolocator: ^13.0.1
  animated_custom_dropdown: ^3.0.0
  cached_network_image: ^3.3.1
  pusher_channels_flutter: ^2.2.1
  # pusher_channels_flutter:
    # git:
    #   url: https://github.com/pintusingh28/pusher-channels-flutter.git
      
  app_settings: ^6.1.1
  flex_color_picker:
  http: ^0.13.6
  bloc: ^9.0.0
  flutter_local_notifications: ^18.0.1
  flutter_slidable: ^4.0.0
  flutter_libphonenumber: ^2.5.0
  path_provider: ^2.1.5
  file_picker: ^10.1.2

  firebase_core: ^3.13.0
  firebase_messaging: ^15.2.5
  firebase_crashlytics: ^4.3.5
  firebase_analytics: ^11.4.5
  firebase_remote_config: ^5.4.3

  connectivity_plus: ^6.1.0

  stream_transform: ^2.1.0
  package_info_plus: ^8.1.1
  wakelock_plus: ^1.2.8

  crypto: ^3.0.6
  flutter_stripe: ^11.4.0
  flutter_image_compress: ^2.4.0
  crop_your_image: ^2.0.0
  flutter_foreground_task: ^9.1.0
  # flutter_overlay_window: ^0.5.0
  web_socket_channel: ^3.0.3
  flutter_gen: ^5.10.0

dev_dependencies:
  flutter_lints: ^5.0.0

flutter:
  generate: true
  uses-material-design: true

  assets:
    - assets/images/
    - assets/images/home/
    - assets/images/wallet/
    - assets/images/setting/
    - assets/images/driver/chat/
    - assets/images/driver/detail/
    - assets/images/supervisor/
    - assets/images/connect/
    - assets/images/offer/
    - assets/images/account/
    - assets/images/suggestions_and_complaints/
#    - assets/images/requests/
    - assets/logo/
    - assets/sounds/

  fonts:
    - family: Urbanist
      fonts:
        - asset: assets/fonts/Urbanist/Urbanist-VariableFont_wght.ttf

    - family: Plus Jakarta Sans
      fonts:
        - asset: assets/fonts/Plus_Jakarta_Sans/PlusJakartaSans-VariableFont_wght.ttf
