# Uncomment this line to define a global platform for your project
platform :ios, '15.5.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
  target 'RunnerTests' do
    inherit! :search_paths
  end
end

post_install do |installer|
	installer.aggregate_targets.each do |target|
		target.xcconfigs.each do |variant, xcconfig|
			xcconfig_path = target.client_root + target.xcconfig_relative_path(variant)
			IO.write(xcconfig_path, IO.read(xcconfig_path).gsub("DT_TOOLCHAIN_DIR", "TOOLCHAIN_DIR"))
		end
	end
	
  installer.pods_project.targets.each do |target|
    flutter_additional_ios_build_settings(target)
    target.build_configurations.each do |config|
      # Disable bitcode for Release (Flutter doesn't support it)
      config.build_settings['ENABLE_BITCODE'] = 'NO'
      
      # Enable dead code stripping for Release
      config.build_settings['DEAD_CODE_STRIPPING'] = 'YES'
      
      # Set optimization level (Swift)
      if config.name == 'Release'
        config.build_settings['SWIFT_OPTIMIZATION_LEVEL'] = '-O'
      end
      
			if config.base_configuration_reference.is_a? Xcodeproj::Project::Object::PBXFileReference
				xcconfig_path = config.base_configuration_reference.real_path
				IO.write(xcconfig_path, IO.read(xcconfig_path).gsub("DT_TOOLCHAIN_DIR", "TOOLCHAIN_DIR"))
			end
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '15.5'
       # Optional: Exclude arm64 for simulator builds (M1/M2 Macs)
      config.build_settings['EXCLUDED_ARCHS[sdk=iphonesimulator*]'] = 'arm64'
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',
        'PERMISSION_CAMERA=1',
        'PERMISSION_PHOTOS=1',
        'PERMISSION_LOCATION=1',
        'PERMISSION_NOTIFICATIONS=1',
      ]
    end
  end
	installer.generated_projects.each do |project|
		project.targets.each do |target|
						target.build_configurations.each do |config|
								 config.build_settings.delete 'IPHONEOS_DEPLOYMENT_TARGET'
						end
				end
		end
end
