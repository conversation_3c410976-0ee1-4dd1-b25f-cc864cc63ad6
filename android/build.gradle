buildscript {
       repositories {
           google()
           mavenCentral()
       }
       dependencies {
           classpath 'com.google.gms:google-services:4.3.15'
       }
   }

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

rootProject.layout.buildDirectory.set(file("../build"))
subprojects {
    project.layout.buildDirectory.set(rootProject.layout.buildDirectory.dir(project.name))
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    it.delete(rootProject.layout.buildDirectory.get())
}