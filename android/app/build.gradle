plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
}

def debugKeystoreProperties = new Properties()
def debugKeystorePropertiesFile = rootProject.file('debug-key.properties')
if (debugKeystorePropertiesFile.exists()) {
    debugKeystorePropertiesFile.withReader('UTF-8') { reader ->
        debugKeystoreProperties.load(reader)
    }
}

def releaseKeystoreProperties = new Properties()
def releaseKeystorePropertiesFile = rootProject.file('release-key.properties')
if (releaseKeystorePropertiesFile.exists()) {
    releaseKeystorePropertiesFile.withReader('UTF-8') { reader ->
        releaseKeystoreProperties.load(reader)
    }
}

android {
    namespace "com.yallagai"
    compileSdk 35

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "com.yallagai.captain"
        minSdkVersion 26
        targetSdkVersion 35
        multiDexEnabled true
        versionCode = flutter.versionCode
        versionName = flutter.versionName
    }

    signingConfigs {
        debug {
            keyAlias debugKeystoreProperties['keyAlias']
            keyPassword debugKeystoreProperties['keyPassword']
            storeFile debugKeystoreProperties['storeFile'] ? file(debugKeystoreProperties['storeFile']) : null
            storePassword debugKeystoreProperties['storePassword']
        }
        release {
            keyAlias releaseKeystoreProperties['keyAlias']
            keyPassword releaseKeystoreProperties['keyPassword']
            storeFile releaseKeystoreProperties['storeFile'] ? file(releaseKeystoreProperties['storeFile']) : null
            storePassword releaseKeystoreProperties['storePassword']
            enableV3Signing = true
            enableV4Signing = true
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            // proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            // // Enable Crashlytics mapping file upload
            // firebaseCrashlytics {
            //     mappingFileUploadEnabled true
            // }
            minifyEnabled false
            shrinkResources false
            ndk {
                abiFilters 'arm64-v8a', 'armeabi-v7a'
            }
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:2.1.2'
    implementation "com.google.android.material:material:1.12.0"
}
