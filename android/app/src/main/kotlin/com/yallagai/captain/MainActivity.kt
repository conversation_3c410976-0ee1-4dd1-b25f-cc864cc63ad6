package com.yallagai.captain

import android.content.Intent
import io.flutter.embedding.android.FlutterActivity
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.FlutterEngineGroup
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.BinaryMessenger
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugins.GeneratedPluginRegistrant


class MainActivity : FlutterActivity() {
    private val CHANNEL = "com.yallagai.captain/channel"
    private lateinit var engineGroup: FlutterEngineGroup

    override fun configureFlutterEngine(flutterEngine: FlutterEngine) {
        super.configureFlutterEngine(flutterEngine)

        engineGroup = FlutterEngineGroup(applicationContext)

        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
            .setMethodCallHandler { call, result ->
                when (call.method) {
                    "overlayMessage" -> {
                        MethodChannel(flutterEngine.dartExecutor.binaryMessenger, CHANNEL)
                            .invokeMethod("overlayMessage", call.arguments)
                        result.success(true)
                    }

                    "openMainApp" -> {
                        val intent = Intent(this, MainActivity::class.java)
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        startActivity(intent)
                        result.success(true)
                    }

                    "launchOverlay" -> {
                        launchOverlayEngine()
                        result.success(true)
                    }

                    else -> result.notImplemented()
                }
            }
    }

    private fun launchOverlayEngine() {
        val appBundlePath = applicationContext.applicationInfo.nativeLibraryDir
        val dartEntrypoint = DartExecutor.DartEntrypoint(appBundlePath, "mainOverlay")

        val overlayEngine = engineGroup.createAndRunEngine(this, dartEntrypoint)

        // REGISTER PLUGINS
        GeneratedPluginRegistrant.registerWith(overlayEngine)

        // CACHE ENGINE
        FlutterEngineCache.getInstance().put("overlay_engine", overlayEngine)

        // START FLUTTER ACTIVITY
        val intent = FlutterActivity
            .withCachedEngine("overlay_engine")
            .build(this)

        startActivity(intent)
    }

}
