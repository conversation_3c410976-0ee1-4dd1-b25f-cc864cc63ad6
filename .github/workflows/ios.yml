name: Build iOS

on:
  push:
    branches:
      - main

jobs:
  build-ios:
    runs-on: macos-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install flutter sdk
      uses: subosito/flutter-action@v2
      with:
        channel: stable

    - name: Install dependencies
      run: flutter pub get

    - name: Configure flutterfire cli
      run: dart pub global activate flutterfire_cli

    - name: Build iOS
      run: flutter build ios --release --no-codesign

    - name: Upload iOS artifacts
      uses: actions/upload-artifact@v4
      with:
        name: ios-app
        path: build/ios/iphoneos/Runner.app

