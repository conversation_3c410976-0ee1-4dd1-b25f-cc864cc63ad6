import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';
import '../domain/entity/notification_model.dart';

class NotificationRepository {
  final _httpClient = HttpApiClient();

  Future<List<NotificationMessage>> getNotifications() async {
    final response = await _httpClient.get(EndPoints.getNotifications());
    final responseData = jsonDecode(response.body);
    if (response.isSuccess) {
      List<NotificationMessage> notifications = [];
      for (var noti in responseData['data']) {
        if (noti["type"] == "notifications") {
          notifications.add(NotificationMessage.fromJson(noti));
        }
      }
      return notifications;
    } else {
      throw ServerException(message: responseData["message"]);
    }
  }

/*Future<ChatMessage?> sendMessage(String receiverId, String message) async {
    var response = await _httpClient.post(
      EndPoints.sendTechSupportChats(),
      body: jsonEncode({
        'receiver_id': receiverId,
        'message': message,
      }),
    );

    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (response.statusCode == 200) {
      return ChatMessage.fromJson(responseData['message']);
    } else {
      throw ServerException(message: responseData["message"]);
    }
  }*/
}
