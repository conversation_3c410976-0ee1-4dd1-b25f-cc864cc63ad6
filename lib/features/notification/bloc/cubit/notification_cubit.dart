import 'dart:convert';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';

import '../../../../core/notification/custom_local_notification.dart';
import '../../domain/entity/notification_model.dart';
import '../../repository/notification_repo.dart';

part 'notification_state.dart';

class NotificationCubit extends Cubit<NotificationState> {
  NotificationRepository notificationRepository;
  NotificationCubit(this.notificationRepository) : super(NotificationInitial());

  List<NotificationMessage> allNotification = [];
  onAcceptEvent(PusherEvents event) {
    log("Event::${event.channelName}");
    log("Event data::${event.data}");
    final data = event.data['message'];
    if (data != null) {
      emit(NotificationLoading());
      final status = NotificationMessage.fromJson(data);
      // log('before notification');
      CustomLocalNotification.showDriverNotification(status);
      // log('after notification');
      allNotification.add(status);
      emit(NotificationLoaded());
    }
  }

  getNotification() async {
    try {
      emit(NotificationLoading());
      allNotification = await notificationRepository.getNotifications();
      emit(NotificationLoaded());
    } catch (e) {
      log(e.toString());
      emit(NotificationError());
    }
  }
}
