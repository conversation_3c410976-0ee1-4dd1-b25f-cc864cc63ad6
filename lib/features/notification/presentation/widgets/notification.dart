import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';

class NotificationCard extends StatelessWidget {
  final String type;
  final String message;
  final String time;
  final bool seen;

  const NotificationCard(
      {super.key,
      required this.type,
      required this.message,
      required this.time,
      required this.seen});
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 20),
          child: Opacity(
            opacity: !seen ? 1 : 0.6,
            child: Container(
              height: 16.h,
              alignment: Alignment.bottomCenter,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[400]!),
                  borderRadius: const BorderRadius.all(Radius.circular(10))),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    time,
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(fontSize: 12, fontWeight: FontWeight.w400),
                  ),
                  const Gap(
                    w: 10,
                  ),
                  const VerticalDivider(),
                  const Gap(
                    w: 10,
                  ),
                  SizedBox(
                    width: 50.w,
                    child: Text(
                      message,
                      style: Theme.of(context)
                          .textTheme
                          .bodyMedium!
                          .copyWith(fontSize: 14, fontWeight: FontWeight.w600),
                    ),
                  )
                ],
              ),
            ),
          ),
        ),
        Positioned(
            top: 28,
            left: 8,
            child: Icon(
              Icons.circle,
              color: !seen ? Colors.red : Colors.grey,
              size: 12,
            )),
        Positioned(
            top: 0,
            right: 0,
            child: Container(
              height: 40,
              width: 40.w,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                  color: Colors.grey[600],
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                      bottomLeft: Radius.circular(10))),
              child: Center(
                child: Text(
                  type,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontSize: 14, fontWeight: FontWeight.w600, color: white),
                ),
              ),
            )),
      ],
    );
  }
}
