import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/nearby_users/nearby_users_cubit.dart';
import 'package:yalla_gai_driver/features/notification/bloc/cubit/notification_cubit.dart';

class Notifications extends StatefulWidget {
  const Notifications({super.key});

  @override
  State<Notifications> createState() => _NotificationsState();
}

class _NotificationsState extends State<Notifications> {
  @override
  void initState() {
    super.initState();
    context.read<NotificationCubit>().getNotification();
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final messages = context
        .watch<NotificationCubit>()
        .allNotification
        .where((e) => e.userId == null || e.userId.toString() == globalDriverProfile.user?.driver?.userId.toString())
        .toList();
    return AppScaffold(
      isLoading: context.watch<NotificationCubit>().state is NotificationLoading,
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.notifications,
        bottomBorder: true,
        // onBackButtonPressed: () {
        //   context.pop();assets\images\box.png
        // },
      ),
      body: messages.isEmpty
          ? const EmptyItemBox()
          : ListView.separated(
              padding: EdgeInsets.symmetric(horizontal: size.width * 0.03),
              itemCount: messages.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    ListTile(
                      onTap: () {
                        setState(() {
                          for (int i = 0; i < messages.length; i++) {
                            final message = messages[i];
                            if (i == index) {
                              message.isExpanded = !message.isExpanded;
                            } else {
                              message.isExpanded = false;
                            }
                          }
                        });
                      },
                      title: Text(
                        Utils.appText(
                            context: context,
                            text: messages[index].title ?? "",
                            arabicText: messages[index].arTitle ?? ""),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                      ),
                      trailing: Icon(messages[index].isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down),
                      subtitle: Text(
                        "${DateFormat.yMEd().format(DateTime.parse(messages[index].createdAt!).toLocal())} "
                        "${DateFormat.jms().format(DateTime.parse(messages[index].createdAt!).toLocal())}",
                        style: const TextStyle(fontSize: 14, color: Colors.green),
                      ),
                      leading: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: BoxDecoration(color: Colors.grey.shade200, shape: BoxShape.circle),
                        child: const Icon(Icons.notifications_outlined),
                      ),
                    ),
                    Gap(h: 1.h),
                    // if (selected[index])
                    Visibility(
                      visible: messages[index].isExpanded,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            const Gap(h: 16),
                            if (messages[index].filePath != null) ...[
                              ClipRRect(
                                borderRadius: BorderRadius.circular(2.w),
                                child: CachedNetworkImage(
                                  imageUrl: AppEnvironment.publicBaseUrl + messages[index].filePath!,
                                  height: 84,
                                  fit: BoxFit.contain,
                                  placeholder: (context, url) {
                                    return Center(
                                      child: CircularProgressIndicator(),
                                    );
                                  },
                                ),
                              ),
                              Gap(h: 2.h),
                            ],
                            Text(
                              Utils.appText(
                                  context: context,
                                  text: messages[index].title ?? "",
                                  arabicText: messages[index].arTitle ?? ""),
                              textAlign: TextAlign.center,
                              style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
                            ),
                            const Divider(),
                            Padding(
                              padding: EdgeInsets.symmetric(vertical: 1.h),
                              child: Text(
                                Utils.appText(
                                    context: context,
                                    text: messages[index].message ?? "",
                                    arabicText: messages[index].arMessage ?? ""),
                                style: const TextStyle(color: Colors.grey, fontSize: 16),
                                textAlign: TextAlign.start,
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                );
              },
              separatorBuilder: (context, index) => const Divider(height: 16),
            ),
    );
  }
}

class EmptyItemBox extends StatelessWidget {
  const EmptyItemBox({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Image.asset(
        "assets/images/box.png",
        height: 120,
        color: Colors.grey.shade700,
      ),
    );
  }
}

class NotificationWidget extends StatefulWidget {
  const NotificationWidget(
      {super.key,
      required this.size,
      required this.title,
      required this.image,
      required this.message,
      required this.date,
      required this.arTitle,
      required this.arMessage});

  final Size size;
  final String arTitle, title, image, arMessage, message, date;

  @override
  State<NotificationWidget> createState() => _NotificationWidgetState();
}

bool isExpanded = false;

class _NotificationWidgetState extends State<NotificationWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListTile(
          titleAlignment: ListTileTitleAlignment.center,
          minVerticalPadding: 2,
          dense: true,
          isThreeLine: true,
          title: Row(
            children: [
              Expanded(
                child: Text(
                  Utils.appText(context: context, text: widget.title, arabicText: widget.arTitle),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(fontSize: 15, fontFamily: 'Poppins', fontWeight: FontWeight.w500),
                ),
              ),
              IconButton(
                  onPressed: () {
                    setState(() {
                      isExpanded = !isExpanded;
                    });
                  },
                  icon: Icon(isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down, size: 25.sp))
            ],
          ),
          subtitle: Text(
            Utils.formatDate(widget.date),
            style: const TextStyle(color: Colors.green),
          ),
          leading: Container(
            padding: const EdgeInsets.all(6),
            decoration: const BoxDecoration(color: Color.fromARGB(237, 228, 240, 255), shape: BoxShape.circle),
            child: const Icon(Icons.notifications_outlined),
          ),
        ),
        AnimatedOpacity(
          opacity: isExpanded ? 1.0 : 0.0,
          duration: const Duration(seconds: 1),
          curve: Curves.easeInOut,
          child: !isExpanded
              ? null
              : Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 3.h),
                    CachedNetworkImage(
                      width: widget.size.width,
                      height: widget.size.height * 0.2,
                      fit: BoxFit.contain,
                      imageUrl: "${AppEnvironment.publicBaseUrl}${widget.image}",
                      placeholder: (context, url) => const CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation(primaryColor),
                      ),
                      errorWidget: (context, url, error) => Icon(Icons.error, size: 20.sp),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      maxLines: 10,
                      Utils.appText(context: context, text: widget.message, arabicText: widget.arMessage),
                      style: const TextStyle(color: Colors.black),
                    ),
                    SizedBox(height: 3.h),
                  ],
                ),
        ),
        const Divider(),
      ],
    );
  }
}

// ListView.separated(
//           itemCount: allNotification.length,
//           itemBuilder: (context, index) {
//             return Column(
//               children: [
//                 Gap(h: 1.h),
//                 // if (selected[index])
//                 Padding(
//                   padding: EdgeInsets.symmetric(horizontal: 6.w),
//                   child: Column(
//                     children: [
//                       Row(children: [
//                         Text(
//                           allNotification[index].message ?? "",
//                           style: const TextStyle(color: Colors.grey),
//                         )
//                       ]),
//                       Gap(h: 3.h),
//                       // SvgPicture.asset(yallaGai),
//                       Image.asset(
//                         yallaGaiLogo,
//                         color: Colors.black,
//                       ),
//                       Gap(h: 2.h),
//                       Text(
//                         '${AppLocalizations.of(context)!.discount_code_first_two_trips} \n 90%',
//                         textAlign: TextAlign.center,
//                         style: const TextStyle(
//                             fontWeight: FontWeight.bold, fontSize: 22),
//                       ),
//                       const Divider(),
//                       Padding(
//                         padding: EdgeInsets.symmetric(vertical: 1.h),
//                         child: Text(
//                           allNotification[index].message ?? "",
//                           style: const TextStyle(color: Colors.grey),
//                         ),
//                       ),
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Text(
//                             AppLocalizations.of(context)!.valid_until,
//                             style:
//                                 const TextStyle(fontWeight: FontWeight.w600),
//                           ),
//                           SizedBox(width: 3.w),
//                           Container(
//                             padding: EdgeInsets.symmetric(
//                                 horizontal: 2.w, vertical: 3),
//                             decoration: BoxDecoration(
//                                 color:
//                                     const Color.fromARGB(237, 228, 240, 255),
//                                 borderRadius: BorderRadius.circular(20)),
//                             child: Text(allNotification[index].arTitle ?? ""),
//                           )
//                         ],
//                       )
//                     ],
//                   ),
//                 )
//               ],
//             );
//           },
//           separatorBuilder: (context, index) => const Divider()
