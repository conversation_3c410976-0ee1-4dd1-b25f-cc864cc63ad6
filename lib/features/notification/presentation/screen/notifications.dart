// import 'package:flutter/material.dart';
// import 'package:intl/intl.dart';
// import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
// import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
// import 'package:yalla_gai_driver/features/notification/domain/entity/notification.dart';
// import 'package:yalla_gai_driver/features/notification/presentation/widgets/notification.dart';

// class Notifications extends StatelessWidget {
//   @override
//   Widget build(BuildContext context) {
//     final notifications = [
//       NotificationMessage(
//           type: 'Admin',
//           message: 'You need to go to Marthahalli',
//           time: DateFormat('HH: mm: ss').format(DateTime.now())),
//       NotificationMessage(
//           type: 'Admin',
//           message: 'You need to go to Marthahalli',
//           time: DateFormat('HH: mm: ss').format(DateTime.now()))
//     ];
//     return Scaffold(
//       appBar: CustomAppBar(title: 'Notification'),
//       body: Container(
//         padding: EdgeInsets.only(top: 16, left: 16, right: 16),
//         child: ListView.separated(
//           shrinkWrap: true,
//           separatorBuilder: (BuildContext context, int index) => const Gap(),
//           itemCount: notifications.length,
//           itemBuilder: (BuildContext context, int index) {
//             return NotificationCard(
//               type: notifications[index].type,
//               message: notifications[index].message,
//               time: notifications[index].time,
//               seen: index % 2 == 0,
//             );
//           },
//         ),
//       ),
//     );
//   }
// }
