class NotificationMessage {
  String? title;
  String? arTitle;
  String? arMessage;
  String? message;
  String? filePath;
  String? roleId;
  String? userId;
  String? cityId;
  String? countryId;
  String? updatedAt;
  String? createdAt;
  String? id;
  bool isExpanded = false;

  NotificationMessage(
      {this.title,
      this.arTitle,
      this.arMessage,
      this.message,
      this.filePath,
      this.roleId,
      this.userId,
      this.cityId,
      this.countryId,
      this.updatedAt,
      this.createdAt,
      this.id});

  NotificationMessage.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    arTitle = json['ar_title'];
    arMessage = json['ar_message'];
    message = json['message'];
    filePath = json['file_path'];
    roleId = json['role_id']?.toString();
    userId = json['user_id']?.toString();
    cityId = json['city_id']?.toString();
    countryId = json['country_id']?.toString();
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['ar_title'] = arTitle;
    data['ar_message'] = arMessage;
    data['message'] = message;
    data['file_path'] = filePath;
    data['role_id'] = roleId;
    data['user_id'] = userId;
    data['city_id'] = cityId;
    data['country_id'] = countryId;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['id'] = id;
    return data;
  }
}
