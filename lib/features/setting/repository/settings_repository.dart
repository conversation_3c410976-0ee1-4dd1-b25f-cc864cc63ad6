import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/setting/model/app_version_model.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';

class SettingsRepository {
  final _httpClient = HttpApiClient();

  SettingsRepository();

  Future<AppVersion?> getApplicationVersion() async {
    var response = await _httpClient.get(EndPoints.appVersion());

    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (responseData['status'] == true) {
      if (responseData["data"] is Iterable) {
        final data = responseData["data"][0];
        return AppVersion.fromJson(data);
      }
    } else {
      final errors = responseData["message"];
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }
}
