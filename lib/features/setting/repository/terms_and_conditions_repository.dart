import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';
import '../model/privacy_policy.dart';

class TermsAndConditionRepository {
  final _httpClient = HttpApiClient();

  Future<PrivacyAndTermsContent?> getTermsConditions() async {
    var response = await _httpClient.get(EndPoints.getTermsAndConditions());
    Map<String, dynamic> responseData = jsonDecode(response.body);

    if (responseData['status'] == true) {
      if (responseData["data"] is Iterable) {
        final data = responseData["data"][0];
        return PrivacyAndTermsContent(
          content: data["name_the_condition"],
          arabicContent: data["arabic_name_the_condition"],
        );
      }
    } else {
      final errors = responseData["message"];
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  Future<PrivacyAndTermsContent?> getPrivacyPolicy() async {
    var response = await _httpClient.get(EndPoints.getTermsAndConditions());
    Map<String, dynamic> responseData = jsonDecode(response.body);

    if (responseData['status'] == true) {
      if (responseData["data"] is Iterable) {
        final data = responseData["data"][0];
        return PrivacyAndTermsContent(
          content: data["name"],
          arabicContent: data["arabic_name"],
        );
      }
    } else {
      final errors = responseData["message"];
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }
}
