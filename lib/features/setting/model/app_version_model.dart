class AppVersion {
  String? id;
  String? currentVersion;
  String? minAllowed;
  String? roleId;
  String? createdAt;
  String? updatedAt;

  AppVersion(
      {this.id,
      this.currentVersion,
      this.minAllowed,
      this.roleId,
      this.createdAt,
      this.updatedAt});

  AppVersion.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    currentVersion = json['current_version'];
    minAllowed = json['min_allowed'];
    roleId = json['role_id']?.toString();
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}

class RatingLink {
  String? id;
  String? type;
  String? link;

  String? roleId;
  String? createdAt;
  String? updatedAt;

  RatingLink(
      {this.id,
      this.type,
      this.link,
      this.roleId,
      this.createdAt,
      this.updatedAt});

  RatingLink.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    type = json['type'];
    link = json['link'];
    roleId = json['role_id']?.toString();
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}
