import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../model/privacy_policy.dart';
import '../../repository/terms_and_conditions_repository.dart';

part 'terms_and_condition_event.dart';
part 'terms_and_condition_state.dart';

class TermsAndConditionsBloc extends Bloc<TermsAndConditionsEvent, TermsAndConditionsState> {
  TermsAndConditionRepository termsAndConditionRepository = TermsAndConditionRepository();

  TermsAndConditionsBloc() : super(TermsAndConditionsInitial()) {
    ////// TermsAndConditionsGet
    on<TermsAndConditionsGet>((event, emit) async {
      try {
        emit(TermsAndConditionsGetLoading());
        PrivacyAndTermsContent? termsAndConditions = await termsAndConditionRepository.getTermsConditions();
        emit(TermsAndConditionsGetSuccess(termsAndConditions: termsAndConditions));
      } catch (e) {
        emit(TermsAndConditionsGetFaulire(message: e.toString()));
      }
    });
    ////// TermsAndConditionsGet
    on<TermsAndConditionsGetPrivacyPolicy>((event, emit) async {
      try {
        emit(TermsAndConditionsGetPrivacyPolicyLoading());
        PrivacyAndTermsContent? privacyPolicy = await termsAndConditionRepository.getPrivacyPolicy();
        emit(TermsAndConditionsGetPrivacyPolicySuccess(privacyPolicy: privacyPolicy));
      } catch (e) {
        emit(TermsAndConditionsGetPrivacyPolicyFaulire(message: e.toString()));
      }
    });
  }
}
