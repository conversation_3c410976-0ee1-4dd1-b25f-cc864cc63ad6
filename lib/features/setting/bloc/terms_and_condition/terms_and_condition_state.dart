part of 'terms_and_condition_bloc.dart';

abstract class TermsAndConditionsState extends Equatable {
  const TermsAndConditionsState();

  @override
  List<Object> get props => [];
}

final class TermsAndConditionsInitial extends TermsAndConditionsState {}

final class TermsAndConditionsGetLoading extends TermsAndConditionsState {}

final class TermsAndConditionsGetSuccess extends TermsAndConditionsState {
  final PrivacyAndTermsContent? termsAndConditions;
  const TermsAndConditionsGetSuccess({required this.termsAndConditions});
}

final class TermsAndConditionsGetFaulire extends TermsAndConditionsState {
  final String message;
  const TermsAndConditionsGetFaulire({required this.message});
}

final class TermsAndConditionsGetPrivacyPolicyLoading
    extends TermsAndConditionsState {}

final class TermsAndConditionsGetPrivacyPolicySuccess
    extends TermsAndConditionsState {
  final PrivacyAndTermsContent? privacyPolicy;
  const TermsAndConditionsGetPrivacyPolicySuccess(
      {required this.privacyPolicy});
}

final class TermsAndConditionsGetPrivacyPolicyFaulire
    extends TermsAndConditionsState {
  final String message;
  const TermsAndConditionsGetPrivacyPolicyFaulire({required this.message});
}
