part of 'terms_and_condition_bloc.dart';

abstract class TermsAndConditionsEvent extends Equatable {
  const TermsAndConditionsEvent();
}

class TermsAndConditionsGet extends TermsAndConditionsEvent {
  const TermsAndConditionsGet();

  @override
  List<Object?> get props => [];
}

class TermsAndConditionsGetPrivacyPolicy extends TermsAndConditionsEvent {
  const TermsAndConditionsGetPrivacyPolicy();

  @override
  List<Object?> get props => [];
}
