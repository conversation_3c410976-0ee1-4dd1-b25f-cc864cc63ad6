part of 'app_version_cubit.dart';

sealed class AppVersionState extends Equatable {
  const AppVersionState();

  @override
  List<Object> get props => [];
}

final class AppVersionInitial extends AppVersionState {}

final class AppVersionMustUpdate extends AppVersionState {}

final class AppVersionOptionalUpdate extends AppVersionState {}

final class AppVersionUnderMaintenance extends AppVersionState {}

final class AppVersionUpdated extends AppVersionState {}
