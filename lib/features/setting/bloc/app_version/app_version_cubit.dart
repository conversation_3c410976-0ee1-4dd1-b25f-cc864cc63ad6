import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/features/setting/repository/settings_repository.dart';

part 'app_version_state.dart';

class AppVersionCubit extends Cubit<AppVersionState> {
  SettingsRepository settingsRepository;

  AppVersionCubit(this.settingsRepository) : super(AppVersionInitial());

  getAppVersion() async {
    try {
      final data = await settingsRepository.getApplicationVersion();
      if (data == null) return;
      if(data.currentVersion == "0.0.0") {
        emit(AppVersionUnderMaintenance());
        return;
      }

      PackageInfo packageInfo = await PackageInfo.fromPlatform();
      log("Server:${data.currentVersion}--Device:${packageInfo.version}");
      // The current version on the device

      int comparisonToServer = compareVersions(data.currentVersion ?? "", packageInfo.version);
      int comparisonToMinAllowed = compareVersions(data.minAllowed ?? "", packageInfo.version);

      if (comparisonToMinAllowed > 0) {
        log("SSS The app version is too old. Forced update required.");
        emit(AppVersionMustUpdate());
      } else if (comparisonToServer > 0) {
        log("SSS The app is outdated. Please update.");
        final remindExpired = await remindMeExpired();
        if (remindExpired) {
          emit(AppVersionOptionalUpdate());
        }
      } else {
        log("SSS The app is up to date.");
        emit(AppVersionUpdated());
      }
    } catch (e) {
      log("Appversion:$e");
    }
  }

  int compareVersions(String version1, String version2) {
    List<String> v1Parts = version1.split('.'); // Splitting version1 into major, minor, patch
    List<String> v2Parts = version2.split('.'); // Splitting version2 into major, minor, patch

    for (int i = 0; i < v1Parts.length; i++) {
      int v1 = int.parse(v1Parts[i]); // Parsing the version components as integers
      int v2 = int.parse(v2Parts[i]);

      if (v1 > v2) {
        return 1; // version1 is greater
      } else if (v1 < v2) {
        return -1; // version2 is greater
      }
    }

    return 0; // Versions are equal
  }

  remindMelater() async {
    final pref = await SharedPreferences.getInstance();
    await pref.setString("RemindUpdateDate", DateTime.now().toString());
    emit(AppVersionInitial());
  }

  Future<bool> remindMeExpired() async {
    bool response = true;
    final pref = await SharedPreferences.getInstance();
    final date = pref.getString("RemindUpdateDate");
    if (date != null) {
      var inDays = DateTime.now().difference(DateTime.parse(date)).inDays;

      response = inDays >= 3;
    }
    log("message:$response");
    return response;
  }
}
