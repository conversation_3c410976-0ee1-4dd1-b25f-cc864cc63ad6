import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/links/links_cubit.dart';
import 'package:yalla_gai_driver/features/authentication/model/driver_profile.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/service_bloc/service_bloc.dart';
import 'package:yalla_gai_driver/notifications/firebase_push_notifications_helper.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/images.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {

  @override
  void initState() {
    super.initState();
    context.read<LinksCubit>().fetchLinks();
  }

  @override
  Widget build(BuildContext context) {
    final List<SettingsItem> items = [
      SettingsItem(
          icon: Icons.phone,
          name: AppLocalizations.of(context)!.phone_number,
          onTap: () {
            showGeneralDialog(
              barrierDismissible: true,
              barrierLabel: '',
              barrierColor: Colors.black38,
              transitionDuration: const Duration(milliseconds: 500),
              pageBuilder: (ctx, anim1, anim2) => AlertDialog(
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(phoneConformationImage),
                    Text(AppLocalizations.of(context)!.new_phone_number),
                    const Gap(),
                    const Divider(),
                    const Gap(),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        CustomButton(
                          buttonText: AppLocalizations.of(context)!.no,
                          onPressed: () {
                            Navigator.pop(context);
                          },
                          width: MediaQuery.of(context).size.width * 0.8,
                          borderOnly: true,
                          borderRadius: 10,
                        ),
                        const Gap(),
                        CustomButton(
                          buttonText: AppLocalizations.of(context)!.yes,
                          width: MediaQuery.of(context).size.width * 0.8,
                          onPressed: () {
                            Navigator.pop(context);
                            context.push(path.changePhoneNumber);
                          },
                          borderRadius: 10,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              transitionBuilder: (ctx, anim1, anim2, child) => BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 4 * anim1.value, sigmaY: 4 * anim1.value),
                child: FadeTransition(
                  opacity: anim1,
                  child: child,
                ),
              ),
              context: context,
            );
          },),
      SettingsItem(
          icon: Icons.language,
          name: AppLocalizations.of(context)!.language,
          onTap: () {
            context.push(path.languageSetting);
          },),
      // SettingsItem(
      //     icon: Icons.calendar_today,
      //     name: 'Date and Distances',
      //     onTap: () {
      //       context.push(path.dateAndDistanceSetting);
      //     }),
      SettingsItem(
          icon: Icons.rule,
          name: AppLocalizations.of(context)!.rules_terms,
          onTap: () {
            context.push(path.rulesAndTerms);
          },),
      SettingsItem(
        icon: Icons.delete_outline,
        name: AppLocalizations.of(context)!.delete_account,
        onTap: () {
          _deleteAccount(context);
        },
      ),
    ];

    return Scaffold(
        appBar: CustomAppBar(
          onBackButtonPressed: () {
            context.go(path.home);
          },
          title: AppLocalizations.of(context)!.settings,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: ListView.separated(
                  separatorBuilder: (BuildContext context, int index) => const Gap(),
                  itemCount: items.length,
                  itemBuilder: (BuildContext context, int index) {
                    return ListTile(
                      leading: Icon(items[index].icon),
                      title: Text(items[index].name),
                      onTap: items[index].onTap,
                      trailing: const Icon(Icons.keyboard_arrow_right),
                    );
                  },
                ),
              ),
              // const Spacer(),
              CustomButton(
                buttonText: AppLocalizations.of(context)!.log_out,
                borderRadius: 10,
                width: MediaQuery.of(context).size.width * 0.6,
                onPressed: () {
                  showGeneralDialog(
                    barrierDismissible: true,
                    barrierLabel: '',
                    barrierColor: Colors.black38,
                    transitionDuration: const Duration(milliseconds: 500),
                    pageBuilder: (ctx, anim1, anim2) => AlertDialog(
                      content: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(AppLocalizations.of(context)!.confirm_log_out,
                              style: Theme.of(context).textTheme.bodyLarge, textAlign: TextAlign.center,),
                          const Gap(),
                          Text(
                            AppLocalizations.of(context)!.log_out_warning,
                            style: Theme.of(context)
                                .textTheme
                                .labelMedium!
                                .copyWith(color: Colors.black.withOpacity(0.4), fontWeight: FontWeight.normal),
                            textAlign: TextAlign.center,
                          ),
                          const Gap(),
                          const Divider(),
                          const Gap(),
                          Column(
                            children: [
                              CustomButton(
                                buttonText: AppLocalizations.of(context)!.no,
                                onPressed: () {
                                  // Handle 'No' button press
                                  ctx.pop();
                                },
                                width: MediaQuery.of(context).size.width * 0.8,
                                borderOnly: true,
                                borderRadius: 10,
                              ),
                              const Gap(),
                              CustomButton(
                                buttonText: AppLocalizations.of(context)!.yes,
                                width: MediaQuery.of(context).size.width * 0.8,
                                onPressed: () async {
                                  BlocProvider.of<ServiceBloc>(context)
                                      .add(const ServiceChangeStatus(id: null, status: 'off'));
                                  CustomSocket.disconnect();
                                  globalDriverProfile = DriverProfile();
                                  LocalStorage.instance.clear();
                                  PushNotificationHelper.deleteFcmToken();
                                  Stripe.instance.resetPaymentSheetCustomer();
                                  GoRouter.of(context).go(path.login);
                                },
                                borderRadius: 10,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    transitionBuilder: (ctx, anim1, anim2, child) => BackdropFilter(
                      filter: ImageFilter.blur(sigmaX: 4 * anim1.value, sigmaY: 4 * anim1.value),
                      child: FadeTransition(
                        opacity: anim1,
                        child: child,
                      ),
                    ),
                    context: context,
                  );
                },
              ),
            ],
          ),
        ),);
  }

  void _deleteAccount(BuildContext context) {
    showGeneralDialog(
      barrierDismissible: true,
      barrierLabel: '',
      barrierColor: Colors.black38,
      transitionDuration: const Duration(milliseconds: 500),
      pageBuilder: (ctx, anim1, anim2) => AlertDialog(
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(AppLocalizations.of(context)!.delete_account,
                style: Theme.of(context).textTheme.bodyLarge, textAlign: TextAlign.center,),
            const Gap(),
            Text(
              AppLocalizations.of(context)!.delete_account_warning,
              style: Theme.of(context)
                  .textTheme
                  .labelMedium!
                  .copyWith(color: Colors.black.withOpacity(0.4), fontWeight: FontWeight.normal),
              textAlign: TextAlign.center,
            ),
            const Gap(),
            const Divider(),
            const Gap(),
            Column(
              children: [
                CustomButton(
                  buttonText: AppLocalizations.of(context)!.no,
                  onPressed: () {
                    context.pop();
                  },
                  width: MediaQuery.of(context).size.width * 0.8,
                  borderOnly: true,
                  borderRadius: 10,
                ),
                const Gap(),
                BlocConsumer<AuthBloc, AuthState>(
                  listener: (context, state) {
                    if (state is AuthDeleteAccountSuccess) {
                      CustomSocket.disconnect();
                      globalDriverProfile = DriverProfile();
                      LocalStorage.instance.clear();
                      PushNotificationHelper.deleteFcmToken();
                      Stripe.instance.resetPaymentSheetCustomer();
                      GoRouter.of(context).go(path.login);
                    }

                    if (state is AuthDeleteAccountFailure) {
                      showCustomSnackbar(context, state.message ?? "Unable to delete account! Something went wrong!");
                    }
                  },
                  builder: (context, state) {
                    return CustomButton(
                      color: Colors.red,
                      isLoading: state is AuthDeleteAccountLoading,
                      buttonText: AppLocalizations.of(context)!.delete_account,
                      borderRadius: 10,
                      width: 80.w,
                      onPressed: () => context.read<AuthBloc>().add(const AuthDeleteAccount()),
                    );
                  },
                ),
              ],
            ),
          ],
        ),
      ),
      transitionBuilder: (ctx, anim1, anim2, child) => BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 4 * anim1.value, sigmaY: 4 * anim1.value),
        child: FadeTransition(
          opacity: anim1,
          child: child,
        ),
      ),
      context: context,
    );
  }
}

class SettingsItem {
  final IconData icon;
  final String name;
  final VoidCallback onTap;

  const SettingsItem({required this.icon, required this.name, required this.onTap});
}
