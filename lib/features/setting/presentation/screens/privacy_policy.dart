import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/core/utils/colors.dart';

import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../bloc/terms_and_condition/terms_and_condition_bloc.dart';

class PrivacyPolicy extends StatefulWidget {
  const PrivacyPolicy({Key? key}) : super(key: key);

  @override
  State<PrivacyPolicy> createState() => _PrivacyPolicyState();
}

class _PrivacyPolicyState extends State<PrivacyPolicy> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    BlocProvider.of<TermsAndConditionsBloc>(context).add(const TermsAndConditionsGetPrivacyPolicy());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: AppLocalizations.of(context)!.privacy_policy,
        ),
        body: BlocConsumer<TermsAndConditionsBloc, TermsAndConditionsState>(listener: (context, state) {
          if (state is TermsAndConditionsGetPrivacyPolicyFaulire) {
            showCustomSnackbar(context, state.message);
          }
        }, builder: (context, state) {
          if (state is TermsAndConditionsGetPrivacyPolicyLoading) {
            return const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation(primaryColor),
              ),
            );
          }
          if (state is TermsAndConditionsGetPrivacyPolicySuccess) {
            return Padding(
                padding: const EdgeInsets.all(16.0),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.black.withOpacity(0.2)),
                      borderRadius: const BorderRadius.all(Radius.circular(10))),
                  child: Text(
                    Localizations.localeOf(context).languageCode == "ar"
                        ? state.privacyPolicy?.arabicContent ?? ""
                        : state.privacyPolicy?.content ?? "",
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(color: Colors.black.withOpacity(0.6), fontWeight: FontWeight.normal),
                  ),
                ));
          }
          return const Center(
            child: Text("No terms and conditions found"),
          );
        }));
  }
}
