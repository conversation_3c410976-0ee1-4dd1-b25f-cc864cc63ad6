import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';

class DateAndDistance extends StatefulWidget {
  const DateAndDistance({super.key});

  @override
  State<DateAndDistance> createState() => _DateAndDistanceState();
}

class _DateAndDistanceState extends State<DateAndDistance> {
  String groupValue = '';
  final border = const BorderSide(width: 2, color: primaryColor);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.date_and_distances,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(border: Border(bottom: border)),
                  child: Text(
                    AppLocalizations.of(context)!.date_and_time,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const Gap(),
            buildOption(
                AppLocalizations.of(context)!.hour_time_24,
                SvgPicture.asset(
                  clockIcon,
                )),
            const Gap(),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(border: Border(bottom: border)),
                  child: Text(
                    AppLocalizations.of(context)!.distances,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const Gap(),
            buildOption(
                AppLocalizations.of(context)!.kilometers,
                SvgPicture.asset(
                  calendarIcon,
                )),
            const Divider(),
            buildOption(
                AppLocalizations.of(context)!.miles,
                SvgPicture.asset(
                  calendarTickIcon,
                )),
          ],
        ),
      ),
    );
  }

  Widget buildOption(String title, SvgPicture icon) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            icon,
            const Gap(
              w: 20,
            ),
            Text(
              title,
              style: const TextStyle(fontSize: 20),
            )
          ],
        ),
        Radio<String>(
          value: title,
          groupValue: groupValue,
          onChanged: (value) => setState(() => groupValue = value!),
        ),
      ],
    );
  }
}
