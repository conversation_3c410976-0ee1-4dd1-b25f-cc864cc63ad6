import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/utils/colors.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class NotificationSetting extends StatefulWidget {
  const NotificationSetting({super.key});

  @override
  State<NotificationSetting> createState() => _NotificationSettingState();
}

class _NotificationSettingState extends State<NotificationSetting> {
  List checkList = [false, false, false];

  List items = [
    "General Notificaiton",
    "Sound",
    "Vibrate",
  ];

  @override
  Widget build(BuildContext context) {
    items = [
       AppLocalizations.of(context)!.general_notification,
        AppLocalizations.of(context)!.sound,
         AppLocalizations.of(context)!.vibrate
    
  ];
    return Scaffold(
        appBar: CustomAppBar(
          onBackButtonPressed: () {
            context.go(path.home);
          },
          title:  AppLocalizations.of(context)!.notification,
        ),
        body: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            Expanded(
              child: ListView.separated(
                separatorBuilder: (BuildContext context, int index) =>
                    const Divider(),
                itemCount: items.length,
                itemBuilder: (BuildContext context, int index) {
                  return ListTile(
                    title: Text(items[index]),
                    onTap: () => setState(() {
                      checkList[index] = !checkList[index];
                    }),
                    trailing: Switch(
                        trackOutlineColor: WidgetStateColor.resolveWith(
                            (states) => checkList[index]
                                ? primaryColor
                                : grey.withOpacity(0.3)),
                        trackColor: WidgetStateColor.resolveWith((states) =>
                            checkList[index]
                                ? primaryColor
                                : grey.withOpacity(0.3)),
                        value: checkList[index],
                        onChanged: (bool value) {
                          setState(() {
                            checkList[index] = value;
                          });
                        }),
                  );
                },
              ),
            ),
          ],
        ));
  }
}
