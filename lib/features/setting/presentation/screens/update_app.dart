import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_button.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/links/links_cubit.dart';
import 'package:yalla_gai_driver/features/setting/bloc/app_version/app_version_cubit.dart';

class UpdateAppScreen extends StatelessWidget {
  const UpdateAppScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final watchAppversion = context.watch<AppVersionCubit>();
    final localizations = AppLocalizations.of(context)!;

    return Material(
      child: Visibility(
        visible: watchAppversion.state is AppVersionMustUpdate || watchAppversion.state is AppVersionOptionalUpdate,
        child: Container(
          width: size.width,
          height: size.height,
          color: white,
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(
                child: Container(
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(color: primaryColor, borderRadius: BorderRadius.circular(3.w)),
                  child: SvgPicture.asset(
                    yallaGaiBlackLogo,
                    width: 150,
                    height: 150,
                    color: white, // Replace with your image path
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              Gap(h: 6.h),
              Text(
                localizations.update_title,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w600, fontSize: 18.sp),
                textAlign: TextAlign.center,
              ),
              Gap(h: 3.h),
              Text(
                localizations.update_notice,
                style: Theme.of(context)
                    .textTheme
                    .labelMedium!
                    .copyWith(color: Colors.black.withOpacity(0.4), fontWeight: FontWeight.normal),
                textAlign: TextAlign.center,
              ),
              Gap(h: 5.h),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 3.w),
                child: Row(
                  children: [
                    watchAppversion.state is! AppVersionOptionalUpdate
                        ? const SizedBox.shrink()
                        : Expanded(
                            child: CustomButton(
                              buttonText: localizations.remind_me_later.toUpperCase(),
                              borderRadius: 3.w,
                              onPressed: () {
                                context.read<AppVersionCubit>().remindMelater();
                              },
                            ),
                          ),
                    Gap(w: 4.w),
                    Expanded(
                      child: CustomButton(
                        buttonText: localizations.update_now.toUpperCase(),
                        borderRadius: 3.w,
                        onPressed: () async {
                          final platform = Platform.operatingSystem;
                          final link =
                              context.read<LinksCubit>().links.where((element) => element.type == platform).firstOrNull;
                          if (link != null) launchUrlString(link.link);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
