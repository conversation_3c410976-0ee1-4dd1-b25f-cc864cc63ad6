import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/links/links_cubit.dart';
import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';

import '../../../../core/utils/images.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class RulesAndTerms extends StatelessWidget {
  const RulesAndTerms({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final List<SettingsItem> items = [
      SettingsItem(
        icon: privacyIcon,
        name: AppLocalizations.of(context)!.privacy_policy,
        onTap: () {
          final link = context
              .read<LinksCubit>()
              .links
              .where((element) => element.type == "privacy_policy")
              .firstOrNull;
          if (link != null) launchUrlString(link.link);
          /*context.push(path.privacyPolicy);*/
        },
      ),
      SettingsItem(
        icon: termsIcon,
        name: AppLocalizations.of(context)!.terms_conditions,
        onTap: () {
          final link = context
              .read<LinksCubit>()
              .links
              .where((element) => element.type == "terms_conditions")
              .firstOrNull;
          if (link != null) launchUrlString(link.link);
          /*context.push(path.termsAndConditions);*/
        },
      ),
    ];

    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.settings,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: ListView.separated(
                separatorBuilder: (BuildContext context, int index) => const Divider(),
                itemCount: items.length,
                itemBuilder: (BuildContext context, int index) {
                  return ListTile(
                    leading: SvgPicture.asset(items[index].icon),
                    title: Text(items[index].name),
                    trailing: const Icon(
                      Icons.arrow_forward_ios,
                      size: 12,
                    ),
                    onTap: items[index].onTap,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SettingsItem {
  final String icon;
  final String name;
  final VoidCallback onTap;

  const SettingsItem({
    required this.icon,
    required this.name,
    required this.onTap,
  });
}
