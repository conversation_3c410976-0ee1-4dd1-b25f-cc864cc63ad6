import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/features/authentication/model/language.dart';

import '../../../../app_locale.dart';
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../authentication/bloc/language/language_bloc.dart';

class ChangeLanguage extends StatefulWidget {
  const ChangeLanguage({super.key});

  @override
  State<ChangeLanguage>  createState() => _ChangeLanguageState();
}

class _ChangeLanguageState extends State<ChangeLanguage> {
  String groupValue = 'English';
  final border = const BorderSide(width: 2, color: primaryColor);
  late SharedPreferences preferences;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    BlocProvider.of<LanaguageBloc>(context).add(const LanguageGet());
    init();
  }

  init() async {
    preferences = await SharedPreferences.getInstance();
    String languageCode = preferences.getString('languageCode').toString();

    if (languageCode == "ar") {
      groupValue = "Arabic";
    } else {
      groupValue = "English";
    }
    setState(() {});
  }

  List<LanguageData>? languageData;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.language,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: <Widget>[
            BlocConsumer<LanaguageBloc, LanguageState>(
                builder: (context, state) => Container(),
                listener: (context, state) {
                  if (state is LanguageGetSuccess) {
                    languageData = state.language;
                  }
                  if (state is LanguageGetFaulire) {
                    languageData = null;
                  }
                }),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  decoration: BoxDecoration(border: Border(bottom: border)),
                  child: Text(
                    AppLocalizations.of(context)!.suggested,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
            const Gap(),
            buildOption("English"),
            const Divider(),
            buildOption("Arabic"),
            const Divider(),
          ],
        ),
      ),
    );
  }

  Widget buildOption(String title) {
    return InkWell(
      onTap: () async {
        setState(() => groupValue = title);
        if (title == "English") {
          final provider = Provider.of<AppLocale>(context, listen: false);
          provider.setLocale(const Locale('en'));
          preferences.setString("languageCode", 'en');
          if (languageData == null) {
            final lang = languageData?.firstWhere((element) => element.language?.toLowerCase() == "english");
            BlocProvider.of<LanaguageBloc>(context).add(LanguageUpdate(languageId: lang?.id ?? "1"));
          } else {
            BlocProvider.of<LanaguageBloc>(context).add(const LanguageUpdate(languageId: "1"));
          }
        } else {
          final provider = Provider.of<AppLocale>(context, listen: false);
          provider.setLocale(const Locale('ar'));
          preferences.setString("languageCode", 'ar');
          if (languageData == null) {
            final lang = languageData?.firstWhere((element) => element.language?.toLowerCase() == "arabic");
            BlocProvider.of<LanaguageBloc>(context).add(LanguageUpdate(languageId: lang?.id ?? "2"));
          } else {
            BlocProvider.of<LanaguageBloc>(context).add(const LanguageUpdate(languageId: "2"));
          }
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            title,
            style: const TextStyle(fontSize: 20),
          ),
          Radio<String>(
              value: title,
              groupValue: groupValue,
              onChanged: (value) {
                setState(() => groupValue = title);
                if (title == "English") {
                  final provider = Provider.of<AppLocale>(context, listen: false);
                  provider.setLocale(const Locale('en'));
                  preferences.setString("languageCode", 'en');
                  if (languageData == null) {
                    final lang = languageData?.firstWhere((element) => element.language?.toLowerCase() == "english");
                    BlocProvider.of<LanaguageBloc>(context).add(LanguageUpdate(languageId: lang?.id ?? "1"));
                  } else {
                    BlocProvider.of<LanaguageBloc>(context).add(const LanguageUpdate(languageId: "1"));
                  }
                } else {
                  final provider = Provider.of<AppLocale>(context, listen: false);
                  provider.setLocale(const Locale('ar'));
                  preferences.setString("languageCode", 'ar');
                  if (languageData == null) {
                    final lang = languageData?.firstWhere((element) => element.language?.toLowerCase() == "arabic");
                    BlocProvider.of<LanaguageBloc>(context).add(LanguageUpdate(languageId: lang?.id ?? "2"));
                  } else {
                    BlocProvider.of<LanaguageBloc>(context).add(const LanguageUpdate(languageId: "2"));
                  }
                }
              }),
        ],
      ),
    );
  }
}
