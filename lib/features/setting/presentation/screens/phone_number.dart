import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/dialog_with_image_builder.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/utils/colors.dart';
import '../../../account_type/domain/entity/device_info.dart';

class ChangePhoneNumber extends StatefulWidget {
  const ChangePhoneNumber({
    Key? key,
    // required this.rideRequest,
    // required this.rideOffer,
  }) : super(key: key);

  @override
  _ChangePhoneNumberState createState() => _ChangePhoneNumberState();
}

class _ChangePhoneNumberState extends State<ChangePhoneNumber> {
  String _phoneEditingController = "";
  bool checkBoxValue = false;
  bool isPhoneValid = false;

  @override
  void initState() {
    _getDeviceId();
    super.initState();
  }

  String deviceId = '';

  _getDeviceId() async {
    deviceId = await getDeviceId();
    print("Device id: $deviceId");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.phone_number,
      ),
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) async {
          if (state is AuthChangePhoneNumberFaulire) {
            showCustomSnackbar(context, state.message);
          }

          if (state is AuthChangePhoneNumberOtpSentSuccess) {
            context.push(path.otp, extra: {"phoneNumber": _phoneEditingController, "isFromChangePhoneNumber": true});
          }
          if (state is AuthChangePhoneNumberSuccess) {
            if (state.isSuccess) {
              dialogWithImageBuilder(
                  onPressed: () {
                    context.go(path.home);
                  },
                  showButtonInside: true,
                  context: context,
                  image: checkedImage,
                  successText: AppLocalizations.of(context)!.successfully_changed_phone_number,
                  buttonText: AppLocalizations.of(context)!.okay);
            }
          }
        },
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(left: 32, right: 32),
                  child: Column(
                    children: [
                      Text(
                        AppLocalizations.of(context)!.change_phone_number,
                        style: Theme.of(context).textTheme.displayMedium!.copyWith(color: Colors.black, fontSize: 24),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),
                      Text(AppLocalizations.of(context)!.account_linkage,
                          textAlign: TextAlign.center,
                          style:
                              Theme.of(context).textTheme.labelMedium!.copyWith(color: Colors.black.withOpacity(0.2))),
                    ],
                  ),
                ),
                const SizedBox(height: 20),
                SizedBox(
                  width: 85.w,
                  child: IntlPhoneField(
                    languageCode: Localizations.localeOf(context).languageCode,
                    pickerDialogStyle: PickerDialogStyle(
                      searchFieldInputDecoration: InputDecoration(
                        hintText: Utils.appText(
                          context: context,
                          text: "Search Country",
                          arabicText: "بحث البلد",
                        ),
                      ),
                    ),
                    textAlignVertical: TextAlignVertical.center,
                    decoration: InputDecoration(
                      focusColor: primaryColor,
                      border: const UnderlineInputBorder(),
                      label: Text(AppLocalizations.of(context)!.mobile_number,
                          style: Theme.of(context).textTheme.labelSmall),
                    ),
                    initialCountryCode: 'JO',
                    onCountryChanged: (value) {
                      phoneNumber.countryCode = value.fullCountryCode;
                      setState(() {});
                    },
                    flagsButtonPadding: const EdgeInsets.all(4),
                    dropdownIconPosition: IconPosition.trailing,
                    flagsButtonMargin: const EdgeInsets.only(right: 20, bottom: 0),
                    dropdownDecoration: BoxDecoration(
                      border: Border.all(color: black.withOpacity(0.4)),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    onChanged: (phone) {
                      // Handle phone number changes

                      setState(() {
                        try {
                          isPhoneValid = phone.isValidNumber();
                        } catch (e) {}

                        _phoneEditingController = phone.completeNumber;
                      });
                      phoneNumber = phone;
                    },
                  ),
                ),
                const SizedBox(height: 20),
                BlocConsumer<AuthBloc, AuthState>(listener: (context, state) {
                  if (state is AuthSendOtpFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                  if (state is AuthSendOtpSuccess) {
                    context.push(path.otp, extra: {"phoneNumber": _phoneEditingController});
                  }
                }, builder: (context, state) {
                  return CustomButton(
                    isLoading: state is AuthChangePhoneNumberLoading ? true : false,
                    onPressed: onContinuePressed,

                    // dialogWithImageBuilder(
                    //     context: context,
                    //     image: checkedImage,
                    //     successText: AppLocalizations.of(context)!
                    //         .successfully_changed_phone_number,
                    //     buttonText: AppLocalizations.of(context)!.okay);

                    borderRadius: 10,
                    width: MediaQuery.of(context).size.width * 0.6,
                    buttonText: AppLocalizations.of(context)!.change,
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  PhoneNumber phoneNumber = PhoneNumber(countryCode: "", countryISOCode: "", number: "");

  onContinuePressed() {
    if (!isPhoneValid) {
      return;
    }
    if (phoneNumber.number.characters.first == "0") {
      phoneNumber.number = phoneNumber.number.substring(1);
    }
    _phoneEditingController = phoneNumber.completeNumber;
    if (!_phoneEditingController.contains("+")) {
      _phoneEditingController = "+$_phoneEditingController";
    }

    BlocProvider.of<AuthBloc>(context).add(AuthChangePhoneNumber(phoneNumber: _phoneEditingController));
  }
}
