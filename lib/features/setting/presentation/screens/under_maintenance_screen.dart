import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/features/setting/bloc/app_version/app_version_cubit.dart';

class UnderMaintenanceScreen extends StatelessWidget {
  const UnderMaintenanceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final watchAppversion = context.watch<AppVersionCubit>();
    final localizations = AppLocalizations.of(context)!;

    return Visibility(
      visible: watchAppversion.state is AppVersionUnderMaintenance,
      child: Material(
        child: Container(
          width: size.width,
          height: size.height,
          color: white,
          alignment: Alignment.center,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Center(
                child: Container(
                  padding: EdgeInsets.all(4.w),
                  decoration: BoxDecoration(color: primaryColor, borderRadius: BorderRadius.circular(3.w)),
                  child: Image.asset(
                    underMaintenance,
                    width: 150,
                    height: 150,
                    color: white, // Replace with your image path
                    fit: BoxFit.contain,
                  ),
                ),
              ),
              Gap(h: 6.h),
              Text(
                localizations.app_name,
                style: Theme.of(context).textTheme.titleLarge!.copyWith(fontWeight: FontWeight.w600, fontSize: 18.sp),
                textAlign: TextAlign.center,
              ),
              Gap(h: 3.h),
              Text(
                localizations.under_maintenance_line1,
                style: Theme.of(context)
                    .textTheme
                    .labelMedium!
                    .copyWith(color: Colors.black.withOpacity(0.4), fontWeight: FontWeight.normal),
                textAlign: TextAlign.center,
              ),
              Text(
                localizations.under_maintenance_line2,
                style: Theme.of(context)
                    .textTheme
                    .labelMedium!
                    .copyWith(color: Colors.black.withOpacity(0.4), fontWeight: FontWeight.normal),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
