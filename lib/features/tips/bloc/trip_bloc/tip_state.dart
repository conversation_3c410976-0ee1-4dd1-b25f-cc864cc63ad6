part of 'tip_bloc.dart';

abstract class TipState extends Equatable {
  const TipState();

  @override
  List<Object> get props => [];
}

final class TipInitial extends TipState {}

final class TipGetLoading extends TipState {}

final class TipGetSuccess extends TipState {
  final List<Tip>? tips;
 
  const TipGetSuccess(  {required this.tips});
}

final class TipGetFaulire extends TipState {
  final String message;
  const TipGetFaulire({required this.message});
}
