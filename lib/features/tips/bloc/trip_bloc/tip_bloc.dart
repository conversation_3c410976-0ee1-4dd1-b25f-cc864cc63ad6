import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:http/http.dart' as http;
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

import '../../model/tip.dart';
import '../../repository/tip_repository.dart';

part 'tip_event.dart';
part 'tip_state.dart';

class TipBloc extends Bloc<TipEvent, TipState> {
  TipRepository tipRepository = TipRepository();

  TipBloc() : super(TipInitial()) {
    ////// Charge Account Driver
    on<TipGetTips>((event, emit) async {
      try {
        emit(TipGetLoading());
        List<Tip>? tips = await tipRepository.getTips();

        emit(TipGetSuccess(
            tips: tips
                ?.where((e) =>
                    e.countryId.toString() == globalDriverProfile.user?.countryId.toString() &&
                    e.roleId.toString() == globalDriverProfile.user?.roleId.toString())
                .toList()));
      } catch (e) {
        emit(TipGetFaulire(message: e.toString()));
      }
    });
  }
}
