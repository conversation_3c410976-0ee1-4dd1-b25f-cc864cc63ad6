import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/features/tips/bloc/trip_bloc/tip_bloc.dart';
import 'package:yalla_gai_driver/features/tips/presentation/widgets/tips_card.dart';

class Tips extends StatefulWidget {
  const Tips({super.key});

  @override
  State<Tips> createState() => _TipsState();
}

class _TipsState extends State<Tips> {
  @override
  void initState() {
    super.initState();
    BlocProvider.of<TipBloc>(context).add(const TipGetTips());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.tips),
      body: SingleChildScrollView(
        child: Column(
          children: [
            BlocConsumer<TipBloc, TipState>(listener: (context, state) {
              if (state is TipGetFaulire) {
                showCustomSnackbar(context, state.message);
              }
            }, builder: (context, state) {
              if (state is TipGetLoading) {
                return Container(
                  margin: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * .35),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation(primaryColor),
                    ),
                  ),
                );
              }
              if (state is TipGetSuccess) {
                return Padding(
                  padding: const EdgeInsets.all(20),
                  child: ListView.separated(
                    separatorBuilder: (context, index) =>
                        const SizedBox(height: 20),
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.tips?.length ?? 0,
                    itemBuilder: (context, index) {
                      return TipsCard(
                        title: Utils.appText(
                            context: context,
                            text: state.tips?[index].title ?? "",
                            arabicText: state.tips?[index].arTitle ?? ""),
                        content: Utils.appText(
                            context: context,
                            text: state.tips?[index].description ?? '',
                            arabicText: state.tips?[index].arDescription ?? ''),
                      );
                    },
                  ),
                );
              }
              return Container(
                margin: EdgeInsets.only(
                    top: MediaQuery.of(context).size.height * .35),
                child: const Center(
                  child: Text("No tip found"),
                ),
              );
            }),
          ],
        ),
      ),
    );
  }
}
