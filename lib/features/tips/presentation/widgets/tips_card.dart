import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/features/tips/presentation/widgets/collabsible.dart';

class TipsCard extends StatefulWidget {
  final String title;
  final String content;

  const TipsCard({Key? key, required this.title, required this.content})
      : super(key: key);

  @override
  State<TipsCard> createState() => _TipsCardState();
}

class _TipsCardState extends State<TipsCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: const Color.fromARGB(10, 14, 59, 65),
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(10.0),
          topRight: Radius.circular(10.0),
        ),
      ),
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(10, 15, 10, 15),
      child: Collapsible(
        maxHeight: 1000,
        title: widget.title,
        child: Padding(
          padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  child: Text(widget.content),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
