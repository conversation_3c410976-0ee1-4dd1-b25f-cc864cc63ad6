import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';

class Collapsible extends StatefulWidget {
  final String title;
  final Widget child;

  const Collapsible(
      {super.key,
      required this.title,
      required this.child,
      required int maxHeight});

  @override
  State<Collapsible> createState() => _CollapsibleState();
}

class _CollapsibleState extends State<Collapsible> {
  bool _isExpanded = false;

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: _toggleExpanded,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  SvgPicture.asset(tipsIcon),
                  const Gap(w: 20),
                  SizedBox(
                    width: 65.w,
                    child: Text(
                      widget.title,
                      softWrap: true,
                    ),
                  ),
                ],
              ),
              Icon(_isExpanded
                  ? Icons.keyboard_arrow_down
                  : Icons.keyboard_arrow_right),
            ],
          ),
        ),
        if (_isExpanded) widget.child,
      ],
    );
  }
}
