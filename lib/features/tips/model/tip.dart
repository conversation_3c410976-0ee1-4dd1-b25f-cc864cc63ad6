class Tip {
  String? id;
  String? roleId;
  String? countryId;
  String? title;
  String? description;
  String? arTitle;
  String? arDescription;

  Tip(
      {this.id,
      this.roleId,
      this.title,
      this.description,
      this.arDescription,
      this.countryId,
      this.arTitle});

  factory Tip.fromJson(Map<String, dynamic> json) {
    return Tip(
      id: json['id']?.toString(),
      roleId: json['role_id']?.toString(),
      title: json['title']?.toString(),
      description: json['description']?.toString(),
      arTitle: json['title_ar']?.toString(),
      arDescription: json['description_ar']?.toString(),
      countryId: json['country_id']?.toString(),
    );
  }
}
