import 'dart:convert';

import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/tips/model/tip.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';

class TipRepository {
  final _httpClient = HttpApiClient();

  TipRepository();

  Future<List<Tip>?> getTips() async {
    String roleId = LocalStorage.instance.roleId ?? "";
    var response = await _httpClient.get(EndPoints.getTips(roleId));
    Map<String, dynamic> responseData = jsonDecode(response.body);

    if (responseData['status'] == true) {
      if (responseData["data"] == null) {
        return null;
      }
      return (responseData["data"] as List).map((e) => Tip.fromJson(e)).toList();
    } else {
      final errors = responseData["message"];
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }
}
