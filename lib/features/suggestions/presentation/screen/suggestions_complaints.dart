import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/features/suggestions/bloc/trip_bloc/suggestion_bloc.dart';

import '../../../../core/shared_widgets/dialog_with_image_builder.dart';
import '../../../../core/utils/images.dart';

class SuggestionsPage extends StatefulWidget {
  const SuggestionsPage({super.key});

  @override
  SuggestionsPageState createState() => SuggestionsPageState();
}

class SuggestionsPageState extends State<SuggestionsPage> {
  FilePickerResult? compliantFile;
  FilePickerResult? suggestionFile;
  final suggesstionController = TextEditingController();
  final complaintsController = TextEditingController();
  bool compliantPicked = false;
  bool suggestionPicked = false;

  Future<void> _pickFile({required bool forCompliant}) async {
    FilePickerResult? result = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      withData: true,
      allowedExtensions: ['svg', 'jpg', 'png', 'jpeg'],
    );

    if (result != null) {
      if (forCompliant) {
        setState(() {
          compliantFile = result;
          compliantPicked = true;
        });
      } else {
        setState(() {
          suggestionFile = result;
          suggestionPicked = true;
        });
      }
      // TODO: File Upload/Submit
    } else {
      //TODO: File Picking Canceled
    }
  }

  @override
  void initState() {
    Future.delayed(Duration.zero, () {
      selectedOption = 0;
    });
    super.initState();
  }

  var selectedOption = 0;

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);
    final formKey = GlobalKey<FormState>();
    return BlocListener<SuggestionBloc, SuggestionState>(
      listener: (context, state) {
        if (state is SuggestionStoreFaulire) {
          showCustomSnackbar(context, state.message);
        }

        if (state is SuggestionStoreSuccess) {
          dialogWithImageBuilder(
            context: context,
            image: banknote,
            successText: AppLocalizations.of(context)!.suggestions_complaints_sent_successfully,
            buttonText: AppLocalizations.of(context)!.okay,
          );
          /*ScaffoldMessenger.of(context)
              .showSnackBar(const SnackBar(backgroundColor: primaryColor, content: Center(child: Text("Submitted"))));*/
          suggesstionController.clear();
          complaintsController.clear();
          suggestionFile = null;
          compliantFile = null;
          setState(() {});
        }
      },
      child: AppScaffold(
        isLoading: context.watch<SuggestionBloc>().state is SuggestionStoreLoading,
        appBar: CustomAppBar(title: AppLocalizations.of(context)!.suggestions_complaints),
        body: DefaultTabController(
          length: 2,
          child: SingleChildScrollView(
            child: Padding(
              padding: EdgeInsets.all(8.w),
              child: Form(
                key: formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: primaryColor),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TabBar(
                        dividerColor: Colors.transparent,
                        indicatorSize: TabBarIndicatorSize.tab,
                        indicator: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          color: primaryColor,
                        ),
                        labelColor: Colors.white,
                        unselectedLabelColor: Colors.black,
                        labelStyle: Theme.of(context)
                            .textTheme
                            .bodyLarge!
                            .copyWith(color: Colors.black, fontSize: 20, fontWeight: FontWeight.w700),
                        unselectedLabelStyle:
                            Theme.of(context).textTheme.bodyLarge!.copyWith(color: Colors.black, fontSize: 20),
                        onTap: (value) {
                          setState(() {
                            selectedOption = value;
                          });
                        },
                        tabs: [
                          Tab(text: AppLocalizations.of(context)!.suggestions),
                          Tab(text: AppLocalizations.of(context)!.complaints),
                        ],
                      ),
                    ),
                    SizedBox(height: size.height * 0.04),
                    Visibility(
                      visible: selectedOption == 0,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextFormField(
                            maxLines: 10,
                            controller: suggesstionController,
                            // validator: TextfieldValidators.nameValidator,
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context)!.enter_suggestion,
                              hintStyle: Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    color: Colors.grey,
                                  ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 1.5.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: primaryColorDark.withOpacity(0.1),
                            ),
                            width: 100.w,
                            child: InkWell(
                              onTap: () {
                                _pickFile(forCompliant: false);
                              },
                              child: Row(
                                children: [
                                  const Icon(Icons.file_present_rounded),
                                  Gap(
                                    w: 1.w,
                                  ),
                                  Expanded(
                                    child: Text(
                                      suggestionPicked
                                          ? suggestionFile?.files[0].name ?? ""
                                          : Utils.appText(
                                              context: context,
                                              text: "Attach any picture",
                                              arabicText: "إرفاق أي صورة",
                                            ),
                                      style: const TextStyle(overflow: TextOverflow.ellipsis),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: selectedOption == 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextFormField(
                            maxLines: 10,
                            controller: complaintsController,
                            // validator: TextfieldValidators.nameValidator,
                            decoration: InputDecoration(
                              hintText: AppLocalizations.of(context)!.enter_complaint,
                              hintStyle: Theme.of(context).textTheme.bodyLarge!.copyWith(
                                    color: Colors.grey,
                                  ),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(10),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 1.5.h),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              color: primaryColorDark.withOpacity(0.1),
                            ),
                            width: 100.w,
                            child: InkWell(
                              onTap: () {
                                _pickFile(forCompliant: true);
                              },
                              child: Row(
                                children: [
                                  const Icon(Icons.file_present_rounded),
                                  Gap(
                                    w: 1.w,
                                  ),
                                  Expanded(
                                    child: Text(
                                      compliantPicked
                                          ? compliantFile?.files[0].name ?? ""
                                          : Utils.appText(
                                              context: context,
                                              text: "Attach any picture",
                                              arabicText: "إرفاق أي صورة",
                                            ),
                                      style: const TextStyle(overflow: TextOverflow.ellipsis),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Gap(h: 3.h),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            formKey.currentState!.validate();
                            send(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            width: 60.w,
                            padding: EdgeInsets.symmetric(vertical: 1.5.h),
                            decoration: BoxDecoration(borderRadius: BorderRadius.circular(10), color: primaryColor),
                            child: Text(
                              AppLocalizations.of(context)!.send,
                              style: const TextStyle(color: white),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void send(BuildContext context) {
    File? file;

    if (selectedOption == 0) {
      if (suggesstionController.text.trim() == "") {
        showCustomSnackbar(context, "Fill in your suggestion");
        return;
      }
      if (suggestionFile != null) {
        file = File(suggestionFile!.files.first.path!);
      }

      context
          .read<SuggestionBloc>()
          .add(SuggestionStore(message: suggesstionController.text, type: "suggestion", file: file));
      return;
    } else {
      if (complaintsController.text.trim() == "") {
        showCustomSnackbar(context, "Fill in your complaint");
        return;
      }
      if (compliantFile != null) {
        file = File(compliantFile!.files.first.path!);
      }
      context
          .read<SuggestionBloc>()
          .add(SuggestionStore(message: complaintsController.text, type: "complaint", file: file));
      return;
    }
  }
}
