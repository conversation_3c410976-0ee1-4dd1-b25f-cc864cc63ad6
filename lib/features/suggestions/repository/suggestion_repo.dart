import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';

class SuggestionRepository {
  final _httpClient = HttpApiClient();

  Future storeSuggestionAndComplent(String message, File? file, String type) async {
    var request = MultipartRequest('POST', EndPoints.suggestionComplaint());

    request.fields.addAll({'message': message, 'type': type});
    request.fields['_method'] = "post";
    if (file != null) {
      request.files.add(await MultipartFile.fromPath('file', file.path));
    }

    final response = await _httpClient.send(request);
    var responseData = await response.stream.toBytes();
    Map<String, dynamic> responseBody = jsonDecode(String.fromCharCodes(responseData));

    if (responseBody["status"] == false) {
      final errors = responseBody["message"];
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
    return;
  }
}
