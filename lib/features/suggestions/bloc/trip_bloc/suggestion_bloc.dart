import 'dart:io';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../repository/suggestion_repo.dart';

part 'suggestion_event.dart';
part 'suggestion_state.dart';

class SuggestionBloc extends Bloc<SuggestionEvent, SuggestionState> {
  SuggestionRepository suggestionRepository = SuggestionRepository();
  SuggestionBloc() : super(SuggestionInitial()) {
    //// Charge Account Driver
    on<SuggestionStore>((event, emit) async {
      try {
        emit(SuggestionStoreLoading());
        await suggestionRepository.storeSuggestionAndComplent(
            event.message, event.file, event.type);
        emit(const SuggestionStoreSuccess());
      } catch (e) {
        emit(SuggestionStoreFaulire(message: e.toString()));
      }
    });
  }
}
