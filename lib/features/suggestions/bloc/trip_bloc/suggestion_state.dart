part of 'suggestion_bloc.dart';

abstract class SuggestionState extends Equatable {
  const SuggestionState();

  @override
  List<Object> get props => [];
}

final class SuggestionInitial extends SuggestionState {}

final class SuggestionStoreLoading extends SuggestionState {}

final class SuggestionStoreSuccess extends SuggestionState {
  const SuggestionStoreSuccess();
}

final class SuggestionStoreFaulire extends SuggestionState {
  final String message;
  const SuggestionStoreFaulire({required this.message});
}
