import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/utils/colors.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';

class Tasks extends StatefulWidget {
  const Tasks({super.key});

  @override
  State<Tasks> createState() => _TasksState();
}

class _TasksState extends State<Tasks> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.tasks,
        onBackButtonPressed: () {
          context.go(path.home);
        },
      ),
      body: Column(
        children: [
          const Divider(
            indent: 20,
            endIndent: 20,
            thickness: 0.1,
            color: Color.fromARGB(255, 56, 56, 56),
          ),
          Stack(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 23,
                  vertical: 27,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    width: .7,
                    color: const Color.fromRGBO(138, 138, 138, 1),
                  ),
                ),
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 12,
                      ),
                      child: Text("${AppLocalizations.of(context)!.task}1",
                          style: Theme.of(context).textTheme.bodyLarge),
                    ),
                    const SizedBox(
                      width: 250,
                      child: Divider(
                        indent: 12,
                        thickness: 0.1,
                        color: Color.fromARGB(255, 56, 56, 56),
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                     Text(

                      AppLocalizations.of(context)!.complete_10_trips_reward,
                      textAlign: TextAlign.center,
                      style:
                          const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    const Stack(
                      alignment: Alignment.center,
                      children: [
                        LinearProgressIndicator(
                          backgroundColor:
                              Color.fromARGB(255, 225, 225, 225),
                          value: 0.6,
                          minHeight: 12,
                          // borderRadius: BorderRadius.circular(22),
                          color: Color.fromRGBO(0, 128, 0, 1),
                        ),
                         Text(
                          '60%',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                right: 0,
                top: 13,
                child: Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 23,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(7),
                      topRight: Radius.circular(7),
                      bottomLeft: Radius.circular(7),
                      bottomRight: Radius.circular(0),
                    ),
                    border: Border.all(
                      width: .7,
                      color: const Color.fromRGBO(138, 138, 138, 1),
                    ),
                  ),
                  child:  Text(
                   AppLocalizations.of(context)!.reward,
                    style: const TextStyle(
                      color: Colors.grey,
                    ),
                  ),
                ),
              )
            ],
          ),
          Stack(
            children: [
              Container(
                margin: const EdgeInsets.symmetric(
                  horizontal: 23,
                  vertical: 27,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    width: .7,
                    color: const Color.fromRGBO(138, 138, 138, 1),
                  ),
                ),
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 12,
                      ),
                      child: Text("${AppLocalizations.of(context)!.task}2",
                          style: Theme.of(context).textTheme.bodyLarge),
                    ),
                    const SizedBox(
                      width: 250,
                      child: Divider(
                        indent: 12,
                        thickness: 0.1,
                        color: Color.fromARGB(255, 56, 56, 56),
                      ),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                     Text(
                      AppLocalizations.of(context)!.complete_10_trips_reward,
                      textAlign: TextAlign.center,
                      style:
                          const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    const Stack(
                      alignment: Alignment.center,
                      children: [
                        LinearProgressIndicator(
                          backgroundColor: white,
                          value: 1,
                          minHeight: 12,
                          color: primaryColor,
                        ),
                        Text(
                          '100%',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                        )
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                right: 0,
                top: 13,
                child: Container(
                  margin: const EdgeInsets.symmetric(
                    horizontal: 23,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: const Color.fromRGBO(0, 128, 0, 1),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(7),
                      topRight: Radius.circular(7),
                      bottomLeft: Radius.circular(7),
                      bottomRight: Radius.circular(0),
                    ),
                    border: Border.all(
                      width: .7,
                      color: const Color.fromRGBO(138, 138, 138, 1),
                    ),
                  ),
                  child:  Text(
                    AppLocalizations.of(context)!.reward,
                    style: const TextStyle(
                      color: Colors.white,
                      // backgroundColor: Color.fromRGBO(0, 128, 0, 1),
                    ),
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
