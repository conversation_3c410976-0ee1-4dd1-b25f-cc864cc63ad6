import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/utils/colors.dart';
import '../../../booking/presentation/screens/pick_on_map.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class AddAddress extends StatefulWidget {
  const AddAddress({super.key});

  @override
  State<AddAddress> createState() => _AddAddressState();
}

class _AddAddressState extends State<AddAddress> {
  var locationNameController = TextEditingController();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.address,
        onBackButtonPressed: () {
          context.go(path.home);
        },
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Stack(children: [
              Container(
                margin: const EdgeInsets.all(22),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: const [
                    BoxShadow(
                      color: Color.fromARGB(255, 214, 213, 213),
                      spreadRadius: 2, // Spread radius
                      blurRadius: 5, // Blur radius
                      offset: Offset(0, 3),
                      blurStyle: BlurStyle.normal,
                    ),
                  ],
                  borderRadius: BorderRadius.circular(15),
                ),
                padding: const EdgeInsets.fromLTRB(
                  16,
                  10,
                  16,
                  80,
                ),
                child: Column(
                  children: [
                    const SizedBox(
                      height: 15,
                    ),
                     Text(
                     AppLocalizations.of(context)!.address_info,
                      style: const TextStyle(
                        fontWeight: FontWeight.w600,
                        fontFamily: "Plus Jakarta Sans",
                        fontSize: 21,
                      ),
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    const Divider(
                      thickness: 0.1,
                      color: Color.fromARGB(255, 56, 56, 56),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 32,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(
                            height: 15,
                          ),
                           Text(
                            AppLocalizations.of(context)!.name,
                            style: const TextStyle(
                              fontFamily: "Plus Jakarta Sans",
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(
                            height: 15,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: const Color.fromARGB(255, 221, 220, 220),
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: TextField(
                              decoration: InputDecoration(
                                  hintText: AppLocalizations.of(context)!.enter_location_name,
                                  hintStyle:
                                      Theme.of(context).textTheme.bodySmall,
                                  border: InputBorder.none),
                            ),
                          ),
                          const SizedBox(
                            height: 15,
                          ),
                           Text(
                          AppLocalizations.of(context)!.address_details,
                            style: const TextStyle(
                              fontFamily: "Plus Jakarta Sans",
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(
                            height: 15,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8),
                            decoration: BoxDecoration(
                              border: Border.all(
                                color: const Color.fromARGB(255, 221, 220, 220),
                              ),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: TextField(
                              decoration: InputDecoration(
                                  hintText:AppLocalizations.of(context)!.enter_address_detail,
                                  hintStyle:
                                      Theme.of(context).textTheme.bodySmall,
                                  border: InputBorder.none),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 35,
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(
                        horizontal: 15,
                      ),
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(
                          width: 1,
                          color: const Color.fromARGB(255, 221, 220, 220),
                        ),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 4,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              color: primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(52),
                            ),
                            child: const Icon(
                              Icons.home,
                            ),
                          ),
                          const SizedBox(
                            width: 15,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 4,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              color: primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(52),
                            ),
                            child: const Icon(
                              Icons.apartment,
                            ),
                          ),
                          const SizedBox(
                            width: 15,
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              vertical: 4,
                              horizontal: 16,
                            ),
                            decoration: BoxDecoration(
                              color: primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(52),
                            ),
                            child: const Icon(
                              Icons.work,
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
              Positioned(
                  bottom: 8,
                  left: 25.w,
                  child: CustomButton(
                      buttonText:AppLocalizations.of(context)!.add_address,
                      borderRadius: 12,
                      width: MediaQuery.sizeOf(context).width - 50.w,
                      onPressed: () {
                        context.pop();
                      }))
            ]),
            const SizedBox(
              height: 76,
            ),
            IntrinsicWidth(
              child: OutlinedButton(
                style: OutlinedButton.styleFrom(
                  backgroundColor: primaryColor,
                  foregroundColor: white,
                ).copyWith(
                  shape: WidgetStatePropertyAll<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12.0),
                    ),
                  ),
                ),
                onPressed: () async {
                  await Navigator.push(
                    context,
                    MaterialPageRoute(builder: (context) {
                      return MapPicker(
                        locationNameController: locationNameController,
                        // initialLocation:
                        //     const LatLng(31.9497, 35.9320),
                      );
                    }),
                  );
                },
                child:  Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 10,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: white,
                        size: 38,
                      ),
                      const SizedBox(
                        width: 12,
                      ),
                      Text(
                       AppLocalizations.of(context)!.select_on_map,
                        style: const TextStyle(
                          fontSize: 18,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
