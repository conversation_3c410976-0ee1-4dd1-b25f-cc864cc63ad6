import 'package:flutter/material.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class Notfication extends StatefulWidget {
  const Notfication({super.key});

  @override
  State<Notfication> createState() => _NotficationState();
}

class _NotficationState extends State<Notfication> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Center(
          child: Text(
            AppLocalizations.of(context)!.notification,
            style: const TextStyle(color: Colors.black),
          ),
        ),
        automaticallyImplyLeading: false,
        leading: IconButton(
          onPressed: () {},
          icon: const Icon(
            Icons.arrow_back_ios,
            color: Colors.black,
            size: 22,
          ),
        ),
      ),
      body: Container(
        margin: const EdgeInsets.symmetric(
          horizontal: 32,
        ),
        child: Column(children: [
          const SizedBox(
            height: 12,
          ),
          const Divider(
            thickness: 0.1,
            color: Color.fromARGB(255, 56, 56, 56),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(AppLocalizations.of(context)!.general_notification),
              Switch(
                trackOutlineColor: const WidgetStatePropertyAll(Colors.white),
                thumbColor: const WidgetStatePropertyAll(Colors.white),
                activeColor: const Color.fromRGBO(0, 128, 0, 1),
                inactiveTrackColor: const Color.fromARGB(255, 225, 225, 225),
                value: false,
                onChanged: (bool value) {},
              ),
            ],
          ),
          const Divider(
            thickness: 0.1,
            color: Color.fromARGB(255, 56, 56, 56),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(AppLocalizations.of(context)!.sound),
              Switch(
                trackOutlineColor: const WidgetStatePropertyAll(Colors.white),
                thumbColor: const WidgetStatePropertyAll(Colors.white),
                activeColor: const Color.fromRGBO(0, 128, 0, 1),
                inactiveTrackColor: const Color.fromARGB(255, 225, 225, 225),
                value: true,
                onChanged: (bool value) {},
              ),
            ],
          ),
          const Divider(
            thickness: 0.1,
            color: Color.fromARGB(255, 56, 56, 56),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(AppLocalizations.of(context)!.vibrate),
              Switch(
                trackOutlineColor: const WidgetStatePropertyAll(Colors.white),
                thumbColor: const WidgetStatePropertyAll(Colors.white),
                activeColor: const Color.fromRGBO(0, 128, 0, 1),
                inactiveTrackColor: const Color.fromARGB(255, 225, 225, 225),
                value: true,
                onChanged: (bool value) {},
              ),
            ],
          ),
          const Divider(
            thickness: 0.1,
            color: Color.fromARGB(255, 56, 56, 56),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(AppLocalizations.of(context)!.general_notification),
              Switch(
                trackOutlineColor: const WidgetStatePropertyAll(Colors.white),
                thumbColor: const WidgetStatePropertyAll(Colors.white),
                activeColor: const Color.fromRGBO(0, 128, 0, 1),
                inactiveTrackColor: const Color.fromARGB(255, 225, 225, 225),
                value: true,
                onChanged: (bool value) {},
              ),
            ],
          ),
          const Divider(
            thickness: 0.1,
            color: Color.fromARGB(255, 56, 56, 56),
          ),
        ]),
      ),
    );
  }
}
