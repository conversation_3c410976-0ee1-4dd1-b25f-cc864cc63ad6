import 'dart:io';

import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_textfield.dart';
import '../../../../core/utils/colors.dart';

class ProfileFill extends StatefulWidget {
  const ProfileFill({Key? key}) : super(key: key);

  @override
  State<ProfileFill>  createState() => _ProfileFillState();
}

class _ProfileFillState extends State<ProfileFill> {
  String leftSquareArrow = "assets/images/arrow-square-left.svg";
  String personIcon = "assets/images/profile.svg";

  var firstNameTextEditingController = TextEditingController();
  var lastNameTextEditingController = TextEditingController();
  var emailTextEditingController = TextEditingController();
  var passwordTextEditingController = TextEditingController();
  var confirmPasswordTextEditingController = TextEditingController();
  File? image;

  final picker = ImagePicker();

  Future<void> _getImage() async {
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);

    setState(() {
      if (pickedFile != null) {
        image = File(pickedFile.path);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.fill_your_profile,
        onBackButtonPressed: () {
          context.go(path.home);
        },
      ),
      body: SingleChildScrollView(
        child: SafeArea(
          child: Column(
            children: [
              const SizedBox(height: 20),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Stack(
                          children: [
                            Container(
                              width: 140,
                              height: 140,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: black.withOpacity(0.2),
                                  width: 2,
                                ),
                              ),
                              child: CircleAvatar(
                                maxRadius: 80,
                                backgroundColor: Colors.white,
                                child: image == null
                                    ? InkWell(
                                        customBorder: const CircleBorder(),
                                        onTap: _getImage,
                                        child: SvgPicture.asset(
                                          personIcon,
                                          height: 70,
                                          width: 80,
                                        ),
                                      )
                                    : CircleAvatar(
                                        backgroundColor: white,
                                        maxRadius: 80,
                                        backgroundImage: FileImage(image!),
                                      ),
                              ),
                            ),
                            Positioned(
                              bottom: 12,
                              right: 15,
                              child: InkWell(
                                onTap: _getImage,
                                child: const SizedBox(
                                  width: 25,
                                  height: 35,
                                  child: Center(
                                    child: Icon(Icons.photo_camera, size: 40),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 20),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 60),
                child: Column(
                  children: [
                    CustomTextField(
                      width: 90.w,
                      borderRadius: 4,
                      labelText: AppLocalizations.of(context)!.first_name,
                      textEditingController: firstNameTextEditingController,
                      validator: (value) => null,
                    ),
                    const SizedBox(height: 12),
                    CustomTextField(
                      width: 90.w,
                      borderRadius: 4,
                      labelText: AppLocalizations.of(context)!.last_name,
                      textEditingController: lastNameTextEditingController,
                      validator: (value) => null,
                    ),
                    const SizedBox(height: 12),
                    CustomTextField(
                      width: 90.w,
                      borderRadius: 4,
                      labelText: AppLocalizations.of(context)!.email,
                      textEditingController: emailTextEditingController,
                      validator: (value) => null,
                    ),
                    const SizedBox(height: 12),
                    CustomTextField(
                      isPassword: true,
                      width: 90.w,
                      borderRadius: 4,
                      labelText: AppLocalizations.of(context)!.password,
                      textEditingController: passwordTextEditingController,
                      validator: (value) => null,
                    ),
                    const SizedBox(height: 12),
                    CustomTextField(
                      isPassword: true,
                      width: 90.w,
                      borderRadius: 4,
                      labelText: AppLocalizations.of(context)!.confirm_password,
                      textEditingController: confirmPasswordTextEditingController,
                      validator: (value) => null,
                    ),
                    const SizedBox(
                      height: 12,
                    )
                  ],
                ),
              ),
              const SizedBox(
                height: 32,
              ),
              CustomButton(
                borderRadius: 12,
                buttonText: AppLocalizations.of(context)!.update,
                onPressed: () {
                  context.pop();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
