import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';

import '../../../../core/constants/end_points.dart';
import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_textfield.dart';
import '../../../../core/shared_widgets/dialog_with_image_builder.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';

class EditProfile extends StatefulWidget {
  const EditProfile({Key? key}) : super(key: key);

  @override
  State<EditProfile> createState() => _EditProfileState();
}

class _EditProfileState extends State<EditProfile> {
  final TextEditingController _arabicLastNameEditingController = TextEditingController();
  final TextEditingController _englishFirstNameEditingController = TextEditingController();
  final TextEditingController _englishLastEditingController = TextEditingController();
  final TextEditingController _arabicFirstNameEditingController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  File? profileImage;
  File? carPhotoOne;
  File? carPhotoTwo;
  File? driverLicenseOne;
  File? driverLicenseTwo;
  File? vehicleLicenseOne;
  File? vehicleLicenseTwo;
  File? certificateOfNonSecurityOne;
  File? certificateOfNonSecurityTwo;
  bool firstTerm = false;
  bool secondTerm = false;

  final picker = ImagePicker();
  int selectedRadio = 1;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<AuthBloc>(context).add(const AuthGetProfile());
    init();
  }

  SharedPreferences? preferences;

  init() async {
    preferences = await SharedPreferences.getInstance();
    setState(() {});
  }

  Future<File?> _getImage() async {
    final pickedFile = await picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        backgroundColor: white,
        centerTitle: true,
        title: Text(AppLocalizations.of(context)!.edit_profile, style: Theme.of(context).textTheme.displayMedium),
      ),
      body: SingleChildScrollView(
        child: Form(
          key: _formKey,
          child: Column(children: [
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
              child: Align(
                alignment: Alignment.centerLeft,
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Stack(
                        children: [
                          Container(
                            width: 140, // adjust the width as per your requirement
                            height: 140, // adjust the height as per your requirement
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: black.withOpacity(0.2),
                                width: 2,
                              ),
                            ),
                            child: CircleAvatar(
                              maxRadius: 100,
                              backgroundColor: Colors.white,
                              child: profileImage == null
                                  ? preferences?.getString("profile_image") != null
                                      ? CachedNetworkImage(
                                          height: double.infinity,
                                          imageUrl: "${AppEnvironment.publicBaseUrl}${preferences?.getString("profile_image") ?? ""}",
                                          fit: BoxFit.cover,
                                          imageBuilder: (context, imageProvider) => Container(
                                            width: 140.0,
                                            height: 140.0,
                                            decoration: BoxDecoration(
                                              shape: BoxShape.circle,
                                              image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
                                            ),
                                          ),
                                          placeholder: (context, url) => const SizedBox(
                                              width: 140,
                                              height: 140,
                                              child: CircularProgressIndicator(
                                                valueColor: AlwaysStoppedAnimation(primaryColor),
                                              )),
                                          errorWidget: (context, url, error) => const Icon(
                                            Icons.person,
                                            size: 50,
                                          ),
                                        )
                                      : SvgPicture.asset(
                                          personIcon,
                                          height: 70,
                                          width: 80,
                                        )
                                  : CircleAvatar(
                                      backgroundColor: white,
                                      maxRadius: 80,
                                      backgroundImage: FileImage(
                                        profileImage!,
                                      )),
                            ),
                          ),
                          Positioned(
                            bottom: 12,
                            right: 0,
                            child: InkWell(
                              onTap: () async {
                                profileImage = await _getImage();
                                setState(() {});
                              },
                              child: Container(
                                  alignment: Alignment.center,
                                  padding: const EdgeInsets.all(4),
                                  decoration: BoxDecoration(color: white, shape: BoxShape.circle, border: Border.all()),
                                  child: const Icon(Icons.edit_outlined, size: 30)),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Gap(h: 3.h),
            CustomTextField(
              width: 65.5.w,
              labelText: AppLocalizations.of(context)!.arabic_first_name,
              validator: (v) => null,
              textEditingController: _arabicFirstNameEditingController,
              borderRadius: 4,
            ),
            const Gap(h: 5),
            CustomTextField(
              width: 65.5.w,
              labelText: AppLocalizations.of(context)!.arabic_last_name,
              validator: (v) => null,
              textEditingController: _arabicLastNameEditingController,
              borderRadius: 4,
            ),
            const Gap(h: 5),
            CustomTextField(
              width: 65.5.w,
              labelText: AppLocalizations.of(context)!.english_first_name,
              validator: (v) => null,
              textEditingController: _englishFirstNameEditingController,
              borderRadius: 4,
            ),
            const Gap(h: 5),
            CustomTextField(
              width: 65.5.w,
              labelText: AppLocalizations.of(context)!.english_last_name,
              validator: (v) => null,
              textEditingController: _englishLastEditingController,
              borderRadius: 4,
            ),
            Gap(h: 4.h),
            Gap(h: 3.h),
            Gap(h: 4.h),
            BlocConsumer<AuthBloc, AuthState>(listener: (context, state) {
              if (state is AuthGetProfileSuccess) {
                _arabicFirstNameEditingController.text = state.driverProfile.user?.arabicFullName ?? "";
                // _arabicLastNameEditingController.text =
                //     state.driverProfile.user?.arabicFirstName ?? "";
                _englishFirstNameEditingController.text = state.driverProfile.user?.fullName ?? "";
                // _englishLastEditingController.text =
                //     state.driverProfile.user?.lastName ?? "";
                setState(() {});
              }
              if (state is AuthUpdateProfileSuccess) {
                dialogWithImageBuilder(
                    onPressed: () {
                      context.go(path.home);
                    },
                    showButtonInside: true,
                    context: context,
                    image: checkedImage,
                    successText: AppLocalizations.of(context)!.account_updated_successfully,
                    buttonText: AppLocalizations.of(context)!.okay);
              }
              if (state is AuthUpdateProfileFaulire) {
                showCustomSnackbar(context, state.message);
              }
            }, builder: (context, state) {
              return CustomButton(
                  isLoading: state is AuthUpdateProfileLoading ? true : false,
                  width: 65.5.w,
                  height: 55,
                  buttonText: AppLocalizations.of(context)!.update,
                  borderRadius: 12,
                  onPressed: () {
                    UpdateProfile updateProfile = UpdateProfile(
                        arabicFirstName: _arabicFirstNameEditingController.text,
                        arabicLastName: _arabicLastNameEditingController.text,
                        firstName: _englishFirstNameEditingController.text,
                        lastName: _englishLastEditingController.text,
                        profileImage: profileImage);
                    AuthEvent event = AuthUpdateProfile(updateProfile: updateProfile);
                    BlocProvider.of<AuthBloc>(context).add(event);
                  });
            }),
            Gap(h: 4.h),
          ]),
        ),
      ),
    );
  }

  Widget photoFields(File? imageFieldOne, File? imageFieldTwo, void Function() onPressedOne, onPressedTwo) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Container(
          width: 150,
          height: 150,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withOpacity(0.5), width: 2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: imageFieldOne == null
              ? Center(
                  child: IconButton(
                    onPressed: onPressedOne,
                    icon: Icon(Icons.upload_file_outlined, size: 50, weight: 1, color: Colors.grey.withOpacity(0.5)),
                  ),
                )
              : Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      image: DecorationImage(
                          fit: BoxFit.cover,
                          image: FileImage(
                            imageFieldOne,
                          )))),
        ),
        Container(
          width: 150,
          height: 150,
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.withOpacity(0.5), width: 2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: imageFieldTwo == null
              ? Center(
                  child: IconButton(
                    onPressed: onPressedTwo,
                    icon: Icon(Icons.upload_file_outlined, size: 50, weight: 1, color: Colors.grey.withOpacity(0.5)),
                  ),
                )
              : Container(
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(12),
                      image: DecorationImage(
                          fit: BoxFit.cover,
                          image: FileImage(
                            imageFieldTwo,
                          )))),
        ),
      ],
    );
  }
}
