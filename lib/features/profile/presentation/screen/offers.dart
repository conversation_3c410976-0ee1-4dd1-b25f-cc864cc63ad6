import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';

import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/utils/colors.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import '../../../wallet/bloc/wallet_bloc/wallet_bloc.dart';

class Offer extends StatefulWidget {
  const Offer({super.key});

  @override
  State<Offer> createState() => _OfferState();
}

class _OfferState extends State<Offer> {
  String leftSquareArrow = "assets/images/arrow-square-left.svg";
  String giftOffer = "assets/images/gift_offer.svg";
  String divider = "assets/images/divider_icon.svg";
  String exchangeIcon = "assets/images/exch_icon.svg";

  // @override
  // void initState() {
  //   // BlocProvider.of<WalletBloc>(context).add(const WalletGetGallet());
  //   // super.initState();
  // }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Scaffold(
      appBar: const CustomAppBar(title: "Offers"),
      body: Column(
        children: [
          const SizedBox(
            height: 32,
          ),
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 25),
            padding: const EdgeInsets.symmetric(
              horizontal: 18,
              vertical: 35,
            ),
            decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(12)),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(giftOffer),
                const SizedBox(
                  width: 60,
                ),
                BlocBuilder<WalletBloc, WalletState>(builder: (context, state) {
                  if (state is WalletGetLoading) {
                    return const SizedBox(
                      height: 15,
                      width: 100,
                      child: LinearProgressIndicator(
                        color: Colors.white,
                      ),
                    );
                  }
                  if (state is WalletGetSuccess) {
                    return Text(
                      state.wallet == null
                          ? "0 ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}"
                          : "${state.wallet?.driverWallet?.balance ?? "0"} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 28,
                      ),
                    );
                  }
                  return Text(
                    "0 ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                    selectionColor: white,
                    style: Theme.of(context)
                        .textTheme
                        .displayLarge!
                        .copyWith(fontSize: 40, fontWeight: FontWeight.w700, color: white),
                  );
                })
              ],
            ),
          ),
          const SizedBox(
            height: 50,
          ),
          OutlinedButton(
            style: OutlinedButton.styleFrom(
              backgroundColor: const Color.fromRGBO(0, 128, 0, 1),
              foregroundColor: Colors.white,
            ).copyWith(
              shape: WidgetStatePropertyAll<RoundedRectangleBorder>(
                RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
              ),
            ),
            onPressed: () {},
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Text(
                AppLocalizations.of(context)!.withdraw_points,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
          const SizedBox(
            height: 70,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
            ),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                AppLocalizations.of(context)!.redeem,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 28,
                ),
              ),
            ),
          ),
          Row(
            children: [
              Stack(
                children: [
                  Container(
                    margin: const EdgeInsets.all(12),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 15,
                      vertical: 30,
                    ),
                    decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(12)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "1000 ${AppLocalizations.of(context)!.points}",
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        ),
                        const SizedBox(
                          height: 40,
                        ),
                        // SvgPicture.asset(divider),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
                          decoration: BoxDecoration(color: primaryColor, borderRadius: BorderRadius.circular(18)),
                          child: Row(children: [
                            SvgPicture.asset(
                              exchangeIcon,
                              width: 12,
                              height: 18,
                            ),
                            const SizedBox(
                              width: 18,
                            ),
                            Text(
                              "1 ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                              style: const TextStyle(
                                color: white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ]),
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    top: 14,
                    left: 12,
                    child: SvgPicture.asset(
                      divider,
                      color: Colors.grey.shade300,
                    ),
                  )
                ],
              ),
              Stack(
                children: [
                  Container(
                    margin: const EdgeInsets.all(12),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 15,
                      vertical: 30,
                    ),
                    decoration: BoxDecoration(color: Colors.grey.shade100, borderRadius: BorderRadius.circular(12)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          "1000 ${AppLocalizations.of(context)!.points}",
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        ),
                        const SizedBox(
                          height: 40,
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
                          decoration: BoxDecoration(color: primaryColor, borderRadius: BorderRadius.circular(18)),
                          child: Row(children: [
                            SvgPicture.asset(
                              exchangeIcon,
                              width: 12,
                              height: 18,
                            ),
                            const SizedBox(
                              width: 18,
                            ),
                            Text(
                              "1 ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                              style: const TextStyle(
                                color: white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ]),
                        )
                      ],
                    ),
                  ),
                  Positioned(
                    top: 14,
                    left: 12,
                    child: SvgPicture.asset(
                      divider,
                      color: Colors.grey.shade300,
                    ),
                  )
                ],
              )
            ],
          )
        ],
      ),
    );
  }
}
