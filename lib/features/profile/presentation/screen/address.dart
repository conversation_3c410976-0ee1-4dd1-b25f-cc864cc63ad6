import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';

import '../../../../core/routes/paths.dart' as path;

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class Address extends StatefulWidget {
  const Address({super.key});

  @override
  State<Address> createState() => _AddressState();
}

class _AddressState extends State<Address> {
  String editIcon = "assets/images/edit_icon.svg";
  String location_icon = "assets/images/location.svg";
  String leftSquareArrow = "assets/images/arrow-square-left.svg";
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        title: Center(
          child: Text(
            AppLocalizations.of(context)!.address,
            style: const TextStyle(color: Colors.black),
          ),
        ),
        automaticallyImplyLeading: false,
        leading: TextButton(
          onPressed: () {},
          child: SvgPicture.asset(leftSquareArrow),
        ),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 30),
          child: Column(
            children: [
              const Divider(
                thickness: 0.1,
                color: Color.fromARGB(255, 56, 56, 56),
              ),
              const SizedBox(
                height: 32,
              ),
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      width: 0.2,
                      color: const Color.fromRGBO(138, 138, 138, 1),
                    )),
                padding: const EdgeInsets.symmetric(
                  vertical: 20,
                  horizontal: 25,
                ),
                child: Row(
                  children: [
                    SvgPicture.asset(location_icon),
                    const SizedBox(
                      width: 22,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.home,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          const Text(
                            'Mia Tayilor, Ring road, Tabaz',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                          )
                        ],
                      ),
                    ),
                    SvgPicture.asset(editIcon)
                  ],
                ),
              ),
              const SizedBox(
                height: 32,
              ),
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      width: 0.2,
                      color: const Color.fromRGBO(138, 138, 138, 1),
                    )),
                padding: const EdgeInsets.symmetric(
                  vertical: 20,
                  horizontal: 25,
                ),
                child: Row(
                  children: [
                    SvgPicture.asset(location_icon),
                    const SizedBox(
                      width: 22,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.college,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          const Text(
                            'Mia Tayilor, Ring road, Tabaz',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                          )
                        ],
                      ),
                    ),
                    SvgPicture.asset(editIcon)
                  ],
                ),
              ),
              const SizedBox(
                height: 32,
              ),
              Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      width: 0.2,
                      color: const Color.fromRGBO(138, 138, 138, 1),
                    )),
                padding: const EdgeInsets.symmetric(
                  vertical: 20,
                  horizontal: 25,
                ),
                child: Row(
                  children: [
                    SvgPicture.asset(location_icon),
                    const SizedBox(
                      width: 22,
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            AppLocalizations.of(context)!.office,
                            style: const TextStyle(
                              fontSize: 15,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(
                            height: 10,
                          ),
                          const Text(
                            'Mia Tayilor, Ring road, Tabaz',
                            style: TextStyle(
                              fontSize: 10,
                              color: Colors.black45,
                            ),
                          )
                        ],
                      ),
                    ),
                    SvgPicture.asset(editIcon)
                  ],
                ),
              ),
              const SizedBox(
                height: 96,
              ),
              OutlinedButton(
                onPressed: () {
                  context.push(path.addAddress);
                },
                style: OutlinedButton.styleFrom(
                    backgroundColor: const Color.fromRGBO(0, 128, 0, 1),
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      vertical: 22,
                      horizontal: 44,
                    )).copyWith(
                  shape: WidgetStatePropertyAll<RoundedRectangleBorder>(
                    RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10.0),
                    ),
                  ),
                ),
                child: Text(AppLocalizations.of(context)!.add_new_address),
              )
            ],
          ),
        ),
      ),
    );
  }
}
