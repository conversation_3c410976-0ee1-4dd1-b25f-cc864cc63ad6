class AccountTypeModel {
  String? id;
  String? name;
  String? arabicName;
  String? incentives;
  String? offers;
  String? priorityOfRequests;
  String? commission;
  String? iconImage;
  String? color;
  String? minRating;
  String? countryId;
  String? cityId;
  dynamic status;
  String? createdAt;
  String? updatedAt;

  AccountTypeModel(
      {this.id,
      this.name,
      this.arabicName,
      this.incentives,
      this.offers,
      this.priorityOfRequests,
      this.commission,
      this.iconImage,
      this.color,
      this.minRating,
      this.countryId,
      this.cityId,
      this.status,
      this.createdAt,
      this.updatedAt});

  AccountTypeModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name'];
    arabicName = json['arabic_name'];
    incentives = json['incentives'];
    offers = json['offers'];
    priorityOfRequests = json['priority_of_requests'];
    commission = json['commission'];
    iconImage = json['icon-image'];
    color = json['color'];
    minRating = json['min_rating'];
    countryId = json['country_id']?.toString();
    cityId = json['city_id']?.toString();
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}
