import 'dart:developer';
import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

Future<String> getDeviceId() async {
  var deviceInfo = DeviceInfoPlugin();
  if (Platform.isIOS) {
    // import 'dart:io'
    var iosDeviceInfo = await deviceInfo.iosInfo;
    return iosDeviceInfo.identifierForVendor ?? ""; // unique ID on iOS
  } else if (Platform.isAndroid) {
    var androidDeviceInfo = await deviceInfo.androidInfo;
    log("============================= ${androidDeviceInfo.model}");
    log(androidDeviceInfo.id);
    return "${androidDeviceInfo.model}-${androidDeviceInfo.id}"; // unique ID on Android
  }
  return "";
}
