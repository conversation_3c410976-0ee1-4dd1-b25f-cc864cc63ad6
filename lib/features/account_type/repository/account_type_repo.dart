import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

import '../../../core/constants/end_points.dart';
import '../model/account_tye.dart';

class AccountTypeRepository {
  final _httpClient = HttpApiClient();

  Future getAccountType() async {
    final cityID = globalDriverProfile.user?.city?.id;
    final response = await _httpClient.get(EndPoints.getAccountType(cityId: cityID));
    final responseData = jsonDecode(response.body);

    List<AccountTypeModel> allAcctType = [];
    for (var type in responseData['data']) {
      allAcctType.add(AccountTypeModel.fromJson(type));
    }
    return allAcctType;
  }
}
