part of 'account__type_bloc.dart';

abstract class AccountTypeState extends Equatable {
  const AccountTypeState();

  @override
  List<Object> get props => [];
}

final class AccountTypeInitial extends AccountTypeState {}

final class AccountTypeGetLoading extends AccountTypeState {}

final class AccountTypeGetSuccess extends AccountTypeState {
  final List<AccountTypeModel> accountType;
  const AccountTypeGetSuccess({required this.accountType});
}

final class AccountTypeGetFaulire extends AccountTypeState {
  final String message;
  const AccountTypeGetFaulire({required this.message});
}
