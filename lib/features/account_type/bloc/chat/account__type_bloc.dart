import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:yalla_gai_driver/features/account_type/repository/account_type_repo.dart';

import '../../model/account_tye.dart';

part 'account__type_event.dart';
part 'account__type_state.dart';

class AccountTypeBloc extends Bloc<AccountTypeEvent, AccountTypeState> {
  AccountTypeRepository accountTypeRepository = AccountTypeRepository();
  AccountTypeBloc() : super(AccountTypeInitial()) {
    ////// CounterStartCounter
    on<AccountTypeGet>((event, emit) async {
      try {
        emit(AccountTypeGetLoading());
        List<AccountTypeModel>? accountTypes =
            await accountTypeRepository.getAccountType();
        emit(AccountTypeGetSuccess(accountType: accountTypes!));
      } catch (e) {
        emit(AccountTypeGetFaulire(message: e.toString()));
      }
    });
  }
}
