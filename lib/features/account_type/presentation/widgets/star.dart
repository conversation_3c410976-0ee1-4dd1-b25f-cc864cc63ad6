import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/environment.dart';

class Star extends StatelessWidget {
  final String imageUrl;

  const Star({super.key, required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(30.w),
      child: CachedNetworkImage(
        height: 10.w,
        width: 10.w,
        imageUrl: "${AppEnvironment.publicBaseUrl}$imageUrl",
        fit: BoxFit.fill,
        placeholder: (context, url) => const CircularProgressIndicator(
          value: 2.0,
          valueColor: AlwaysStoppedAnimation(primaryColor),
        ),
        errorWidget: (context, url, error) => Icon(
          Icons.error,
          size: 20.sp,
          color: Colors.red,
        ),
      ),
    );
  }
}
