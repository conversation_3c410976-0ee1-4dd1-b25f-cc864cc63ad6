import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';

class Tick extends StatelessWidget {
  final bool tick;
  const Tick({super.key, required this.tick});
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Container(
        padding: const EdgeInsets.all(4),
        width: 24,
        height: 24,
        decoration: BoxDecoration(
            color: !tick ? const Color(0xFFFF5F5F) : const Color(0xFF007BFE),
            borderRadius: BorderRadius.circular(16)),
        child: Center(
          child: Icon(
            tick ? Icons.check : Icons.close_sharp,
            color: white,
            size: 16,
          ),
        ),
      ),
    );
  }
}
