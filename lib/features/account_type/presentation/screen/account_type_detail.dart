import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/features/account_type/model/account_tye.dart';
import 'package:yalla_gai_driver/features/account_type/presentation/widgets/star.dart';
import 'package:yalla_gai_driver/features/account_type/presentation/widgets/check.dart';

class AccountTypeDetail extends StatefulWidget {
  final List<AccountTypeModel> accountTypeModel;
  final int selectedIndex;
  const AccountTypeDetail(
      {super.key, required this.accountTypeModel, required this.selectedIndex,});

  @override
  AccountTypeDetailState createState() => AccountTypeDetailState();
}

AccountTypeModel? selectedType;

class AccountTypeDetailState extends State<AccountTypeDetail> {
  @override
  void initState() {
    selectedType = widget.accountTypeModel[widget.selectedIndex];
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: CustomAppBar(
          title: AppLocalizations.of(context)!.account_type,
        ),
        body: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              Opacity(
                opacity: 0.7,
                child: InkWell(
                  onTap: () {
                    showGeneralDialog(
                      barrierDismissible: true,
                      barrierLabel: '',
                      barrierColor: Colors.black38,
                      transitionDuration: const Duration(milliseconds: 100),
                      pageBuilder: (ctx, anim1, anim2) => AlertDialog(
                        content: SizedBox(
                          height: 250,
                          width: 800,
                          child: ListView.separated(
                            shrinkWrap: true,
                            separatorBuilder:
                                (BuildContext context, int index) =>
                                    const Divider(),
                            itemCount: widget.accountTypeModel.length,
                            itemBuilder: (BuildContext context, int index) {
                              return ListTile(
                                leading: Star(
                                    imageUrl: widget.accountTypeModel[index]
                                            .iconImage ??
                                        '',),
                                title: Text(
                                  widget.accountTypeModel[index].name ?? '',
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w400,),
                                ),
                                onTap: () {
                                  setState(() {
                                    selectedType =
                                        widget.accountTypeModel[index];
                                  });
                                  context.pop(); // This line closes the dialog
                                },
                              );
                            },
                          ),
                        ),
                      ),
                      transitionBuilder: (ctx, anim1, anim2, child) =>
                          BackdropFilter(
                        filter: ImageFilter.blur(
                            sigmaX: 4 * anim1.value, sigmaY: 4 * anim1.value,),
                        child: FadeTransition(
                          opacity: anim1,
                          child: child,
                        ),
                      ),
                      context: context,
                    );
                  },
                  child: Center(
                    child: Container(
                        padding: const EdgeInsets.all(16.0),
                        width: 60.w,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        child: Row(
                          children: [
                            Star(imageUrl: selectedType?.iconImage ?? ''),
                            const Gap(
                              w: 20,
                            ),
                            Text(selectedType?.name ?? '',
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w300,),),
                            const Expanded(child: SizedBox()),
                            const Icon(Icons.keyboard_arrow_down),
                          ],
                        ),),
                  ),
                ),
              ),
              const Gap(h: 40),
              Column(
                children: [
                  Container(
                    width: 90.w,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                        color: Color(int.tryParse(selectedType?.color ?? '') ?? 0),
                        borderRadius: const BorderRadius.only(
                            topLeft: Radius.circular(10),
                            topRight: Radius.circular(10),),),
                    child: Center(
                        child: Text(
                      Utils.appText(
                          context: context,
                          text: selectedType?.name ?? '',
                          arabicText: selectedType?.arabicName ?? '',),
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 20,
                          fontWeight: FontWeight.w600,
                          color: white,),
                    ),),
                  ),
                  Container(
                    width: 90.w,
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey[200]!),
                        borderRadius: const BorderRadius.only(
                            bottomLeft: Radius.circular(10),
                            bottomRight: Radius.circular(10),),),
                    child: Row(
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              width: 60.w,
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                  border: Border(
                                      bottom:
                                          BorderSide(color: Colors.grey[200]!),
                                      right: BorderSide(
                                          color: Colors.grey[200]!,),),),
                              child: Text(
                                AppLocalizations.of(context)!.incentives,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                        fontSize: 16,
                                        fontWeight: FontWeight.w600,),
                              ),
                            ),
                            Container(
                                width: 60.w,
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            color: Colors.grey[200]!,),
                                        right: BorderSide(
                                            color: Colors.grey[200]!,),),),
                                child: Text(
                                  AppLocalizations.of(context)!.offers,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,),
                                ),),
                            Container(
                                width: 60.w,
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                    border: Border(
                                        bottom: BorderSide(
                                            color: Colors.grey[200]!,),
                                        right: BorderSide(
                                            color: Colors.grey[200]!,),),),
                                child: Text(
                                  AppLocalizations.of(context)!
                                      .priority_of_requests,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                          fontSize: 16,
                                          fontWeight: FontWeight.w600,),
                                ),),
                          ],
                        ),
                        const VerticalDivider(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Tick(
                                tick: selectedType?.incentives == "on"
                                    ? true
                                    : false,),
                            Tick(
                                tick: selectedType?.offers == "on"
                                    ? true
                                    : false,),
                            Tick(
                                tick: selectedType?.priorityOfRequests == "on"
                                    ? true
                                    : false,),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),);
  }
}
