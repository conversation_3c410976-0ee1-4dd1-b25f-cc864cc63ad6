import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/routes/paths.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/account_type/bloc/chat/account__type_bloc.dart';

class AccountTypes extends StatefulWidget {
  const AccountTypes({super.key});

  @override
  State<AccountTypes> createState() => _AccountTypesState();
}

class _AccountTypesState extends State<AccountTypes> {
  @override
  void initState() {
    super.initState();

    BlocProvider.of<AccountTypeBloc>(context).add(const AccountTypeGet());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.account_type),
      body: SingleChildScrollView(
        child: BlocBuilder<AccountTypeBloc, AccountTypeState>(builder: (context, state) {
          if (state is AccountTypeGetLoading) {
            return Container(
              margin: EdgeInsets.only(top: MediaQuery.of(context).size.height * .4),
              child: const Center(
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation(primaryColor),
                ),
              ),
            );
          }

          if (state is AccountTypeGetSuccess) {
            return Container(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  state.accountType.length,
                  (index) {
                    return Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            context.push(accountTypeDetail,
                                extra: {"selectedIndex": index, "account_type": state.accountType});
                          },
                          child: AccountTypeWidget(
                            commission: double.parse(state.accountType[index].commission ?? '0'),
                            minRating: double.parse(state.accountType[index].minRating!),
                            name: Utils.appText(
                              context: context,
                              text: state.accountType[index].name ?? '',
                              arabicText: state.accountType[index].arabicName,
                            ),
                            imageUrl: state.accountType[index].iconImage!,
                            cardColor: state.accountType[index].color!,
                          ),
                        ),
                        const Gap(),
                      ],
                    );
                  },
                ),
              ),
            );
          }
          return const Center();
        }),
      ),
    );
  }
}

class AccountTypeWidget extends StatelessWidget {
  final num? minRating;
  final num? commission;

  final String name, cardColor, imageUrl;

  const AccountTypeWidget(
      {super.key,
      required this.name,
      this.minRating,
      this.commission,
      required this.imageUrl,
      required this.cardColor});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(5.w),
        color: Color(int.tryParse(cardColor) ?? 0).withAlpha(50),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                ClipOval(
                  child: CachedNetworkImage(
                    height: 48,
                    width: 48,
                    imageUrl: "${AppEnvironment.publicBaseUrl}$imageUrl",
                    fit: BoxFit.fill,
                    placeholder: (context, url) => const CircularProgressIndicator(
                      value: 2.0,
                      valueColor: AlwaysStoppedAnimation(primaryColor),
                    ),
                    errorWidget: (context, url, error) => Icon(Icons.error, size: 20.sp, color: Colors.red),
                  ),
                ),
                const Gap(w: 12),
                Text(name,
                    textAlign: TextAlign.start,
                    style:
                        Theme.of(context).textTheme.displayLarge!.copyWith(fontSize: 16, fontWeight: FontWeight.w600))
              ],
            ),
            const Gap(h: 20),
            Table(
              columnWidths: const {
                0: IntrinsicColumnWidth(),
                1: FixedColumnWidth(24),
              },
              children: [
                TableRow(
                  children: [
                    TableCell(child: Text(AppLocalizations.of(context)!.min_rating)),
                    const TableCell(child: Center(child: Text(":"))),
                    TableCell(child: Text("$minRating", style: const TextStyle(fontWeight: FontWeight.w600))),
                  ],
                ),
                TableRow(
                  children: [
                    TableCell(child: Text(AppLocalizations.of(context)!.commission)),
                    const TableCell(child: Center(child: Text(":"))),
                    TableCell(child: Text("$commission", style: const TextStyle(fontWeight: FontWeight.w600))),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
