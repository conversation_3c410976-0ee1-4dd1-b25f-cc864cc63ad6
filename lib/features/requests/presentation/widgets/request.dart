import 'dart:async';

import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/get_polyline_points.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';

import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';

class RequestCard extends StatefulWidget {
  final String requestID;
  final String user;
  final String userPhoto;
  final DateTime date;
  final num fare;
  final num yallaPercentage;
  final num total;
  final num? cost;
  final double startLng;
  final double startLat;
  final double? destLng;
  final double? destLat;
  final String paymentMode;
  final String referenceId;
  final String commissionPaidAmount;
  final String status;
  final Color? statusColor;

  const RequestCard({
    super.key,
    required this.requestID,
    required this.date,
    required this.user,
    required this.userPhoto,
    required this.fare,
    required this.total,
    required this.yallaPercentage,
    required this.destLat,
    required this.destLng,
    required this.startLat,
    required this.startLng,
    required this.paymentMode,
    required this.commissionPaidAmount,
    required this.referenceId,
    required this.cost,
    required this.status,
    required this.statusColor,
  });

  @override
  State<RequestCard> createState() => _RequestCardState();
}

class _RequestCardState extends State<RequestCard> {
  bool showMore = false;
  final Completer<GoogleMapController> _controller = Completer();
  List<LatLng> routePoints = [];

  BitmapDescriptor sourceIcon = BitmapDescriptor.defaultMarker;
  BitmapDescriptor destinationIcon = BitmapDescriptor.defaultMarker;

  @override
  initState() {
    setSourceAndDestinationIcons();

    super.initState();
  }

  void setSourceAndDestinationIcons() async {
    await Future.wait([
      getBitmapDescriptor(locationAMarker, 48).then((value) => sourceIcon = value),
      getBitmapDescriptor(locationBMarker, 48).then((value) => destinationIcon = value),
    ]);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Container(
      width: 90.w,
      padding: const EdgeInsets.all(16),
      decoration: const BoxDecoration(color: primaryColorLight, borderRadius: BorderRadius.all(Radius.circular(10))),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    const Gap(
                      w: 5,
                    ),
                    CircleAvatar(radius: 3.h, backgroundImage: NetworkImage(widget.userPhoto)),
                    const Gap(
                      w: 5,
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.user,
                            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                            overflow: TextOverflow.clip,
                          ),
                          const Gap(
                            h: 5,
                          ),
                          Text(
                            DateFormat('dd MMM yyyy HH:mm:ss').format(widget.date),
                            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                  fontSize: 8,
                                  fontWeight: FontWeight.w600,
                                ),
                            overflow: TextOverflow.clip,
                          ),
                          const Gap(
                            h: 5,
                          ),
                          SelectableText(
                            '#${widget.referenceId}',
                            style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                  fontSize: 8,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              const Gap(w: 5),
              const SizedBox(
                height: 48,
                child: VerticalDivider(),
              ),
              const Gap(w: 5),
              Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')} ${(widget.total / 100).toStringAsFixed(2)}',
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const Gap(h: 5),
                  Text(
                    widget.status,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                          color: widget.statusColor ?? primaryColor,
                        ),
                  ),
                ],
              ),
              const Gap(
                w: 10,
              ),
              InkWell(
                onTap: () {
                  setState(() {
                    showMore = !showMore;
                  });
                },
                child: const Icon(Icons.keyboard_arrow_down_outlined),
              ),
            ],
          ),
          showMore
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Divider(),
                    const Gap(),
                    Center(
                      child: Container(
                        height: 20.h,
                        clipBehavior: Clip.antiAlias,
                        decoration: const BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(10))),
                        child: GoogleMap(
                          myLocationButtonEnabled: false,
                          zoomControlsEnabled: false,
                          compassEnabled: false,
                          myLocationEnabled: false,
                          zoomGesturesEnabled: false,
                          tiltGesturesEnabled: false,
                          rotateGesturesEnabled: false,
                          scrollGesturesEnabled: false,
                          mapToolbarEnabled: false,
                          initialCameraPosition: CameraPosition(
                            target: LatLng(widget.startLat, widget.startLng),
                            zoom: 14.5,
                          ),
                          onMapCreated: (controller) {
                            _controller.complete(controller);
                            if (widget.destLat != null && widget.destLng != null) {
                              final bounds = computeBounds([
                                LatLng(widget.startLat, widget.startLng),
                                LatLng(widget.destLat!, widget.destLng!),
                              ]);
                              controller.animateCamera(CameraUpdate.newLatLngBounds(bounds, 32));
                            } else {
                              controller
                                  .animateCamera(CameraUpdate.newLatLng(LatLng(widget.startLat, widget.startLng)));
                            }
                          },
                          markers: {
                            Marker(
                              markerId: const MarkerId("driver_source"),
                              icon: sourceIcon,
                              position: LatLng(widget.startLat, widget.startLng),
                            ),
                            if (widget.destLat != null && widget.destLng != null)
                              Marker(
                                markerId: const MarkerId("driver_destination"),
                                icon: destinationIcon,
                                position: LatLng(widget.destLat!, widget.destLng!),
                              ),
                          },
                        ),
                      ),
                    ),
                    const Gap(),
                    Text(
                      AppLocalizations.of(context)!.payment_details,
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                            fontSize: 12,
                            fontWeight: FontWeight.w600,
                          ),
                    ),
                    const Divider(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        /* Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(AppLocalizations.of(context)!.fare,
                                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                        fontSize: 10,
                                        fontWeight: FontWeight.w600,
                                      )),
                              Text('${widget.fare} ${AppLocalizations.of(context)!.fare}',
                                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                        fontSize: 12,
                                        fontWeight: FontWeight.w400,
                                      )),
                            ],
                          ),
                          const VerticalDivider(),*/
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.commission,
                              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            Text(
                              '${((double.tryParse(widget.commissionPaidAmount) ?? 0) / 100).toStringAsFixed(2)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}',
                              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                            ),
                          ],
                        ),
                        const VerticalDivider(),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.total,
                              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontSize: 10,
                                    fontWeight: FontWeight.w600,
                                  ),
                            ),
                            Text(
                              '${((widget.cost ?? widget.total) / 100).toStringAsFixed(2)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}',
                              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w400,
                                  ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const Gap(),
                    Center(
                      child: Container(
                        padding: const EdgeInsets.only(top: 8, bottom: 8, left: 16, right: 16),
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(20)),
                          color: primaryColorDark,
                        ),
                        child: Text(
                          '${AppLocalizations.of(context)!.payment_mode} : ${widget.paymentMode}',
                          style: Theme.of(context)
                              .textTheme
                              .bodyMedium!
                              .copyWith(fontSize: 14, fontWeight: FontWeight.w600, color: white),
                        ),
                      ),
                    ),
                  ],
                )
              : Container(),
        ],
      ),
    );
  }
}
