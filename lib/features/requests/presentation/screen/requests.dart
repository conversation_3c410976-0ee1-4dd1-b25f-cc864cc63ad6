import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/constants/end_points.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/requests/presentation/widgets/request.dart';

class Requests extends StatefulWidget {
  const Requests({super.key});

  @override
  State<Requests> createState() => _RequestsState();
}

class _RequestsState extends State<Requests> {
  List<RequestCard> requests = [];

  @override
  void initState() {
    BlocProvider.of<TripBloc>(context).add(const TripGetNewRequest());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.requests),
      body: BlocConsumer<TripBloc, TripState>(listener: (context, state) {
        if (state is TripGetNewRequestSuccess) {
          for (var element in state.rideRequests) {
            requests.add(RequestCard(
              user: Utils.appText(
                  context: context,
                  listen: false,
                  text: element.user?.fullName ?? "",
                  arabicText: element.user?.arabicFullName),
              requestID: element.id ?? "",
              userPhoto: '${AppEnvironment.publicBaseUrl}${element.user?.profileImage}',
              date: element.createdAt ?? DateTime.now(),
              fare: num.parse(element.fare ?? "0"),
              yallaPercentage: 0,
              total: element.cost,
              destLat: element.lastLatLocation ?? element.destinationLatitude,
              destLng: element.lastLongLocation ?? element.destinationLongitude,
              startLat: element.pickupLatitude,
              startLng: element.pickupLongitude,
              paymentMode: element.paymentMethod ?? 'CASH',
              referenceId: element.referenceId ?? '',
              commissionPaidAmount: element.commissionPaidAmount ?? '',
              cost: double.tryParse(element.cost?.toString() ?? ''),
              status: element.status == "4"
                  ? localizations.completed
                  : element.cancelledBy != null
                      ? localizations.cancelled
                      : localizations.in_the_trip,
              statusColor: element.status == "4"
                  ? Colors.green
                  : element.cancelledBy != null
                      ? Colors.red
                      : Colors.orange,
            ));
          }
        }
      }, builder: (context, state) {
        if (state is TripGetNewRequestLoading) {
          return const Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation(primaryColor),
            ),
          );
        }
        return SingleChildScrollView(
          child: Column(
            children: [
              const Gap(),
              Container(
                  width: 100.w,
                  padding: const EdgeInsets.only(left: 32, top: 8, bottom: 8),
                  child: Text(
                    AppLocalizations.of(context)!.previous_passengers,
                    style:
                        Theme.of(context).textTheme.displayLarge!.copyWith(fontSize: 20, fontWeight: FontWeight.bold),
                    textAlign: TextAlign.start,
                  )),
              const Gap(),
              ...requests
                  .map((request) => Column(
                        children: [request, const Gap()],
                      ))
                  .toList(),
            ],
          ),
        );
      }),
    );
  }
}
