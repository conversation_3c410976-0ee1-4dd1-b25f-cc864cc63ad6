class OffersModel {
  OffersModel({
    required this.id,
    required this.roleId,
    required this.name,
    required this.description,
    required this.arName,
    required this.arDescription,
    required this.icon,
    required this.points,
    required this.price,
    required this.status,
    required this.type,
    required this.cityId,
    required this.createdAt,
    required this.updatedAt,
  });

  final String? id;
  final String? roleId;
  final String? name;
  final String? description;
  final String? arName;
  final String? arDescription;
  final String? icon;
  final String? points;
  final String? price;
  final String? status;
  final String? type;
  final String? cityId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory OffersModel.fromJson(Map<String, dynamic> json) {
    return OffersModel(
      id: json["id"]?.toString(),
      roleId: json["role_id"]?.toString(),
      name: json["name"],
      description: json["description"],
      arName: json["ar_name"],
      arDescription: json["ar_description"],
      icon: json["icon"]?.toString(),
      points: json["points"]?.toString(),
      price: json["price"]?.toString(),
      status: json["status"]?.toString(),
      type: json["type"]?.toString(),
      cityId: json["city_id"]?.toString(),
      createdAt: DateTime.tryParse(json["created_at"] ?? ""),
      updatedAt: DateTime.tryParse(json["updated_at"] ?? ""),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "role_id": roleId,
        "name": name,
        "description": description,
        "ar_name": arName,
        "ar_description": arDescription,
        "icon": icon,
        "points": points,
        "price": price,
        "status": status,
        "type": type,
        "city_id": cityId,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
