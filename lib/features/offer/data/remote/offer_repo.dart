import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/offer/data/model/offers_model.dart';

import '../../../../core/constants/end_points.dart';
import '../../../../core/error/exception.dart';

class OfferRepo {
  final _httpClient = HttpApiClient();

  Future getOffers() async {
    final response = await _httpClient.get(EndPoints.fetchOffersUrl(globalDriverProfile.user?.roleId ?? ''));
    final responseBody = jsonDecode(response.body);
    if (response.isSuccess) {
      List<OffersModel> offers = [];
      for (var offer in responseBody['data']) {
        offers.add(OffersModel.fromJson(offer));
      }
      return offers;
    } else {
      throw ServerException(message: responseBody["message"]);
    }
  }

  Future<bool> redeemOffer(String id) async {
    final response = await _httpClient.get(EndPoints.redeemOffer(id));
    final responseData = jsonDecode(response.body);
    if (response.isSuccess) {
      return true;
    } else {
      throw ServerException(message: responseData["message"]);
    }
  }
}
