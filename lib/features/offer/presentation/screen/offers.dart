import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/features/notification/presentation/screen/new_notification.dart';
import 'package:yalla_gai_driver/features/offer/presentation/bloc/cubit/offer_cubit.dart';
import 'package:yalla_gai_driver/features/offer/presentation/widgets/offer.dart';

import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';

class Offers extends StatefulWidget {
  const Offers({Key? key}) : super(key: key);

  @override
  OffersState createState() => OffersState();
}

class OffersState extends State<Offers> {
  SharedPreferences? preferences;

  @override
  void initState() {
    context.read<OfferCubit>().getOffers();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    final allOffer = context.watch<OfferCubit>().allOffers;

    return BlocConsumer<OfferCubit, OfferState>(
      listener: (context, state) {
        if (state is OfferRedeemedError) {
          showCustomSnackbar(
            context,
            Utils.appText(
              context: context,
              text: "You have an offer available now.",
              arabicText: "لديك عرض متاح الان",
            ),
          );
        }
        if (state is OfferRedeemedState) {
          final offer = context.read<OfferCubit>().redeemedOffer;
          context.read<AuthBloc>().add(const AuthGetProfile());
          showGeneralDialog(
            barrierDismissible: true,
            barrierLabel: '',
            barrierColor: Colors.black38,
            transitionDuration: const Duration(milliseconds: 500),
            pageBuilder: (ctx, anim1, anim2) => AlertDialog(
              content: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SvgPicture.asset(
                      width: 30.w,
                      height: 20.h,
                      offerTick,
                      color: primaryColorDark,
                    ),
                    const Divider(),
                    Text(
                      '${AppLocalizations.of(context)!.congratulations_you_are_now_a} ${offer?.name} ${AppLocalizations.of(context)!.plan_user}',
                      style:
                          Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 16, fontWeight: FontWeight.w600),
                      textAlign: TextAlign.center,
                    ),
                    const Gap(),
                  ],
                ),
              ),
            ),
            transitionBuilder: (ctx, anim1, anim2, child) => BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 4 * anim1.value, sigmaY: 4 * anim1.value),
              child: FadeTransition(
                opacity: anim1,
                child: child,
              ),
            ),
            context: context,
          );
        }
      },
      builder: (context, state) {
        return AppScaffold(
          isLoading: state is OfferRedeemLoadingState || state is OfferLoadingState,
          appBar: CustomAppBar(
            title: AppLocalizations.of(context)!.offers,
          ),
          body: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: EdgeInsets.all(3.w),
                  child: Stack(
                    children: [
                      SvgPicture.asset(height: 236, width: 158, offerBalanceCard),
                      Positioned(
                        width: 80.w,
                        top: 8.8.h,
                        // left: 8.w,
                        left: preferences?.getString("languageCode") != "ar" ? 8.w : null,
                        right: preferences?.getString("languageCode") != "ar" ? null : 8.w,
                        child: Row(
                          children: [
                            SvgPicture.asset(
                              walletIcon,
                              color: Colors.white,
                            ),
                            const Gap(w: 10),
                            Text(
                              AppLocalizations.of(context)!.balance,
                              selectionColor: white,
                              style: Theme.of(context)
                                  .textTheme
                                  .displayLarge!
                                  .copyWith(color: Colors.white, fontSize: 20, fontWeight: FontWeight.w700),
                            ),
                          ],
                        ),
                      ),
                      Positioned(
                        bottom: 8.h,
                        // left: 8.w,
                        width: 80.w,
                        left: preferences?.getString("languageCode") != "ar" ? 8.w : null,
                        right: preferences?.getString("languageCode") != "ar" ? null : 8.w,
                        child: Text(
                          "${((double.tryParse(driver?.balance.toString() ?? '') ?? 0) / 100).toStringAsFixed(2)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                          selectionColor: black,
                          style: Theme.of(context)
                              .textTheme
                              .displayLarge!
                              .copyWith(fontSize: 40, color: Colors.white, fontWeight: FontWeight.w700),
                        ),
                      ),
                    ],
                  ),
                ),
                const Gap(h: 10),
                Container(
                  height: 8.h,
                  decoration: const BoxDecoration(color: primaryColorLight),
                  child: Center(
                    child: Text(AppLocalizations.of(context)!.use_our_products),
                  ),
                ),
                const Gap(),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 3.w),
                  child: Text(
                    AppLocalizations.of(context)!.our_products,
                    style:
                        Theme.of(context).textTheme.displayLarge!.copyWith(fontSize: 24, fontWeight: FontWeight.w600),
                    textAlign: TextAlign.start,
                  ),
                ),
                const Gap(h: 20),
                if (allOffer.isEmpty)
                  EmptyItemBox()
                else
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Wrap(
                      alignment: WrapAlignment.center,
                      runAlignment: WrapAlignment.center,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      runSpacing: 16,
                      spacing: 16,
                      children: List.generate(
                        allOffer.length,
                        (index) => Offer(
                          id: allOffer[index].id!,
                          desc: Utils.appText(
                            context: context,
                            text: allOffer[index].description ?? '',
                            arabicText: allOffer[index].arDescription,
                          ),
                          price: allOffer[index].price ?? '',
                          offerType: Utils.appText(
                            context: context,
                            text: allOffer[index].name ?? '',
                            arabicText: allOffer[index].arName,
                          ),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
