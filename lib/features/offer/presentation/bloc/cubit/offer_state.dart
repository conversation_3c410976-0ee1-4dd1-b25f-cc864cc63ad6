part of 'offer_cubit.dart';

sealed class OfferState extends Equatable {
  const OfferState();

  @override
  List<Object> get props => [];
}

final class OfferInitial extends OfferState {}

final class OfferLoadingState extends OfferState {}

final class OfferFetchedState extends OfferState {}

final class OfferErrorState extends OfferState {}

final class OfferRedeemLoadingState extends OfferState {}

final class OfferRedeemedState extends OfferState {}

final class OfferRedeemedError extends OfferState {}
