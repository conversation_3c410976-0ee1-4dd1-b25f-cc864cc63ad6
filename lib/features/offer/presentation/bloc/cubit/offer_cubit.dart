import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:yalla_gai_driver/features/offer/data/remote/offer_repo.dart';

import '../../../data/model/offers_model.dart';

part 'offer_state.dart';

class OfferCubit extends Cubit<OfferState> {
  OfferRepo offerRepo;

  OfferCubit(this.offerRepo) : super(OfferInitial());
  List<OffersModel> allOffers = [];

  getOffers() async {
    emit(OfferLoadingState());
    try {
      allOffers = await offerRepo.getOffers();
      emit(OfferFetchedState());
    } catch (e) {
      log(e.toString());
      emit(OfferErrorState());
    }
  }

  OffersModel? redeemedOffer;

  redeemOffer(String id) async {
    emit(OfferRedeemLoadingState());
    try {
      bool success = await offerRepo.redeemOffer(id);
      if (success) {
        redeemedOffer = allOffers.firstWhere((element) => element.id == id);
        emit(OfferRedeemedState());
      } else {
        emit(OfferRedeemedError());
      }
    } catch (e) {
      log(e.toString());
      emit(OfferRedeemedError());
    }
  }
}
