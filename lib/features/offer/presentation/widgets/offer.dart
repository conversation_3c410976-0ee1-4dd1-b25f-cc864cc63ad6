import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/features/offer/presentation/bloc/cubit/offer_cubit.dart';

import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';

class Offer extends StatelessWidget {
  final String id;
  final String price;
  final String offerType;
  final String desc;

  const Offer({super.key, required this.id, required this.price, required this.offerType, required this.desc});

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Stack(
          children: [
            Container(
              height: 220,
              width: 160,
              decoration: const BoxDecoration(
                  color: primaryColorDark,
                  borderRadius: BorderRadius.only(topLeft: Radius.circular(10), topRight: Radius.circular(10))),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      margin: EdgeInsets.only(top: 4.h),
                      child: Text(
                        offerType,
                        textAlign: TextAlign.center,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(fontSize: 20, color: white, fontWeight: FontWeight.w700),
                      ),
                    ),
                    const Gap(h: 3),
                    Flexible(
                      child: Text(
                        desc,
                        textAlign: TextAlign.center,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(fontSize: 12, color: white, fontWeight: FontWeight.w400),
                      ),
                    ),
                    const Gap(h: 8),
                    SvgPicture.asset(dashedDivider),
                    const Gap(h: 8),
                    Text(
                      '${((double.tryParse(price) ?? 0) / 100).toStringAsFixed(2)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}',
                      selectionColor: white,
                      style: Theme.of(context)
                          .textTheme
                          .displayLarge!
                          .copyWith(fontSize: 26, color: white, fontWeight: FontWeight.w700),
                    )
                  ],
                ),
              ),
            ),
            Positioned(
              top: 0,
              left: 0,
              child: Container(
                padding: const EdgeInsets.all(8),
                width: 70,
                decoration: const BoxDecoration(
                    color: primaryColorLight,
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
                child: Center(
                  child: Text(
                    '10%',
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                ),
              ),
            ),
            Positioned(right: 10, top: 10, child: SvgPicture.asset(offerTick)),
            const Positioned(
              top: 100,
              left: -12,
              child: Icon(Icons.circle, color: white),
            ),
            const Positioned(
              top: 100,
              right: -12,
              child: Icon(Icons.circle, color: white),
            ),
          ],
        ),
        InkWell(
          onTap: () {
            context.read<OfferCubit>().redeemOffer(id);
          },
          child: Container(
            width: 160,
            decoration: const BoxDecoration(
                color: primaryColorLight,
                borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
            child: Center(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  AppLocalizations.of(context)!.buy_now,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
