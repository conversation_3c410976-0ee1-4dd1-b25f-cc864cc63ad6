import 'dart:developer' as developer;
import 'dart:developer';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/phone_url_luncher.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/circle_button_with_icon.dart';
import 'package:yalla_gai_driver/features/supervisor/bloc/chat/neighbourhood_bloc.dart';

import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../model/neigb.dart';

class SupervisorsInfo extends StatefulWidget {
  const SupervisorsInfo({super.key});

  @override
  State<SupervisorsInfo> createState() => _SupervisorsInfoState();
}

class _SupervisorsInfoState extends State<SupervisorsInfo> {
  String location = '';

  @override
  void initState() {
    super.initState();

    BlocProvider.of<NeighbourHoodBloc>(context).add(const NeighbourHoodGet());
  }

  List<SupervisorModel> supervisors = [];

  List<NeighbourHood> neighbourhood = [];
  NeighbourHood? selectedNeighbourHood;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        onBackButtonPressed: () {
          context.pop();
        },
        title: AppLocalizations.of(context)!.supervisors_info,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            BlocBuilder<NeighbourHoodBloc, NeighbourHoodState>(
              builder: (context, state) {
                if (state is NeighbourHoodGetLoading) {
                  return Container(
                    margin: EdgeInsets.only(
                      top: MediaQuery.of(context).size.height * .4,
                    ),
                    child: const CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation(primaryColor),
                    ),
                  );
                }
                if (state is NeighbourHoodGetSuccess) {
                  neighbourhood = state.neighbourhood;
                }

                return neighbourhood.isEmpty
                    ? const Center(child: Text("No Neighborhood found"))
                    : Container(
                        width: 90.w,
                        alignment: Alignment.center,
                        child: CustomDropdown<NeighbourHood>(
                          headerBuilder: (context, selectedItem, enabled) {
                            return Row(
                              children: [
                                const Icon(
                                  Icons.location_on,
                                  color: Colors.grey,
                                ),
                                Text(
                                  Utils.appText(
                                    context: context,
                                    text: selectedItem.name ?? "",
                                    arabicText: selectedItem.arabicName ?? "",
                                  ),
                                ),
                              ],
                            );
                          },
                          hintBuilder: (context, hint, enabled) {
                            return Row(
                              children: [
                                const Icon(
                                  Icons.location_on,
                                  color: Colors.grey,
                                ),
                                Text(hint),
                              ],
                            );
                          },
                          listItemBuilder:
                              (context, item, isSelected, onItemSelect) {
                            return Text(
                              Utils.appText(
                                context: context,
                                text: item.name ?? "",
                                arabicText: item.arabicName ?? "",
                              ),
                              style: TextStyle(
                                fontWeight: isSelected ? FontWeight.w600 : null,
                              ),
                            );
                          },
                          decoration: const CustomDropdownDecoration(
                            closedFillColor: Color.fromARGB(255, 245, 245, 245),
                          ),
                          hintText: Utils.appText(
                            context: context,
                            text: "Select Neighborhood",
                            arabicText: "حدد الحي",
                          ),
                          items: neighbourhood,
                          onChanged: (value) {
                            setState(() {
                              selectedNeighbourHood = value;
                              supervisors =
                                  value?.supervisors ?? <SupervisorModel>[];
                            });
                            developer.log("supervisor${supervisors.length}");

                            setState(() {});
                          },
                        ),
                      );
              },
            ),
            const Gap(
              h: 40,
            ),
            selectedNeighbourHood != null
                ? InkWell(
                    onTap: () {
                      // context.push(home);
                    },
                    child: Supervisors(
                      neighbourhood: Utils.appText(
                        context: context,
                        text: selectedNeighbourHood!.name ?? "",
                        arabicText: selectedNeighbourHood!.arabicName ?? "",
                      ),
                      location: Utils.appText(
                        context: context,
                        text: selectedNeighbourHood!.city?.name ?? "",
                        arabicText:
                            selectedNeighbourHood!.city?.arabicName ?? "",
                      ),
                      supervisors: selectedNeighbourHood!.supervisors
                              ?.where(
                                (element) =>
                                    element.neighbourhoodId ==
                                    selectedNeighbourHood?.id?.toString(),
                              )
                              .toList() ??
                          <SupervisorModel>[],
                    ),
                  )
                : Container(),
          ],
        ),
      ),
    );
  }
}

class Supervisors extends StatelessWidget {
  final String neighbourhood;
  final String location;
  final List<SupervisorModel> supervisors;

  const Supervisors({
    super.key,
    required this.neighbourhood,
    required this.location,
    required this.supervisors,
  });

  @override
  Widget build(BuildContext context) {
    Future<void> launchGoogleMaps({
      required double latitude,
      required double longitude,
      String? label,
    }) async {
      final String googleMapsUrl =
          'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude${label != null ? '&query_place_id=$label' : ''}';

      final Uri uri = Uri.parse(googleMapsUrl);

      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch Google Maps';
      }
    }

    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: Column(
        children: <Widget>[
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 60.w,
                    child: Text(
                      neighbourhood,
                      overflow: TextOverflow.ellipsis,
                      style: Theme.of(context)
                          .textTheme
                          .headlineLarge!
                          .copyWith(fontSize: 16, fontWeight: FontWeight.w600),
                    ),
                  ),
                  Opacity(
                    opacity: 0.6,
                    child: Row(
                      children: [
                        const Icon(Icons.location_on),
                        const Gap(
                          w: 10,
                        ),
                        Text(
                          location,
                          style:
                              Theme.of(context).textTheme.bodyMedium!.copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.w300,
                                  ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
          const Gap(),
          const Divider(),
          const Gap(),
          Column(
            children: supervisors.map((supervisor) {
              return Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          CircleAvatar(
                            radius: 3.h,
                            backgroundImage: NetworkImage(
                              "${AppEnvironment.publicBaseUrl}${supervisor.profileImage}",
                            ),
                          ),
                          const Gap(w: 20),
                          SizedBox(
                            width: 40.w,
                            child: Text(
                              Utils.appText(
                                context: context,
                                text: supervisor.fullName ?? "",
                                arabicText: supervisor.arabicFullName ?? "",
                              ),
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                            ),
                          ),
                        ],
                      ),
                      CircleButton(
                        size: 25,
                        icon: Icons.phone_in_talk_sharp,
                        backgroundColor: primaryColor,
                        onTap: () {
                          callPhoneNumber(supervisor.mobileNumber ?? '');
                        },
                      ),
                      InkWell(
                        onTap: () async {
                          String whatsappUrl = "${supervisor.whatsUpNumber}";
                          if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
                            await launchUrl(Uri.parse(whatsappUrl));
                          } else {
                            log("Could not launch $whatsappUrl");
                          }
                        },
                        child: CircleAvatar(
                          radius: 14,
                          backgroundColor: primaryColor,
                          child: SizedBox(
                            height: 18,
                            width: 20,
                            child: SvgPicture.asset(
                              "assets/images/connect/whatsapp.svg",
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ),
                      if (((supervisor.localLatitude is String &&
                                  double.parse(supervisor.localLatitude) !=
                                      0.0) ||
                              (supervisor.localLatitude != 0.0 &&
                                  supervisor.localLatitude != null)) &&
                          ((supervisor.localLongitude is String &&
                                  double.parse(supervisor.localLongitude) !=
                                      0.0) ||
                              (supervisor.localLongitude != 0.0 &&
                                  supervisor.localLongitude != null)))
                        CircleButton(
                          size: 25,
                          icon: Icons.location_pin,
                          backgroundColor: primaryColor,
                          onTap: () {
                            var latitude = supervisor.localLatitude;
                            var longitude = supervisor.localLongitude;
                            if (latitude is String) {
                              latitude = double.parse(latitude);
                            }
                            if (longitude is String) {
                              longitude = double.parse(longitude);
                            }

                            launchGoogleMaps(
                              latitude: latitude,
                              longitude: longitude,
                            );
                          },
                        ),
                    ],
                  ),
                  const Gap(),
                ],
              );
            }).toList(),
          ),
        ],
      ),
    );
  }
}
