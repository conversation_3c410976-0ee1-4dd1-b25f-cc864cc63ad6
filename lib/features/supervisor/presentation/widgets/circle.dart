import 'dart:math';
import 'package:flutter/material.dart';

class RandomColorCircle extends StatelessWidget {
  final String letter;

  const RandomColorCircle({super.key, required this.letter});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 50,
      height: 50,
      decoration: BoxDecoration(
        color: Colors.primaries[Random().nextInt(Colors.primaries.length)],
        shape: BoxShape.circle,
      ),
      child: Center(
        child: Text(
          letter,
          style: const TextStyle(fontSize: 25, color: Colors.white),
        ),
      ),
    );
  }
}
