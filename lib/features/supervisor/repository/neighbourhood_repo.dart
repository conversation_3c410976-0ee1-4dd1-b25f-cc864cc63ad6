import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';

import '../../../core/constants/end_points.dart';
import '../model/neigb.dart';

class SupervisorRepository {
  final _httpClient = HttpApiClient();

  SupervisorRepository();

  Future<List<NeighbourHood>> getNeighbourhoods() async {
    var response = await _httpClient.get(EndPoints.getNeighbourhood());
    Map<String, dynamic> responseData = jsonDecode(response.body);
    return (responseData['data'] as List).map((e) => NeighbourHood.fromJson(e)).toList();
  }
}
