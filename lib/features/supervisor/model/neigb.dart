// class NeighbourHood {
//   String? id;
//   String? name;
//   String? countryId;
//   String? cityId;
//   City? city;
//   List<User>? users;

//   NeighbourHood({
//     this.id,
//     this.name,
//     this.countryId,
//     this.cityId,
//     this.city,
//     this.users,
//   });

//   factory NeighbourHood.fromJson(Map<String, dynamic> json) {
//     return NeighbourHood(
//       id: json['id']?.toString(),
//       name: json['name']?.toString(),
//       countryId: json['country_id']?.toString(),
//       cityId: json['city_id']?.toString(),
//       city: json['city'] == null ? null : City.fromJson(json['city']),
//       users: json['users'] == null
//           ? null
//           : List<User>.from(json['users'].map((x) => User.fromJson(x))),
//     );
//   }
// }

// class City {
//   String? id;
//   String? userId;
//   String? countryId;
//   String? arabicName;
//   String? name;
//   String? image;
//   Country? country;

//   City({
//     this.id,
//     this.userId,
//     this.countryId,
//     this.arabicName,
//     this.name,
//     this.image,
//     this.country,
//   });

//   factory City.fromJson(Map<String, dynamic> json) {
//     return City(
//       id: json['id']?.toString(),
//       userId: json['user_id']?.toString(),
//       countryId: json['country_id']?.toString(),
//       arabicName: json['arabic_name']?.toString(),
//       name: json['name']?.toString(),
//       image: json['image']?.toString(),
//       country:
//           json['country'] == null ? null : Country.fromJson(json['country']),
//     );
//   }
// }

// class Country {
//   String? id;
//   String? englishCurrencyId;
//   String? arabicCurrencyId;
//   String? internationalKeyId;
//   String? countryFlagId;
//   String? arabicName;
//   String? name;
//   String? createdAt;
//   String? updatedAt;
//   EnglishCurrency? englishCurrency;
//   ArabicCurrency? arabicCurrency;
//   CountryFlag? countryFlag;
//   InternationalKey? internationalKey;

//   Country({
//     this.id,
//     this.englishCurrencyId,
//     this.arabicCurrencyId,
//     this.internationalKeyId,
//     this.countryFlagId,
//     this.arabicName,
//     this.name,
//     this.createdAt,
//     this.updatedAt,
//     this.englishCurrency,
//     this.arabicCurrency,
//     this.countryFlag,
//     this.internationalKey,
//   });

//   factory Country.fromJson(Map<String, dynamic> json) {
//     return Country(
//       id: json['id']?.toString(),
//       englishCurrencyId: json['english_currency_id']?.toString(),
//       arabicCurrencyId: json['arabic_currency_id']?.toString(),
//       internationalKeyId: json['international_key_id']?.toString(),
//       countryFlagId: json['country_flag_id']?.toString(),
//       arabicName: json['arabic_name']?.toString(),
//       name: json['name']?.toString(),
//       createdAt: json['created_at']?.toString(),
//       updatedAt: json['updated_at']?.toString(),
//       englishCurrency: json['english_currency'] == null
//           ? null
//           : EnglishCurrency.fromJson(json['english_currency']),
//       arabicCurrency: json['arabic_currency'] == null
//           ? null
//           : ArabicCurrency.fromJson(json['arabic_currency']),
//       countryFlag: json['country_flag'] == null
//           ? null
//           : CountryFlag.fromJson(json['country_flag']),
//       internationalKey: json['international_key'] == null
//           ? null
//           : InternationalKey.fromJson(json['international_key']),
//     );
//   }
// }

// class EnglishCurrency {
//   String? id;
//   String? name;

//   EnglishCurrency({
//     this.id,
//     this.name,
//   });

//   factory EnglishCurrency.fromJson(Map<String, dynamic> json) {
//     return EnglishCurrency(
//       id: json['id']?.toString(),
//       name: json['name']?.toString(),
//     );
//   }
// }

// class ArabicCurrency {
//   String? id;
//   String? name;

//   ArabicCurrency({
//     this.id,
//     this.name,
//   });

//   factory ArabicCurrency.fromJson(Map<String, dynamic> json) {
//     return ArabicCurrency(
//       id: json['id']?.toString(),
//       name: json['name']?.toString(),
//     );
//   }
// }

// class CountryFlag {
//   String? id;
//   String? countryFlag;

//   CountryFlag({
//     this.id,
//     this.countryFlag,
//   });

//   factory CountryFlag.fromJson(Map<String, dynamic> json) {
//     return CountryFlag(
//       id: json['id']?.toString(),
//       countryFlag: json['country_flag']?.toString(),
//     );
//   }
// }

// class InternationalKey {
//   String? id;
//   String? internationalKey;

//   InternationalKey({
//     this.id,
//     this.internationalKey,
//   });

//   factory InternationalKey.fromJson(Map<String, dynamic> json) {
//     return InternationalKey(
//       id: json['id']?.toString(),
//       internationalKey: json['international_key']?.toString(),
//     );
//   }
// }

// class User {
//   String? id;
//   String? firstName;
//   String? lastName;
//   String? email;
//   String? mobileNumber;
//   String? profileImage;

//   User({
//     this.id,
//     this.firstName,
//     this.lastName,
//     this.email,
//     this.mobileNumber,
//     this.profileImage,
//   });

//   factory User.fromJson(Map<String, dynamic> json) {
//     return User(
//       id: json['id']?.toString(),
//       firstName: json['first_name']?.toString(),
//       lastName: json['last_name']?.toString(),
//       email: json['email']?.toString(),
//       mobileNumber: json['mobile_number']?.toString(),
//       profileImage: json['profile_image']?.toString(),
//     );
//   }
// }
class NeighbourHood {
  String? id;
  String? countryId;
  String? cityId;
  String? name;
  String? arabicName;
  String? createdAt;
  String? updatedAt;
  City? city;
  List<SupervisorModel>? supervisors;

  NeighbourHood(
      {this.id,
      this.countryId,
      this.cityId,
      this.name,
      this.arabicName,
      this.createdAt,
      this.updatedAt,
      this.city,
      this.supervisors});

  NeighbourHood.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    countryId = json['country_id']?.toString();
    cityId = json['city_id']?.toString();
    name = json['name'];
    arabicName = json['arabic_name'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    city = json['city'] != null ? City.fromJson(json['city']) : null;
    if (json['supervisors'] != null) {
      supervisors = <SupervisorModel>[];
      json['supervisors'].forEach((v) {
        supervisors!.add(SupervisorModel.fromJson(v));
      });
    }
  }
}

class City {
  String? id;
  String? countryId;
  String? arabicName;
  String? name;
  String? genericName;
  String? status;
  dynamic image;
  String? createdAt;
  String? updatedAt;

  City(
      {this.id,
      this.countryId,
      this.arabicName,
      this.name,
      this.genericName,
      this.status,
      this.image,
      this.createdAt,
      this.updatedAt});

  City.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    countryId = json['country_id']?.toString();
    arabicName = json['arabic_name'];
    name = json['name'];
    genericName = json['generic_name'];
    status = json['status'];
    image = json['image'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}

class SupervisorModel {
  String? id;
  String? deviceId;
  String? roleId;
  String? cityId;
  String? countryId;
  String? neighbourhoodId;
  String? blockConditionId;
  String? languageId;
  String? distanceId;
  String? email;
  dynamic latitude;
  dynamic longitude;
  dynamic direction;
  dynamic location;
  dynamic otp;
  dynamic radius;
  String? mobileNumber;
  String? fullName;
  String? arabicFullName;
  String? rating;
  String? status;
  String? loginStatus;
  String? activationStatus;
  dynamic reason;
  dynamic gender;
  dynamic couponId;
  dynamic privacyPolicyId;
  dynamic termConditionId;
  dynamic accountTypeId;
  dynamic addressId;
  dynamic emailVerifiedAt;
  dynamic password2;
  String? profileImage;
  dynamic idPhoto;
  dynamic points;
  String? balance;
  dynamic deviceToken;
  dynamic localLatitude;
  dynamic localLongitude;
  String? availibilityStatus;
  String? createdAt;
  String? updatedAt;
  String? whatsUpNumber;

  SupervisorModel(
      {this.id,
      this.deviceId,
      this.roleId,
      this.cityId,
      this.countryId,
      this.neighbourhoodId,
      this.blockConditionId,
      this.languageId,
      this.distanceId,
      this.email,
      this.latitude,
      this.longitude,
      this.direction,
      this.location,
      this.otp,
      this.radius,
      this.mobileNumber,
      this.fullName,
      this.arabicFullName,
      this.rating,
      this.status,
      this.loginStatus,
      this.activationStatus,
      this.reason,
      this.gender,
      this.couponId,
      this.privacyPolicyId,
      this.termConditionId,
      this.accountTypeId,
      this.addressId,
      this.emailVerifiedAt,
      this.password2,
      this.profileImage,
      this.localLatitude,
      this.localLongitude,
      this.idPhoto,
      this.points,
      this.balance,
      this.deviceToken,
      this.availibilityStatus,
      this.createdAt,
      this.updatedAt,
      this.whatsUpNumber});

  SupervisorModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    deviceId = json['device_id']?.toString();
    roleId = json['role_id']?.toString();
    cityId = json['city_id']?.toString();
    countryId = json['country_id']?.toString();
    neighbourhoodId = json['neighbourhood_id']?.toString();
    blockConditionId = json['block_condition_id']?.toString();
    languageId = json['language_id']?.toString();
    distanceId = json['distance_id']?.toString();
    email = json['email'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    direction = json['direction'];
    location = json['location'];
    otp = json['otp'];
    radius = json['radius'];
    mobileNumber = json['mobile_number'];
    fullName = json['full_name'];
    arabicFullName = json['arabic_full_name'];
    rating = json['rating'];
    status = json['status'];
    loginStatus = json['login_status'];
    activationStatus = json['activation_status'];
    reason = json['reason'];
    gender = json['gender'];
    couponId = json['coupon_id']?.toString();
    privacyPolicyId = json['privacy_policy_id']?.toString();
    termConditionId = json['term_condition_id']?.toString();
    accountTypeId = json['account_type_id']?.toString();
    addressId = json['address_id']?.toString();
    emailVerifiedAt = json['email_verified_at'];
    password2 = json['password_2'];
    profileImage = json['profile_image'];
    idPhoto = json['id_photo'];
    points = json['points'];
    balance = json['balance'];
    deviceToken = json['device_token'];
    availibilityStatus = json['availibility_status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    whatsUpNumber = json['whats_up_number'];
    localLatitude = json['local_latitude'];
    localLongitude = json['local_longitude'];
  }
}
