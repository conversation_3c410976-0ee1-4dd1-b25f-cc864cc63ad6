part of 'neighbourhood_bloc.dart';

abstract class NeighbourHoodState extends Equatable {
  const NeighbourHoodState();

  @override
  List<Object> get props => [];
}

final class NeighbourHoodInitial extends NeighbourHoodState {}

final class NeighbourHoodGetLoading extends NeighbourHoodState {}

final class NeighbourHoodGetSuccess extends NeighbourHoodState {
  final List<NeighbourHood> neighbourhood;
  const NeighbourHoodGetSuccess({required this.neighbourhood});
}

final class NeighbourHoodGetFaulire extends NeighbourHoodState {
  final String message;
  const NeighbourHoodGetFaulire({required this.message});
}
