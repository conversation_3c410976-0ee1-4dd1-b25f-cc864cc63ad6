import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/features/supervisor/repository/neighbourhood_repo.dart';

import '../../model/neigb.dart';

part 'neighbourhood_event.dart';
part 'neighbourhood_state.dart';

class NeighbourHoodBloc extends Bloc<NeighbourHoodEvent, NeighbourHoodState> {
  SupervisorRepository supervisorRepository = SupervisorRepository();

  NeighbourHoodBloc() : super(NeighbourHoodInitial()) {
    ////// CounterStartCounter
    on<NeighbourHoodGet>((event, emit) async {
      try {
        emit(NeighbourHoodGetLoading());
        List<NeighbourHood>? neighbourhood = await supervisorRepository.getNeighbourhoods();
        emit(NeighbourHoodGetSuccess(neighbourhood: neighbourhood));
      } catch (e) {
        emit(NeighbourHoodGetFaulire(message: e.toString()));
      }
    });
  }
}
