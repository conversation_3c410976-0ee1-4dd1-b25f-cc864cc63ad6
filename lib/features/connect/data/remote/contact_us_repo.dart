import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/connect/data/remote/contact_us_model.dart';

import '../../../../core/constants/end_points.dart';
import '../../../../core/error/exception.dart';

class ContactUsRepo {
  final _httpClient = HttpApiClient();

  Future contactUs() async {
    final response = await _httpClient.get(EndPoints.contactUrl);
    final responseData = jsonDecode(response.body);

    if (response.isSuccess) {
      List<ContactUsModel> contact = [];
      for (var us in responseData['data']) {
        contact.add(ContactUsModel.fromJson(us));
      }

      return contact
          .where((e) =>
              e.roleId.toString() == globalDriverProfile.user?.roleId.toString() &&
              e.countryId.toString() == globalDriverProfile.user?.countryId.toString())
          .toList();
    } else {
      throw ServerException(message: responseData['message']);
    }
  }
}
