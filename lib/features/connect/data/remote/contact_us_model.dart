class ContactUsModel {
  String? id;
  String? name;
  String? email;
  String? roleId;
  String? countryId;
  String? link;
  String? image;
  String? status;
  String? createdAt;
  String? updatedAt;

  ContactUsModel(
      {this.id,
      this.name,
      this.email,
      this.roleId,
      this.countryId,
      this.link,
      this.image,
      this.status,
      this.createdAt,
      this.updatedAt});

  ContactUsModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name'];
    email = json['email'];
    roleId = json['role_id']?.toString();
    countryId = json['country_id']?.toString();
    link = json['link'];
    image = json['image'];
    status = json['status'];
  }
}
