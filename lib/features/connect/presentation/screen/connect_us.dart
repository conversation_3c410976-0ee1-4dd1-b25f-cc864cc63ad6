import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_app_bar.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/connect/presentation/bloc/cubit/contactus_cubit.dart';

class ConnectUs extends StatelessWidget {
  const ConnectUs({super.key});

  @override
  Widget build(BuildContext context) {
    final allcontact =
        context.watch<ContactusCubit>().allContactUs.where((e) => e.name?.toLowerCase() != 'call').toList();
    final call = context.watch<ContactusCubit>().allContactUs.where((e) => e.name?.toLowerCase() == 'call').toList();
    return Scaffold(
      appBar: CustomAppBar(title: AppLocalizations.of(context)!.connect_us),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              children: [
                const Gap(
                  h: 30,
                ),
                Center(
                  child: Container(
                    decoration: const BoxDecoration(
                      color: primaryColorLight,
                      shape: BoxShape.circle,
                    ),
                    child: const Padding(
                      padding: EdgeInsets.all(24.0),
                      child: Icon(
                        Icons.share,
                        size: 52,
                        color: primaryColorDark,
                      ),
                    ),
                  ),
                ),
                const Gap(
                  h: 40,
                ),
                const Divider(
                  indent: 16,
                ),
                const Gap(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(
                    allcontact.length,
                    (index) => GestureDetector(
                      onTap: () async {
                        final url = Uri.parse("${allcontact[index].link}");
                        if (await canLaunchUrl(url)) {
                          await launchUrl(
                            url,
                            mode: LaunchMode.externalApplication,
                          );
                        } else {
                          throw "Could not launch $url";
                        }
                      },
                      child: Container(
                        margin: EdgeInsets.only(right: 6.w),
                        height: 56,
                        width: 56,
                        child: CachedNetworkImage(
                          imageUrl: "${AppEnvironment.publicBaseUrl}${allcontact[index].image ?? ''}",
                          fit: BoxFit.contain,
                          placeholder: (context, url) => Center(
                            child: const CircularProgressIndicator(
                              valueColor: AlwaysStoppedAnimation(primaryColor),
                              value: 2.0,
                            ),
                          ),
                          errorWidget: (context, url, error) => Icon(Icons.error, size: 20.sp, color: Colors.red),
                        ),
                      ),
                    ),
                  ),
                ),
                Gap(h: 6.h),
                GestureDetector(
                  onTap: () async {
                    final url = Uri.parse("tel:${call[0].link}");
                    if (await canLaunchUrl(url)) {
                      await launchUrl(
                        url,
                        mode: LaunchMode.externalApplication,
                      );
                    } else {
                      throw "Could not launch $url";
                    }
                  },
                  child: Container(
                    padding: EdgeInsets.all(2.h),
                    decoration:
                        BoxDecoration(borderRadius: BorderRadius.circular(2.w), border: Border.all(color: Colors.grey)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [Icon(Icons.phone, size: 20.sp), const Text('Call Us')],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
