import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:yalla_gai_driver/features/connect/data/remote/contact_us_repo.dart';

import '../../../data/remote/contact_us_model.dart';

part 'contactus_state.dart';

class ContactusCubit extends Cubit<ContactusState> {
  ContactUsRepo contactUsRepo;
  ContactusCubit(this.contactUsRepo) : super(ContactusInitial()) {
    contactUs();
  }
  List<ContactUsModel> allContactUs = [];
  contactUs() async {
    try {
      emit(ContactusLoading());
      allContactUs = await contactUsRepo.contactUs();
      log('allContact${allContactUs.length}');
      emit(ContactusLoaded());
    } catch (e) {
      log(e.toString());
      emit(ContactusError());
    }
  }
}
