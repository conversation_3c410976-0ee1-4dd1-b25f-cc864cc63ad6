import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:isolate';

import 'package:flutter/foundation.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:geolocator/geolocator.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';

import '../../core/constants/constants.dart';
import '../../core/constants/end_points.dart';
import '../../core/error/exception.dart';
import '../../core/network/api_client.dart';
import '../authentication/repository/auth_repository.dart';
import '../booking/repository/trip_repository.dart';

/// Background task handler
class LocationTaskHandler extends TaskHandler {
  Timer? _timer;
  DateTime _lastLocationUpdateTime = DateTime.now();
  Position? _lastUpdatedLocation;
  double? _lastDirection;

  @override
  Future<void> onStart(DateTime timestamp, TaskStarter starter) async {
    // TODO: implement onStart
    debugPrint("Foreground Service start");
    throw UnimplementedError();
  }

  @override
  Future<void> onEvent(DateTime timestamp, SendPort? sendPort) async {
    log('in Event:');
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );

      onLocationUpdated(position);

      debugPrint('Location: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      debugPrint('Error getting location: $e');
    }
  }

  // @override
  // Future<void> onDestroy(DateTime timestamp, SendPort? sendPort) async {
  //   _timer?.cancel();
  //   debugPrint('Foreground Service Stopped');
// /**/  }
  @override
  Future<void> onDestroy(DateTime timestamp, bool isTimeout) {
    // TODO: implement onDestroy
    log("Destroy Service");
    throw UnimplementedError();
  }

  @override
  void onButtonPressed(String id) {}

  @override
  void onNotificationPressed() {
    FlutterForegroundTask.launchApp();
  }

  updateLocation({lat, lng, direction}) async {
    await sendLocationRequest(
        lat: '${lat}',
        lng: "${lng}",
        direction: "${direction}",
        byGoogle: false);
  }

  void onLocationUpdated(Position position) {
    if (position.heading > 0) {
      _lastDirection = position.heading;
    }

    bool shouldUpdateLocation = canUpdateLocationWithGoogle;

    if (globalDriverProfile.user?.avalabilityStatus == "on") {
      shouldUpdateLocation = true;
    }

    if (DateTime.now().difference(_lastLocationUpdateTime) >
        Duration(seconds: 30)) {
      shouldUpdateLocation = true;
    }

    if (_lastUpdatedLocation != null) {
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        _lastUpdatedLocation!.latitude,
        _lastUpdatedLocation!.longitude,
      );
      if (distance > 10) {
        shouldUpdateLocation = true;
      }
    }

    if (shouldUpdateLocation) {
      _lastLocationUpdateTime = DateTime.now();
      _lastUpdatedLocation = position;
      updateLocation(
        lat: position.latitude,
        lng: position.longitude,
        direction: _lastDirection ?? position.heading,
      );
    }
  }

  @override
  void onRepeatEvent(DateTime timestamp) {
    log('in Event:');
    // TODO: implement onRepeatEvent
    setLocation();
  }

  Future<void> setLocation() async {
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      onLocationUpdated(position);
      debugPrint('Location: ${position.latitude}, ${position.longitude}');
    } catch (e) {
      debugPrint('Error getting location: $e');
    }
  }

  sendLocationRequest(
      {required String lat,
      required String lng,
      required String direction,
      required bool byGoogle}) async {
    final requestBody = {
      'latitude': lat,
      'longitude': lng,
      if (direction != null) 'direction': direction,
      // if (tripId != null) 'trip_id': tripId,
      'user_id': LocalStorage.instance.userId,
    };

    try {
      var response = await HttpApiClient().put(
        EndPoints.updateLocation(byGoogle: byGoogle),
        body: jsonEncode(requestBody),
      );
      var responseString = response.body;
      final body = jsonDecode(responseString);

      if (body['data'] != null) {
        cityId = body['data']['city_id']?.toString();
        log("city$cityId");
      }
    } on UnauthorizedException catch (e) {
      log("User unauthorized $e");
    }
    return true;
  }
}
