import 'dart:io';

class User {
  String? id;
  String? deviceId;
  String? roleId;
  String? neighbourhoodId;
  String? blockConditionId;
  String? languageId;
  String? distanceId;
  String? email;
  String? latitude;
  String? longitude;
  String? location;
  String? otp;
  String? mobileNumber;
  String? arabicFirstName;
  String? arabicLastName;
  String? fullName;
  String? firstName;
  String? lastName;
  String? status;
  String? activationStatus;
  String? reason;
  String? gender;
  String? couponId;
  String? privacyPolicyId;
  String? termConditionId;
  String? accountTypeId;
  String? addressId;
  String? emailVerifiedAt;
  String? password2;
  String? profileImage;
  String? idPhoto;
  String? createdAt;
  String? updatedAt;
  Role? role;

  User({
    this.id,
    this.deviceId,
    this.roleId,
    this.neighbourhoodId,
    this.blockConditionId,
    this.languageId,
    this.distanceId,
    this.email,
    this.latitude,
    this.longitude,
    this.location,
    this.otp,
    this.mobileNumber,
    this.arabicFirstName,
    this.arabicLastName,
    this.fullName,
    this.firstName,
    this.lastName,
    this.status,
    this.activationStatus,
    this.reason,
    this.gender,
    this.couponId,
    this.privacyPolicyId,
    this.termConditionId,
    this.accountTypeId,
    this.addressId,
    this.emailVerifiedAt,
    this.password2,
    this.profileImage,
    this.idPhoto,
    this.createdAt,
    this.updatedAt,
    this.role,
  });

  User.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    deviceId = json['device_id']?.toString();
    roleId = json['role_id']?.toString();
    neighbourhoodId = json['neighbourhood_id']?.toString();
    blockConditionId = json['block_condition_id']?.toString();
    languageId = json['language_id']?.toString();
    distanceId = json['distance_id']?.toString();
    email = json['email']?.toString();
    latitude = json['latitude']?.toString();
    longitude = json['longitude']?.toString();
    location = json['location']?.toString();
    otp = json['otp']?.toString();
    mobileNumber = json['mobile_number']?.toString();
    arabicFirstName = json['arabic_first_name']?.toString();
    arabicLastName = json['arabic_last_name']?.toString();
    fullName = json['full_name']?.toString();
    firstName = json['first_name']?.toString();
    lastName = json['last_name']?.toString();
    status = json['status']?.toString();
    activationStatus = json['activation_status']?.toString();
    reason = json['reason']?.toString();
    gender = json['gender']?.toString();
    couponId = json['coupon_id']?.toString();
    privacyPolicyId = json['privacy_policy_id']?.toString();
    termConditionId = json['term_condition_id']?.toString();
    accountTypeId = json['account_type_id']?.toString();
    addressId = json['address_id']?.toString();
    emailVerifiedAt = json['email_verified_at']?.toString();
    password2 = json['password_2']?.toString();
    profileImage = json['profile_image']?.toString();
    idPhoto = json['id_photo']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
    role = json['role'] != null ? Role.fromJson(json['role']) : null;
  }
}

class Role {
  String? id;
  String? role;
  String? createdAt;
  String? updatedAt;

  Role({
    this.id,
    this.role,
    this.createdAt,
    this.updatedAt,
  });

  Role.fromJson(dynamic json) {
    id = json['id']?.toString();
    role = json['role']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
  }
}

class Profile {
  final String arabicFullName;
  final String englishFullName;
  final String gender;
  final File profileImage;
  final List<File?> criminalRecord;
  final List<File?> driverIdImages;
  final List<File?> carImages;
  final List<File?> drivingLicences;
  final List<File?> driverCertificates;
  final String idNumber;
  final String carModelId;
  final String carIndustryId;
  final String plateNumber;
  final String yearOfManufacturer;
  final String dateOfBirth;
  final String termConditionId;
  final String carCategoryId;
 
  final String privacyPolicyId;
  final String carColor;
  const Profile({
    required this.arabicFullName,
    required this.privacyPolicyId,
    required this.criminalRecord,
    required this.driverIdImages,
    required this.carCategoryId,
    required this.profileImage,
    required this.carImages,
    required this.carIndustryId,
    required this.carModelId,
  
    required this.dateOfBirth,
    required this.driverCertificates,
    required this.drivingLicences,
    required this.englishFullName,
    required this.gender,
    required this.idNumber,
    required this.plateNumber,
    required this.termConditionId,
    required this.yearOfManufacturer,
    required this.carColor,
  });
}
