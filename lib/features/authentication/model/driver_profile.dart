class DriverProfile {
  DriverInfo? user;
  dynamic totalTripCount;

  DriverProfile({this.user, this.totalTripCount});

  DriverProfile.fromJson(Map<String, dynamic> json) {
    user = json['user'] != null ? DriverInfo.fromJson(json['user']) : null;
    totalTripCount = int.tryParse(json['total_trip_count'].toString()) ?? 0;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (user != null) {
      data['user'] = user!.toJson();
    }
    data['total_trip_count'] = totalTripCount;
    return data;
  }

  void changeAvailabilityStatus(bool value) {
    user?.avalabilityStatus = value ? "on" : "off";
  }
}

class DriverInfo {
  String? id;
  String? deviceId;
  String? roleId;
  String? cityId;
  String? countryId;
  String? neighbourhoodId;
  String? blockConditionId;
  String? languageId;
  String? distanceId;
  String? email;
  String? latitude;
  String? longitude;
  String? direction;
  dynamic location, avalabilityStatus;
  String? otp;
  dynamic radius;
  String? mobileNumber;
  String? fullName;
  String? arabicFullName;
  String? rating;
  String? status;
  String? loginStatus;
  String? activationStatus;
  dynamic reason;
  String? gender;
  String? couponId;
  String? privacyPolicyId;
  String? termConditionId;
  String? accountTypeId;
  String? addressId;
  dynamic emailVerifiedAt;
  dynamic password2;
  String? profileImage;
  dynamic idPhoto;
  dynamic points;
  String? balance;
  dynamic deviceToken;
  String? banTill;
  String? createdAt;
  String? updatedAt;
  String? rateAt;
  AccountType? accountType;
  Driver? driver;
  City? city;
  Country? country;

  DriverInfo(
      {this.id,
      this.deviceId,
      this.roleId,
      this.cityId,
      this.countryId,
      this.neighbourhoodId,
      this.blockConditionId,
      this.languageId,
      this.distanceId,
      this.email,
      this.latitude,
      this.longitude,
      this.direction,
      this.location,
      this.avalabilityStatus,
      this.otp,
      this.radius,
      this.mobileNumber,
      this.fullName,
      this.arabicFullName,
      this.rating,
      this.status,
      this.loginStatus,
      this.activationStatus,
      this.reason,
      this.gender,
      this.couponId,
      this.privacyPolicyId,
      this.termConditionId,
      this.accountTypeId,
      this.addressId,
      this.emailVerifiedAt,
      this.password2,
      this.profileImage,
      this.idPhoto,
      this.points,
      this.balance,
      this.deviceToken,
      this.banTill,
      this.createdAt,
      this.updatedAt,
      this.rateAt,
      this.accountType,
      this.driver,
      this.city,
      this.country});

  DriverInfo.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    deviceId = json['device_id']?.toString();
    roleId = json['role_id']?.toString();
    cityId = json['city_id']?.toString();
    countryId = json['country_id']?.toString();
    neighbourhoodId = json['neighbourhood_id']?.toString();
    blockConditionId = json['block_condition_id']?.toString();
    languageId = json['language_id']?.toString();
    distanceId = json['distance_id']?.toString();
    email = json['email'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    direction = json['direction'];
    location = json['location'];
    otp = json['otp'];
    avalabilityStatus = json['availibility_status'];
    radius = json['radius'];

    mobileNumber = json['mobile_number'];
    fullName = json['full_name'];
    arabicFullName = json['arabic_full_name'];
    rating = json['rating'];
    status = json['status'];
    loginStatus = json['login_status'];
    activationStatus = json['activation_status'];
    reason = json['reason'];
    gender = json['gender'];
    couponId = json['coupon_id']?.toString();
    privacyPolicyId = json['privacy_policy_id']?.toString();
    termConditionId = json['term_condition_id']?.toString();
    accountTypeId = json['account_type_id']?.toString();
    addressId = json['address_id']?.toString();
    emailVerifiedAt = json['email_verified_at'];
    password2 = json['password_2'];
    profileImage = json['profile_image'];
    idPhoto = json['id_photo'];
    points = json['points'];
    balance = json['balance'];
    deviceToken = json['device_token'];
    banTill = json['ban_till'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    rateAt = json['rate_at'];
    accountType = json['account_type'] != null ? AccountType.fromJson(json['account_type']) : null;
    driver = json['driver'] != null ? Driver.fromJson(json['driver']) : null;
    city = json['city'] != null ? City.fromJson(json['city']) : null;
    country = json['country'] != null ? Country.fromJson(json['country']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['device_id'] = deviceId;
    data['role_id'] = roleId;
    data['city_id'] = cityId;
    data['country_id'] = countryId;
    data['neighbourhood_id'] = neighbourhoodId;
    data['block_condition_id'] = blockConditionId;
    data['language_id'] = languageId;
    data['distance_id'] = distanceId;
    data['email'] = email;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['direction'] = direction;
    data['location'] = location;
    data['otp'] = otp;
    data['radius'] = radius;
    data['mobile_number'] = mobileNumber;
    data['full_name'] = fullName;
    data['arabic_full_name'] = arabicFullName;
    data['rating'] = rating;
    data['status'] = status;
    data['login_status'] = loginStatus;
    data['activation_status'] = activationStatus;
    data['reason'] = reason;
    data['gender'] = gender;
    data['coupon_id'] = couponId;
    data['privacy_policy_id'] = privacyPolicyId;
    data['term_condition_id'] = termConditionId;
    data['account_type_id'] = accountTypeId;
    data['address_id'] = addressId;
    data['email_verified_at'] = emailVerifiedAt;
    data['password_2'] = password2;
    data['profile_image'] = profileImage;
    data['id_photo'] = idPhoto;
    data['points'] = points;
    data['balance'] = balance;
    data['device_token'] = deviceToken;
    data['ban_till'] = banTill;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (accountType != null) {
      data['account_type'] = accountType!.toJson();
    }
    if (driver != null) {
      data['driver'] = driver!.toJson();
    }
    if (city != null) {
      data['city'] = city!.toJson();
    }
    if (country != null) {
      data['country'] = country!.toJson();
    }
    return data;
  }
}

class AccountType {
  String? id;
  String? name;
  String? arabicName;
  String? incentives;
  String? offers;
  String? priorityOfRequests;
  String? commission;
  String? iconImage;
  String? color;
  String? minRating;
  String? countryId;
  String? cityId;
  String? status;
  String? createdAt;
  String? updatedAt;

  AccountType(
      {this.id,
      this.name,
      this.arabicName,
      this.incentives,
      this.offers,
      this.priorityOfRequests,
      this.commission,
      this.iconImage,
      this.color,
      this.minRating,
      this.countryId,
      this.cityId,
      this.status,
      this.createdAt,
      this.updatedAt});

  AccountType.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name'];
    arabicName = json['arabic_name'];
    incentives = json['incentives'];
    offers = json['offers'];
    priorityOfRequests = json['priority_of_requests'];
    commission = json['commission'];
    iconImage = json['icon-image'];
    color = json['color'];
    minRating = json['min_rating'];
    countryId = json['country_id']?.toString();
    cityId = json['city_id']?.toString();
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['arabic_name'] = arabicName;
    data['incentives'] = incentives;
    data['offers'] = offers;
    data['priority_of_requests'] = priorityOfRequests;
    data['commission'] = commission;
    data['icon-image'] = iconImage;
    data['color'] = color;
    data['min_rating'] = minRating;
    data['country_id'] = countryId;
    data['city_id'] = cityId;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class Driver {
  String? id;
  String? userId;
  String? idNumber;
  String? dateOfBirth;
  String? availibilityStatus;
  dynamic clientsGender;
  List? criminalRecordImages;
  List? driverIdImages;
  List? drivingLicencesImages;
  List? driverCertificatesImages;
  List? carImages;
  String? carModelId;
  String? carModelBrandId;
  String? yearOfManufacturer;
  String? plateNumber;
  String? carColor;
  String? carCategoriesIds;
  String? currentCarCategoriesIds;
  String? createdAt;
  String? updatedAt;
  CarModel? carModel;
  CarModelBrand? carModelBrand;

  Driver(
      {this.id,
      this.userId,
      this.idNumber,
      this.dateOfBirth,
      this.availibilityStatus,
      this.clientsGender,
      this.criminalRecordImages,
      this.driverIdImages,
      this.drivingLicencesImages,
      this.driverCertificatesImages,
      this.carImages,
      this.carModelId,
      this.carModelBrandId,
      this.yearOfManufacturer,
      this.plateNumber,
      this.carColor,
      this.carCategoriesIds,
      this.currentCarCategoriesIds,
      this.createdAt,
      this.updatedAt,
      this.carModel,
      this.carModelBrand});

  Driver.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    idNumber = json['id_number'];
    dateOfBirth = json['date_of_birth'];
    availibilityStatus = json['availibility_status'];
    clientsGender = json['clients_gender'];
    criminalRecordImages = json['criminal_record_images'];
    driverIdImages = json['driver_id_images'];
    drivingLicencesImages = json['driving_licences_images'];
    driverCertificatesImages = json['driver_certificates_images'];
    carImages = json['car_images'];
    carModelId = json['car_model_id']?.toString();
    carModelBrandId = json['car_model_brand_id']?.toString();
    yearOfManufacturer = json['year_of_manufacturer'];
    plateNumber = json['plate_number'];
    carColor = json['car_color'];
    carCategoriesIds = json['car_categories_ids'];
    currentCarCategoriesIds = json['current_car_categories_ids'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    carModel = json['car_model'] != null ? CarModel.fromJson(json['car_model']) : null;
    carModelBrand = json['car_model_brand'] != null ? CarModelBrand.fromJson(json['car_model_brand']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['id_number'] = idNumber;
    data['date_of_birth'] = dateOfBirth;
    data['availibility_status'] = availibilityStatus;
    data['clients_gender'] = clientsGender;
    data['criminal_record_images'] = criminalRecordImages;
    data['driver_id_images'] = driverIdImages;
    data['driving_licences_images'] = drivingLicencesImages;
    data['driver_certificates_images'] = driverCertificatesImages;
    data['car_images'] = carImages;
    data['car_model_id'] = carModelId;
    data['car_model_brand_id'] = carModelBrandId;
    data['year_of_manufacturer'] = yearOfManufacturer;
    data['plate_number'] = plateNumber;
    data['car_color'] = carColor;
    data['car_categories_ids'] = carCategoriesIds;
    data['current_car_categories_ids'] = currentCarCategoriesIds;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (carModel != null) {
      data['car_model'] = carModel!.toJson();
    }
    if (carModelBrand != null) {
      data['car_model_brand'] = carModelBrand!.toJson();
    }
    return data;
  }
}

class CarModel {
  String? id;
  String? model;

  CarModel({
    this.id,
    this.model,
  });

  CarModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    model = json['model'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['model'] = model;

    return data;
  }
}

class CarModelBrand {
  String? id;
  String? name;
  String? modelId;

  CarModelBrand({this.id, this.name, this.modelId});

  CarModelBrand.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name'];
    modelId = json['model_id']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['model_id'] = modelId;
    return data;
  }
}

class City {
  String? id;
  String? countryId;
  String? arabicName;
  String? name;
  String? genericName;
  String? status;
  dynamic image;

  City({this.id, this.countryId, this.arabicName, this.name, this.genericName, this.status, this.image});

  City.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    countryId = json['country_id']?.toString();
    arabicName = json['arabic_name'];
    name = json['name'];
    genericName = json['generic_name'];
    status = json['status'];
    image = json['image'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['country_id'] = countryId;
    data['arabic_name'] = arabicName;
    data['name'] = name;
    data['generic_name'] = genericName;
    data['status'] = status;
    data['image'] = image;
    return data;
  }
}

class Country {
  String? id;
  String? englishCurrency;
  String? arabicCurrency;
  String? internationalKey;
  String? countryFlag;
  String? arabicName;
  String? name;
  String? status;

  Country({
    this.id,
    this.englishCurrency,
    this.arabicCurrency,
    this.internationalKey,
    this.countryFlag,
    this.arabicName,
    this.name,
    this.status,
  });

  Country.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    englishCurrency = json['english_currency'];
    arabicCurrency = json['arabic_currency'];
    internationalKey = json['international_key'];
    countryFlag = json['country_flag'];
    arabicName = json['arabic_name'];
    name = json['name'];
    status = json['status'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['english_currency'] = englishCurrency;
    data['arabic_currency'] = arabicCurrency;
    data['international_key'] = internationalKey;
    data['country_flag'] = countryFlag;
    data['arabic_name'] = arabicName;
    data['name'] = name;
    data['status'] = status;
    return data;
  }
}
