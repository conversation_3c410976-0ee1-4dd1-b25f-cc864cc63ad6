class RatingOptions {
  String? id;
  String? option;
  String? status;
  String? createdAt;
  String? updatedAt;

  RatingOptions({
    this.id,
    this.option,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory RatingOptions.fromJson(Map<String, dynamic> json) => RatingOptions(
        id: json["id"]?.toString(),
        option: json["option"]?.toString(),
        status: json["status"]?.toString(),
        createdAt: json["created_at"]?.toString(),
        updatedAt: json["updated_at"]?.toString(),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "option": option,
        "status": status,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}
