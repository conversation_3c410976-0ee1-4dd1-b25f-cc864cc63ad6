class LanguageData {
  final String? id;
  final String? language;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  LanguageData({
    required this.id,
    required this.language,
    this.createdAt,
    this.updatedAt,
  });

  factory LanguageData.fromJson(Map<String, dynamic> json) {
    return LanguageData(
      id: json['id']?.toString(),
      language: json['language']?.toString(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'language': language,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}
