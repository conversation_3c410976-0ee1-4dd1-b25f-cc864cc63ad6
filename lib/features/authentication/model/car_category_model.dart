class CarCategoryModel {
  String? id;
  String? arabicName;
  String? name;
  String? type;
  String? image;
  String? numberOfPeople;
  String? gender;
  String? cityId;
  String? serviceId;

  List<Intervals>? intervals;

  CarCategoryModel(
      {this.id,
      this.arabicName,
      this.name,
      this.type,
      this.image,
      this.numberOfPeople,
      this.gender,
      this.cityId,
      this.serviceId,
      this.intervals});

  CarCategoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    arabicName = json['arabic_name'];
    name = json['name'];
    type = json['type'];
    image = json['image'];
    numberOfPeople = json['number_of_people'];
    gender = json['gender'];
    cityId = json['city_id']?.toString();
    serviceId = json['service_id']?.toString();

    if (json['intervals'] != null) {
      intervals = <Intervals>[];
      json['intervals'].forEach((v) {
        intervals!.add(Intervals.fromJson(v));
      });
    }
  }
}

class Intervals {
  String? id;
  String? name;
  String? arName;
  String? minimumTripValue;
  String? pricePerKm;
  String? pricePerMinute;
  String? addedValue;
  String? carCategoryId;
  String? startTime;
  String? endTime;
  String? createdAt;
  String? updatedAt;

  Intervals(
      {this.id,
      this.name,
      this.arName,
      this.minimumTripValue,
      this.pricePerKm,
      this.pricePerMinute,
      this.addedValue,
      this.carCategoryId,
      this.startTime,
      this.endTime,
      this.createdAt,
      this.updatedAt});

  Intervals.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    name = json['name'];
    arName = json['ar_name'];
    minimumTripValue = json['minimum_trip_value'];
    pricePerKm = json['price_per_km'];
    pricePerMinute = json['price_per_minute'];
    addedValue = json['added_value'];
    carCategoryId = json['car_category_id']?.toString();
    startTime = json['start_time'];
    endTime = json['end_time'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}
