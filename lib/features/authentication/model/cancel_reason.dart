class CancelReason {
  String? id;
  String? reason;
  String? roleId;
  String? createdAt;
  String? updatedAt;

  CancelReason({
    this.id,
    this.reason,
    this.roleId,
    this.createdAt,
    this.updatedAt,
  });

  factory CancelReason.fromJson(Map<String, dynamic> json) => CancelReason(
        id: json["id"]?.toString(),
        reason: json["reason"]?.toString(),
        roleId: json["role_id"]?.toString(),
        createdAt: json["created_at"]?.toString(),
        updatedAt: json["updated_at"]?.toString(),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "reason": reason,
        "role_id": roleId,
        "created_at": createdAt,
        "updated_at": updatedAt,
      };
}
