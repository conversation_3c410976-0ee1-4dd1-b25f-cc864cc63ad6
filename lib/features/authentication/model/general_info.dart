class GeneralInformation {
  String? numberOfTrips;
  String? acceptedRequest;
  String? rejectedRequest;
  String? workingHours;

  GeneralInformation({this.acceptedRequest, this.numberOfTrips, this.rejectedRequest, this.workingHours});

  factory GeneralInformation.fromJson(Map<String, dynamic> json) {
    return GeneralInformation(
      acceptedRequest: json['accepted request percentage']?.toString(),
      numberOfTrips: json['number of trips']?.toString(),
      rejectedRequest: json['rejected request']?.toString(),
      workingHours: json['working hours']?.toString(),
    );
  }
}

class UserTripInfo {
  String? numberOfTrips;
  String? acceptedRequest;
  String? rejectedRequest;
  String? workingHours;

  UserTripInfo({this.acceptedRequest, this.numberOfTrips, this.rejectedRequest, this.workingHours});

  factory UserTripInfo.fromJson(Map<String, dynamic> json) {
    return UserTripInfo(
      acceptedRequest: json['accepted_request_percentage']?.toString(),
      numberOfTrips: json['number_of_trips']?.toString(),
      rejectedRequest: json['rejected_request']?.toString(),
    );
  }
}
