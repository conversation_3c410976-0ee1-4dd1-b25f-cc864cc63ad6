import 'dart:developer';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui';

import 'package:animated_custom_dropdown/custom_dropdown.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flex_color_picker/flex_color_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/app_locale.dart';
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/utils/car_types.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/cubit/carmodel_cubit.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/language/language_bloc.dart';
import 'package:yalla_gai_driver/features/booking/bloc/service_bloc/service_bloc.dart';
import 'package:yalla_gai_driver/features/booking/bloc/system_service/system_services_cubit.dart';
import 'package:yalla_gai_driver/features/booking/repository/trip_repository.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../../../core/shared_widgets/custom_textfield.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/utils/validators.dart';
import '../../../shared/screen/image_cropper.dart';
import '../../model/car_category_model.dart';
import '../../model/car_model_model.dart';
import '../../model/user.dart';
import '../../repository/auth_repository.dart';
import '../widgets/circular_check_box.dart';
import '../widgets/image_radio.dart';
import '../widgets/text_underline.dart';

class SetProfile extends StatefulWidget {
  const SetProfile({super.key});

  @override
  State<SetProfile> createState() => _SetProfileState();
}

class _SetProfileState extends State<SetProfile> {
  @override
  void initState() {
    super.initState();
    getUserLocationAndService();
  }

  updateLocation() async {
    setState(() {
      isLoading = true;
    });
    Position position =
    await context.read<LocationService>().getCurrentLocation();

    try {
      await TripRepository().updateLocation(
        lat: '${position.latitude}',
        lng: "${position.longitude}",
        direction: "${position.heading}",
        byGoogle: true
      );
      await AuthRepository().getProfile();
      // if (isUpdates == true) {
      await context.read<CarmodelCubit>().fetchCarBrands();
      // }
    } catch (e) {
      log("location:$e");
    }
    setState(() {
      isLoading = false;
    });
  }

  final _arabicFullNameEditingController = TextEditingController();
  final _arabicFullNameEditingKey = GlobalKey();

  final _englishFullNameEditingController = TextEditingController();
  final _englishFullNameEditingKey = GlobalKey();
  final _dateOfBirthEditingController = TextEditingController();
  final _dateOfBirthEditingKey = GlobalKey();

  final _idNumberEditingController = TextEditingController();
  final _idNumberEditingKey = GlobalKey();
  final _plateNumberEditingController = TextEditingController();
  final _plateNumberEditingKey = GlobalKey();
  final _yearOfManufaturerEditingController = TextEditingController();
  final _yearOfManufaturerEditingKey = GlobalKey();
  final _scrollController = ScrollController();

  final _formKey = GlobalKey<FormState>();
  File? profileImage;
  List<File> carPhoto = [];
  List<File> driverLicense = [];
  List<File> driverId = [];
  List<File> certificateOfNonSecurity = [];
  List<File> criminalRecord = [];
  bool firstTerm = false;

  int selectedRadio = 1;

  Future<File> compressImage(File image) async {
    int originalSize = image.lengthSync();
    print('Original image size: ${originalSize / 1024} KB'); // Print size in KB
    var result = await FlutterImageCompress.compressWithFile(
      image.absolute.path,
      minWidth: 800,
      // Resize the width to 800px (you can adjust this)
      minHeight: 800,
      // Resize the height to 800px (you can adjust this)
      quality: 80,
      // Compression quality (80% - you can adjust this for a balance between size and quality)
      format:
      CompressFormat.jpeg, // You can change the format (JPEG, PNG, etc.)
    );

    final compressedImage = File(image.path)..writeAsBytesSync(result!);
    // Print the compressed size of the image
    int compressedSize = compressedImage.lengthSync();
    print(
      'Compressed image size: ${compressedSize / 1024} KB',
    ); // Print size in KB
    return compressedImage;
  }

  Future<File> compressImageForBytes(Uint8List image, String path) async {
    print('Original image size: ${image.length / 1024} KB'); // Print size in KB
    var result = await FlutterImageCompress.compressWithList(
      image,
      minWidth: 800,
      // Resize the width to 800px (you can adjust this)
      minHeight: 800,
      // Resize the height to 800px (you can adjust this)
      quality: 80,
      // Compression quality (80% - you can adjust this for a balance between size and quality)
      format:
          CompressFormat.jpeg, // You can change the format (JPEG, PNG, etc.)
    );

    final compressedImage = File(path)..writeAsBytesSync(result!);
    // Print the compressed size of the image
    int compressedSize = compressedImage.lengthSync();
    print(
      'Compressed image size: ${compressedSize / 1024} KB',
    ); // Print size in KB
    return compressedImage;
  }

  getImage(
      {required String type,
      bool profilePix = false,
      BuildContext? context}) async {
    final picker = ImagePicker();
    if (profilePix == true) {
      final XFile? image =
          await picker.pickImage(source: ImageSource.gallery, imageQuality: 85);
      if (image != null && context != null && context.mounted) {
        Uint8List imageBytes = await image.readAsBytes();
        final result = await Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => ImageCropperScreen(
                    image: imageBytes,
                  )),
        );
        if (result != null) {
          // Compress the image before setting it
          File compressedImage = await compressImage(
              await compressImageForBytes(result, image.path));
          setState(() {
            profileImage = compressedImage;
          });
        }
      }
    } else {
      final List<XFile> images = await picker.pickMultiImage(
        limit: 4,
        imageQuality: 85,
      );
      final pickedImages =
      images.map((xFile) => File(xFile.path)).take(4).toList();

      // Compress all selected images before storing them in the lists
      List<File> compressedImages = [];
      for (var pickedImage in pickedImages) {
        compressedImages.add(await compressImage(pickedImage));
      }

      switch (type) {
        case 'Car':
          setState(() {
            carPhoto.addAll(compressedImages);
          });
          break;
        case 'License':
          setState(() {
            driverLicense.addAll(compressedImages);
          });
          break;
        case 'driverId':
          setState(() {
            driverId.addAll(compressedImages);
          });
          break;
        case 'security':
          setState(() {
            certificateOfNonSecurity.addAll(compressedImages);
          });
          break;
        case 'criminalRecord':
          setState(() {
            criminalRecord.addAll(compressedImages);
          });
          break;
      }
    }
  }

  Color pickerColor = const Color(0xff443a49);

// ValueChanged<Color> callback
  void changeColor(Color color) {
    setState(() => pickerColor = color);
  }

// create some values
  Color? currentColor;

// Define custom colors. The 'guide' color values are from
  // https://material.io/design/color/the-color-system.html#color-theme-creation
  static const Color guidePrimary = Color(0xFF6200EE);
  static const Color guidePrimaryVariant = Color(0xFF3700B3);
  static const Color guideSecondary = Color(0xFF03DAC6);
  static const Color guideSecondaryVariant = Color(0xFF018786);
  static const Color guideError = Color(0xFFB00020);
  static const Color guideErrorDark = Color(0xFFCF6679);
  static const Color blueBlues = Color(0xFF174378);

  // Make a custom ColorSwatch to name map from the above custom colors.
  final Map<ColorSwatch<Object>, String> colorsNameMap =
  <ColorSwatch<Object>, String>{
    ColorTools.createPrimarySwatch(guidePrimary): 'Guide Purple',
    ColorTools.createPrimarySwatch(guidePrimaryVariant): 'Guide Purple Variant',
    ColorTools.createAccentSwatch(guideSecondary): 'Guide Teal',
    ColorTools.createAccentSwatch(guideSecondaryVariant): 'Guide Teal Variant',
    ColorTools.createPrimarySwatch(guideError): 'Guide Error',
    ColorTools.createPrimarySwatch(guideErrorDark): 'Guide Error Dark',
    ColorTools.createPrimarySwatch(blueBlues): 'Blue blues',
  };

  _showDialog() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Pick a color!'),
          content: SingleChildScrollView(
            child: ColorPicker(
              color: pickerColor,
              onColorChanged: (Color color) =>
                  setState(() => pickerColor = color),
            ),
          ),
          actions: <Widget>[
            ElevatedButton(
              child: const Text('Got it'),
              onPressed: () {
                setState(() => currentColor = pickerColor);
                log(currentColor!.value.toString());
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  void _validateAndScroll() async {
    bool isValid = _formKey.currentState!.validate();
    List<GlobalKey> keys = [_arabicFullNameEditingKey,
      _englishFullNameEditingKey,
      _dateOfBirthEditingKey,
      _idNumberEditingKey,
      _plateNumberEditingKey,
      _yearOfManufaturerEditingKey];
    List<TextEditingController> controllers = [_arabicFullNameEditingController,
      _englishFullNameEditingController,
      _dateOfBirthEditingController,
      _idNumberEditingController,
      _plateNumberEditingController,
      _yearOfManufaturerEditingController];
    if (!isValid) {
      for (final entry in keys.asMap().entries) {
        final index = entry.key;
        final controller = controllers.elementAt(index);
        if (!controller.selection.isValid){
          final context = entry.value.currentContext;
          await Scrollable.ensureVisible(
            context!,
            duration: Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
          break;
        }
      }

    }
  }


  bool isLoading = false;
  String error = "";

  @override
  Widget build(BuildContext context) {
    final watchCarCubit = context.watch<CarmodelCubit>();
    final readCarCubit = context.read<CarmodelCubit>();
    // context.read<CarmodelCubit>().fetchCarBrands();
    return AppScaffold(
      isLoading: isLoading,
      appBar: AppBar(
        backgroundColor: white,
        centerTitle: true,
        forceMaterialTransparency: true,
        leading: GestureDetector(
          onTap: () {
            context.push(path.languageSetting);
          },
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 3.w),
            child: Image.asset(
              "assets/images/account/language.png",
              height: 25.h,
            ),
          ),
        ),
        title: Text(
          AppLocalizations.of(context)!.fill_your_profile,
          style: Theme.of(context).textTheme.displayMedium,
        ),
        actions: [
          IconButton(
            onPressed: () async {
              showGeneralDialog(
                barrierDismissible: true,
                barrierLabel: '',
                barrierColor: Colors.black38,
                transitionDuration: const Duration(milliseconds: 500),
                pageBuilder: (ctx, anim1, anim2) => AlertDialog(
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.confirm_log_out,
                        style: Theme.of(context).textTheme.bodyLarge,
                        textAlign: TextAlign.center,
                      ),
                      const Gap(),
                      Text(
                        AppLocalizations.of(context)!.log_out_warning,
                        style:
                        Theme.of(context).textTheme.labelMedium!.copyWith(
                          color: Colors.black.withOpacity(0.4),
                          fontWeight: FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const Gap(),
                      const Divider(),
                      const Gap(),
                      Column(
                        children: [
                          CustomButton(
                            buttonText: AppLocalizations.of(context)!.no,
                            onPressed: () {
                              // Handle 'No' button press
                              context.pop();
                            },
                            width: MediaQuery.of(context).size.width * 0.8,
                            borderOnly: true,
                            borderRadius: 10,
                          ),
                          const Gap(),
                          CustomButton(
                            buttonText: AppLocalizations.of(context)!.yes,
                            width: MediaQuery.of(context).size.width * 0.8,
                            onPressed: () async {
                              SharedPreferences preferences =
                              await SharedPreferences.getInstance();
                              preferences.clear();
                              BlocProvider.of<ServiceBloc>(context).add(
                                const ServiceChangeStatus(
                                  id: null,
                                  status: 'off',
                                ),
                              );
                              GoRouter.of(context).pushReplacement(path.login);
                            },
                            borderRadius: 10,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                transitionBuilder: (ctx, anim1, anim2, child) => BackdropFilter(
                  filter: ImageFilter.blur(
                    sigmaX: 4 * anim1.value,
                    sigmaY: 4 * anim1.value,
                  ),
                  child: FadeTransition(
                    opacity: anim1,
                    child: child,
                  ),
                ),
                context: context,
              );
            },
            padding: EdgeInsets.all(3.w),
            icon: SvgPicture.asset(
              "assets/images/logout.svg",
              height: 25,
              width: 25,
            ),
          ),
        ],
      ),
      body: SingleChildScrollView(
        controller: _scrollController,
        child: Form(
          key: _formKey,
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 1.h),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Stack(
                          children: [
                            Container(
                              width: 140,
                              // adjust the width as per your requirement
                              height: 140,
                              // adjust the height as per your requirement
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: black.withOpacity(0.2),
                                  width: 2,
                                ),
                              ),
                              child: CircleAvatar(
                                maxRadius: 80,
                                backgroundColor: Colors.white,
                                child: profileImage == null
                                    ? InkWell(
                                  customBorder: const CircleBorder(),
                                  onTap: () async {
                                    getImage(profilePix: true, type: '', context: context);
                                  },
                                  child: SvgPicture.asset(
                                    personIcon,
                                    height: 70,
                                    width: 80,
                                  ),
                                )
                                    : CircleAvatar(
                                  backgroundColor: white,
                                  maxRadius: 80,
                                  backgroundImage: FileImage(
                                    profileImage!,
                                  ),
                                ),
                              ),
                            ),
                            Positioned(
                              bottom: 12,
                              right: 15,
                              child: InkWell(
                                onTap: () async {
                                  getImage(profilePix: true, type: '',context: context);
                                },
                                child: const SizedBox(
                                  width: 25,
                                  height: 35,
                                  child: Center(
                                    child: Icon(Icons.photo_camera, size: 40),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Gap(h: 3.h),
              // Padding(
              //   padding: EdgeInsets.symmetric(horizontal: 20.w),
              //   child: CustomButton(
              //     height: 55,
              //     buttonText: AppLocalizations.of(context)!.language,
              //     borderRadius: 12,
              //     onPressed: () {
              //       context.push(path.languageSetting);
              //     },
              //   ),
              // ),
              // Gap(h: 1.h),
              CustomTextField(
                width: 65.5.w,
                labelText: AppLocalizations.of(context)!.arabic_full_name,
                validator: (v) {
                  if ((v?.length ?? 0) < 2|| Validator().detectLanguage(string: v??"")!="ar") {
                    return AppLocalizations.of(context)!.enter_valid_name;
                    // "Enter a valid name";
                  }
                  return null;
                },
                textEditingController: _arabicFullNameEditingController,
                key: _arabicFullNameEditingKey,
                borderRadius: 4,
              ),
              const Gap(h: 5),
              CustomTextField(
                width: 65.5.w,
                labelText: AppLocalizations.of(context)!.english_full_name,
                validator: (v) {
                  if ((v?.length ?? 0) < 2 || Validator().detectLanguage(string: v??"")!="en") {
                    return AppLocalizations.of(context)!.enter_valid_name;
                    // return "Enter a valid name";
                  }
                  return null;
                },
                textEditingController: _englishFullNameEditingController,
                key: _englishFullNameEditingKey,
                borderRadius: 4,
              ),
              Gap(h: 4.h),
              Padding(
                padding: EdgeInsets.only(
                  left: Utils.isAribic(context) ? 0 : 17.w,
                  right: Utils.isAribic(context) ? 17.w : 0.w,
                ),
                child: Align(
                  alignment: Utils.isAribic(context)
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Text(
                    AppLocalizations.of(context)!.choose_gender,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: grey,
                    ),
                  ),
                ),
              ),
              Gap(h: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ImageRadio(
                    imagePath: selectedRadio == 1
                        ? maleIconSelected
                        : maleIconUnSelected,
                    value: 1,
                    groupValue: selectedRadio,
                    label: AppLocalizations.of(context)!.male,
                    onChanged: (value) {
                      setState(() {
                        selectedRadio = value ?? 0;
                      });
                    },
                  ),
                  ImageRadio(
                    imagePath: selectedRadio == 2
                        ? femaleIconSelected
                        : femaleIconUnSelected,
                    value: 2,
                    groupValue: selectedRadio,
                    label: AppLocalizations.of(context)!.female,
                    onChanged: (value) {
                      setState(() {
                        selectedRadio = value ?? 1;
                      });
                    },
                  ),
                ],
              ),
              Gap(h: 3.h),
              Padding(
                padding: EdgeInsets.only(
                  left: Utils.isAribic(context) ? 0 : 17.w,
                  right: Utils.isAribic(context) ? 17.w : 0.w,
                ),
                child: Align(
                  alignment: Utils.isAribic(context)
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Text(
                    AppLocalizations.of(context)!.date_of_birth,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: grey,
                    ),
                  ),
                ),
              ),
              Gap(h: 2.h),
              CustomTextField(
                width: 65.5.w,
                labelText: "DD/MM/YYY",
                readOnly: true,
                validator: (v) {
                  if (v?.isEmpty ?? true) {
                    return AppLocalizations.of(context)!.valid_date_of_birth;
                    // return "Enter valid date of birth!";
                  }
                  DateTime? date = DateFormat("dd/MM/yyyy").tryParse(v ?? '');
                  if (date == null) {
                    return AppLocalizations.of(context)!.valid_date_of_birth;
                    // return "Enter valid date of birth!";
                  }
                  return null;
                },
                inputFormatters: [
                  const DateInputFormatter(),
                ],
                textEditingController: _dateOfBirthEditingController,
                key: _dateOfBirthEditingKey,
                borderRadius: 4,
                icon: IconButton(
                  onPressed: _pickDateOfBirth,
                  icon: Icon(Icons.calendar_month_rounded),
                ),
              ),
              CustomTextField(
                width: 65.5.w,
                labelText: AppLocalizations.of(context)!.id_number,
                validator: (v) {
                  if (v?.isEmpty ?? true) {
                    return AppLocalizations.of(context)!.valid_id_number;
                  }
                  return null;
                },
                textEditingController: _idNumberEditingController,
                key: _idNumberEditingKey,
                borderRadius: 4,
              ),
              const Gap(),
              CustomTextField(
                width: 65.5.w,
                labelText: AppLocalizations.of(context)!.plate_number,
                validator: (v) {
                  if (v?.isEmpty ?? true) {
                    return AppLocalizations.of(context)!.valid_plate_number;
                    // return "Enter a valid plate number";
                  }
                  return null;
                },
                textEditingController: _plateNumberEditingController,
                key: _plateNumberEditingKey,
                borderRadius: 4,
              ),
              const Gap(),
              Padding(
                padding: EdgeInsets.only(
                  left: Utils.isAribic(context) ? 0 : 17.w,
                  right: Utils.isAribic(context) ? 17.w : 0.w,
                ),
                child: Align(
                  alignment: Utils.isAribic(context)
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Text(
                    AppLocalizations.of(context)!.car_brand,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: grey,
                    ),
                  ),
                ),
              ),
              Gap(h: 1.h),
              Container(
                width: 70.w,
                alignment: Alignment.center,
                child: CustomDropdown<String>(
                  decoration: const CustomDropdownDecoration(
                    closedFillColor: Color.fromARGB(255, 245, 245, 245),
                  ),
                  hintText: AppLocalizations.of(context)!.car_brand,
                  items: watchCarCubit.allCarBrands.isEmpty
                      ? ['']
                      : watchCarCubit.allCarBrands
                      .map((e) => e.model!)
                      .toList(),
                  initialItem: watchCarCubit.allCarBrands.isEmpty
                      ? ""
                      : watchCarCubit.allCarBrands.first.model,
                  onChanged: (value) {
                    readCarCubit.getCarModelByCarBrand(brand: value!);
                  },
                ),
              ),
              const Gap(),
              Padding(
                padding: EdgeInsets.only(
                  left: Utils.isAribic(context) ? 0 : 17.w,
                  right: Utils.isAribic(context) ? 17.w : 0.w,
                ),
                child: Align(
                  alignment: Utils.isAribic(context)
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Text(
                    AppLocalizations.of(context)!.car_industry,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: grey,
                    ),
                  ),
                ),
              ),
              Gap(h: 1.h),
              Container(
                width: 70.w,
                alignment: Alignment.center,
                child: CustomDropdown<String>(
                  decoration: const CustomDropdownDecoration(
                    closedFillColor: Color.fromARGB(255, 245, 245, 245),
                  ),
                  hintText: AppLocalizations.of(context)!.car_industry,
                  items: watchCarCubit.filteredCarModel.isEmpty
                      ? [""]
                      : watchCarCubit.filteredCarModel
                      .map((e) => e.name!)
                      .toList(),
                  initialItem: watchCarCubit.filteredCarModel.isNotEmpty
                      ? watchCarCubit.filteredCarModel.first.name
                      : '',
                  onChanged: (value) {
                    readCarCubit.selectCarmodel(model: value!);
                  },
                ),
              ),
              const Gap(),
              Padding(
                padding: EdgeInsets.only(
                  left: Utils.isAribic(context) ? 0 : 17.w,
                  right: Utils.isAribic(context) ? 17.w : 0.w,
                ),
                child: Align(
                  alignment: Utils.isAribic(context)
                      ? Alignment.centerRight
                      : Alignment.centerLeft,
                  child: Text(
                    AppLocalizations.of(context)!.car_year,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: grey,
                    ),
                  ),
                ),
              ),
              Gap(h: 1.h),
              Container(
                width: 70.w,
                alignment: Alignment.center,
                child: CustomDropdown<String>(
                  decoration: const CustomDropdownDecoration(
                    closedFillColor: Color.fromARGB(255, 245, 245, 245),
                  ),
                  hintText: "Year of Manufacturer",
                  items: yearOfManufucture,
                  key: _yearOfManufaturerEditingKey,
                  initialItem: yearOfManufucture.contains("2022")
                      ?"2022"
                      :yearOfManufucture.first,
                  onChanged: (value) {
                    _yearOfManufaturerEditingController.text = value ?? "";
                  },
                ),
              ),
              const Gap(),
              InkWell(
                onTap: _showDialog,
                child: Container(
                  width: 70.w,
                  height: 60,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: currentColor == null ? Colors.grey : Colors.green,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: const Text(
                    "Select car color",
                    style: TextStyle(color: Colors.white),
                  ),
                ),
                //fff44336
              ),

              if (currentColor != null) ...[
                const Gap(),
                Container(
                  width: 70.w,
                  height: 6.h,
                  decoration: BoxDecoration(
                    color: currentColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ],
              // if (currentColor != null)
              //   Column(
              //     children: [
              //       Text(
              //         "0xFF${currentColor?.value}",
              //         style: TextStyle(
              //             color: Color(int.parse("0xFF${currentColor?.hex}"))),
              //       ),
              //     ],
              //   ),
              const Gap(),
              const Gap(),
              TextUnderLine(
                text: AppLocalizations.of(context)!.type_of_car,
              ),
              Container(
                margin: const EdgeInsets.symmetric(horizontal: 20),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  color: const Color(0xffe7ecec),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    ListTile(
                      contentPadding:
                      const EdgeInsets.symmetric(horizontal: 12),
                      title: Text(
                        AppLocalizations.of(context)!
                            .choose_right_car_type_for_you,
                      ),
                      subtitle: Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.info,
                              color: Colors.grey,
                            ),
                            SizedBox(
                              width: 8,
                            ),
                            Expanded(
                              child: Text(
                                AppLocalizations.of(context)!
                                    .choose_right_car_type_for_you_subtitle,
                                style:
                                TextStyle(color: Colors.grey, fontSize: 14),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Builder(
                      builder: (context) {
                        final cars = watchCarCubit.carCategory
                            .where((element) => element.type == "normal")
                            .toList();

                        final children = List<Widget>.generate(
                          math.max(0, (cars.length * 2) - 1),
                              (initialIndex) {
                            if (initialIndex.isOdd) {
                              return const Gap(w: 16);
                            }
                            final index = initialIndex ~/ 2;

                            return GestureDetector(
                              onTap: () {
                                readCarCubit.selectCarCategory(
                                  car: cars[index],
                                );
                              },
                              child: CarSelectionWidget(
                                imageUrl:
                                "${AppEnvironment.publicBaseUrl}${cars[index].image}",
                                selected: watchCarCubit.selectedCarCat
                                    .contains(cars[index]),
                                type: cars[index].name ?? '',
                                numberOfPeople:
                                cars[index].numberOfPeople ?? '',
                              ),
                            );
                          },
                        );

                        return SingleChildScrollView(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: children,
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              Gap(h: 4.h),
              if (context.select<SystemServicesCubit, bool>(
                    (value) {
                  final serviceData = value.services
                      .where((element) => element.type == "vehicle_photos")
                      .firstOrNull;
                  return serviceData == null
                      ? true
                      : serviceData.status == "active";
                },
              )) ...[
                TextUnderLine(text: AppLocalizations.of(context)!.car_Photo),
                const Gap(),
                PhotoUploadWidget(
                  image: carPhoto,
                  uploadOnTap: () {
                    getImage(type: 'Car');
                  },
                ),
                const Gap(),
              ],

              if (context.select<SystemServicesCubit, bool>(
                    (value) {
                  final serviceData = value.services
                      .where((element) => element.type == "drivers_license")
                      .firstOrNull;
                  return serviceData == null
                      ? true
                      : serviceData.status == "active";
                },
              )) ...[
                TextUnderLine(
                  text: AppLocalizations.of(context)!.driver_license,
                ),
                const Gap(),
                PhotoUploadWidget(
                  image: driverLicense,
                  uploadOnTap: () {
                    getImage(type: 'License');
                  },
                ),
                const Gap(),
              ],

              if (context.select<SystemServicesCubit, bool>(
                    (value) {
                  final serviceData = value.services
                      .where((element) => element.type == "vehicle_insurance")
                      .firstOrNull;
                  return serviceData == null
                      ? true
                      : serviceData.status == "active";
                },
              )) ...[
                TextUnderLine(text: AppLocalizations.of(context)!.driver_id),
                const Gap(),
                PhotoUploadWidget(
                  image: driverId,
                  uploadOnTap: () {
                    getImage(type: 'driverId');
                  },
                ),
                const Gap(),
              ],

              if (context.select<SystemServicesCubit, bool>(
                    (value) {
                  final serviceData = value.services
                      .where(
                        (element) =>
                    element.type == "criminal_record_clearance",
                  )
                      .firstOrNull;
                  return serviceData == null
                      ? true
                      : serviceData.status == "active";
                },
              )) ...[
                TextUnderLine(
                  text: AppLocalizations.of(context)!.criminal_record,
                ),
                const Gap(),
                PhotoUploadWidget(
                  image: criminalRecord,
                  uploadOnTap: () {
                    getImage(type: 'criminalRecord');
                  },
                ),
                const Gap(),
              ],

              if (context.select<SystemServicesCubit, bool>(
                    (value) {
                  final serviceData = value.services
                      .where(
                        (element) => element.type == "vehicle_registration",
                  )
                      .firstOrNull;
                  return serviceData == null
                      ? true
                      : serviceData.status == "active";
                },
              )) ...[
                TextUnderLine(
                  text:
                  AppLocalizations.of(context)!.certificate_of_non_security,
                ),
                const Gap(),
                PhotoUploadWidget(
                  image: certificateOfNonSecurity,
                  uploadOnTap: () {
                    getImage(type: 'security');
                  },
                ),
                Gap(h: 3.h),
              ],
              CircularCheckbox(
                text: AppLocalizations.of(context)!
                    .i_agree_to_terms_and_conditions_services_policies_and_prices,
                value: firstTerm,
                onChanged: (value) {
                  setState(() {
                    firstTerm = value;
                  });
                },
              ),
              Gap(h: 4.h),
              BlocConsumer<AuthBloc, AuthState>(
                listener: (context, state) {
                  if (state is AuthSetupProfileLoading) {}
                  if (state is AuthSetupProfileSuccess) {
                    context.pushReplacement(path.transitionPage);
                  }
                  if (state is AuthSetupProfileFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                },
                builder: (contex, state) {
                  return CustomButton(
                    isLoading: state is AuthSetupProfileLoading ? true : false,
                    height: 55,
                    buttonText: AppLocalizations.of(context)!.create_ccount,
                    borderRadius: 12,
                    onPressed: firstTerm
                        ? () {
                      updateProfile(
                        carCategory: readCarCubit.selectedCarCat,
                        carBrandId: readCarCubit.selectedCarBrand.id,
                        carModelId: readCarCubit.selectedCarModel.id,
                      );
                    }
                        : null,
                  );
                },
              ),
              Gap(h: 4.h),
            ],
          ),
        ),
      ),
    );
  }

  showSnackbar(String englishText, String arabicText) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        backgroundColor: Colors.red,
        content: Text(
          Utils.appText(
            context: context,
            listen: false,
            text: englishText,
            arabicText: arabicText,
          ),
          textAlign: TextAlign.center,
        ),
      ),
    );
  }

  Future<void> _pickDateOfBirth() async {
    DateTime? initialDate =
    DateFormat("dd/MM/yyyy").tryParse(_dateOfBirthEditingController.text);

    final now = DateTime.now();
    final firstDate = DateTime(now.year - 100, now.month, now.day);
    final lastDate = DateTime(now.year - 17, now.month, now.day);

    final result = await showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(12)),
      ),
      backgroundColor: Colors.white,
      builder: (context) {
        return _DatePickerSheet(
          initialDate: initialDate ?? lastDate,
          firstDate: firstDate,
          lastDate: lastDate,
        );
      },
    );

    if (result != null) {
      _dateOfBirthEditingController.text =
          DateFormat("dd/MM/yyyy").format(result);
    }
  }

  updateProfile({carModelId, carBrandId, carCategory}) {
    if (_formKey.currentState?.validate() ?? false) {
    } else {
      _validateAndScroll();
      return;
    }

    if (profileImage == null) {
      showCustomSnackbar(
        context,
        AppLocalizations.of(context)!.please_select_profile_picture,
      );
      return;
    }

    if (carModelId == null || carBrandId == null || carCategory == null) {
      showCustomSnackbar(
        context,
        AppLocalizations.of(context)!.please_select_car,
      );
      return;
    }

    if ((carCategory ?? <dynamic>[]).isEmpty) {
      showCustomSnackbar(
        context,
        AppLocalizations.of(context)!
            .please_select_right_car_type_for_you,
      );
      return;
    }else{

      if (carCategory is List<CarCategoryModel>) {
        bool valid = carCategory.any((element) => element.type == "normal");
        if (!valid) {
          showCustomSnackbar(
            context,
            AppLocalizations.of(context)!.please_select_right_car_type_for_you,
          );
          return;
        }
      }
    }

    final systemServices = context.read<SystemServicesCubit>().services;

    final isVehiclePhotosActive = systemServices.any(
          (s) => s.type == 'vehicle_photos' && s.status == 'active',
    );
    if (isVehiclePhotosActive && carPhoto.isEmpty) {
      showSnackbar("Add a picture for your car", "أضف صورة لسيارتك");
      return;
    }

    final isDriversLicenseActive = systemServices.any(
          (s) => s.type == 'drivers_license' && s.status == 'active',
    );
    if (isDriversLicenseActive && driverLicense.isEmpty) {
      showSnackbar("Add driver's license document", "أضف وثيقة رخصة القيادة");
      return;
    }

    final isVehicleInsuranceActive = systemServices.any(
          (s) => s.type == 'vehicle_insurance' && s.status == 'active',
    );
    if (isVehicleInsuranceActive && driverId.isEmpty) {
      showSnackbar(
        "Add a picture of your Vehicle Registration",
        "أضف صورة رخصة السيارة",
      );
      return;
    }

    final isCriminalRecordActive = systemServices.any(
          (s) => s.type == 'criminal_record_clearance' && s.status == 'active',
    );
    if (isCriminalRecordActive && criminalRecord.isEmpty) {
      showSnackbar(
        "Add your Criminal Record Clearance",
        "أضف شهادة عدم محكومية الخاصة بك",
      );
      return;
    }

    final isVehicleRegistrationActive = systemServices.any(
          (s) => s.type == 'vehicle_registration' && s.status == 'active',
    );
    if (isVehicleRegistrationActive && certificateOfNonSecurity.isEmpty) {
      showSnackbar("Add your Vehicle Insurance", "أضف تأمين المركبة");
      return;
    }

    if (currentColor == null) {
      showSnackbar("Select a car color", "اختر لون السيارة");
      return;
    }

    final dateOfBirth =
    DateFormat("dd/MM/yyyy").parse(_dateOfBirthEditingController.text);
    Profile profile = Profile(
      arabicFullName: _arabicFullNameEditingController.text,
      privacyPolicyId: "1",
      criminalRecord: criminalRecord,
      driverIdImages: driverId,
      carCategoryId:  carCategory
        .map((e) => e.id)
        .toList()
        .join(','),
      profileImage: profileImage!,
      carImages: carPhoto,
      carIndustryId: carBrandId.toString(),
      carModelId: carModelId.toString(),
      dateOfBirth: DateFormat("yyyy-MM-dd").format(dateOfBirth),
      driverCertificates: certificateOfNonSecurity,
      drivingLicences: driverLicense,
      englishFullName: _englishFullNameEditingController.text,
      gender: selectedRadio == 1 ? "Male" : "Female",
      idNumber: _idNumberEditingController.text,
      plateNumber: _plateNumberEditingController.text,
      termConditionId: "termConditionId",
      yearOfManufacturer: _yearOfManufaturerEditingController.text == ""
          ? "1990"
          : _yearOfManufaturerEditingController.text,
      carColor: currentColor != null ? "0xFF${currentColor?.hex}" : "",
    );
    BlocProvider.of<AuthBloc>(context).add(AuthSetupProfile(profile: profile));
    final locale = context.read<AppLocale>().locale;
    BlocProvider.of<LanaguageBloc>(context).add(
      LanguageUpdate(languageId: locale.languageCode == "ar" ? "2" : "1"),
    );
  }

  Future<void> getUserLocationAndService() async {
    await context
        .read<LocationService>()
        .requestPermission()
        .then((value) => updateLocation());
    context.read<SystemServicesCubit>().fetchServicesList();
  }
}

class PhotoUploadWidget extends StatefulWidget {
  const PhotoUploadWidget({
    super.key,
    required this.image,
    this.uploadOnTap,
  });

  final List<File> image;
  final Function()? uploadOnTap;

  @override
  State<PhotoUploadWidget> createState() => _PhotoUploadWidgetState();
}

class _PhotoUploadWidgetState extends State<PhotoUploadWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        widget.image.isEmpty
            ? GestureDetector(
          onTap: widget.uploadOnTap,
          child: Container(
            width: 40.w,
            height: 40.w,
            decoration: BoxDecoration(
              border: Border.all(
                color: Colors.grey.withOpacity(0.5),
                width: 2,
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.upload_file_outlined,
                  size: 30.sp,
                  weight: 1,
                  color: Colors.grey.withOpacity(0.5),
                ),
                Text(
                  AppLocalizations.of(context)!
                      .maximum_4_photos_are_required,
                  textAlign: TextAlign.center,
                  style: TextStyle(color: Colors.black, fontSize: 14.sp),
                ),
              ],
            ),
          ),
        )
            : Wrap(
          runSpacing: 5.w,
          spacing: 3.w,
          children: List.generate(
            widget.image.length,
                (index) => Container(
              width: 40.w,
              height: 40.w,
              decoration: BoxDecoration(
                image: DecorationImage(
                  fit: BoxFit.cover,
                  image: FileImage(widget.image[index]),
                ),
                border: Border.all(
                  color: Colors.grey.withOpacity(0.5),
                  width: 2,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    widget.image.removeAt(index);
                  });
                },
                child: Align(
                  alignment: Alignment.topRight,
                  child:
                  Icon(Icons.close, size: 22.sp, color: Colors.red),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class CarSelectionWidget extends StatelessWidget {
  final bool selected;
  final String type, imageUrl, numberOfPeople;

  const CarSelectionWidget({
    required this.selected,
    required this.type,
    required this.numberOfPeople,
    super.key,
    required this.imageUrl,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(10),
          height: 30.w,
          width: 30.w,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: selected ? const Color(0xffd1dadb) : white,
            border: selected ? Border.all(color: primaryColor) : null,
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(10.w),
            child: CachedNetworkImage(
              height: 30.w,
              width: 30.w,
              imageUrl: imageUrl,
              fit: BoxFit.contain,
              placeholder: (context, url) => const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation(primaryColor),
              ),
              errorWidget: (context, url, error) =>
                  Icon(Icons.error, size: 20.sp),
            ),
          ),
        ),
        Gap(h: 1.h),
        Text(
          type,
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        Gap(h: 1.h),
        Text.rich(
          TextSpan(
            children: [
              const WidgetSpan(child: Icon(Icons.person_rounded, size: 20)),
              const TextSpan(text: " "),
              const TextSpan(text: "x"),
              const TextSpan(text: " "),
              TextSpan(text: numberOfPeople),
            ],
          ),
          style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
      ],
    );
  }
}

class DateInputFormatter extends TextInputFormatter {
  const DateInputFormatter();

  static final _regex = RegExp(r'[0-9/]+');

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue,
      TextEditingValue newValue,
      ) {
    print("DateInputFormatter");
    if (newValue.text.length < oldValue.text.length) {
      final newText = (newValue.text.endsWith('/'))
          ? newValue.text.substring(0, newValue.text.length - 1)
          : newValue.text;
      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    }

    if (newValue.text.length > 10) return oldValue;

    bool newValueValid = _regex.hasMatch(newValue.text);
    if (!newValueValid) return oldValue;
    final parts = newValue.text.split('/').map((e) => e);

    if (parts.length == 1) {
      final currentPart = parts.first;
      if (currentPart.length != 2) return newValue;
      final newText = "${int.parse(currentPart) > 31 ? "01" : currentPart}/";
      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    }

    if (parts.length == 2) {
      final currentPart = parts.elementAt(1);
      if (currentPart.length < 2) return newValue;
      final newText =
          "${parts.elementAt(0)}/${int.parse(currentPart) > 12 ? "12" : currentPart}/";
      return TextEditingValue(
        text: newText,
        selection: TextSelection.collapsed(offset: newText.length),
      );
    }

    return newValue;
  }
}

class _DatePickerSheet extends StatefulWidget {
  const _DatePickerSheet({
    required this.initialDate,
    required this.firstDate,
    required this.lastDate,
  });

  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime lastDate;

  @override
  State<_DatePickerSheet> createState() => _DatePickerSheetState();
}

class _DatePickerSheetState extends State<_DatePickerSheet> {
  late DateTime _selectedDate = widget.initialDate;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: kToolbarHeight,
            child: NavigationToolbar(
              trailing: TextButton(
                style: TextButton.styleFrom(foregroundColor: primaryColor),
                onPressed: () => Navigator.of(context).pop(_selectedDate),
                child: Text(
                  AppLocalizations.of(context)!.done,
                  style: TextStyle(fontWeight: FontWeight.w600),
                ),
              ),
            ),
          ),
          Flexible(
            child: SizedBox(
              height: 240,
              child: CupertinoDatePicker(
                onDateTimeChanged: (value) {
                  _selectedDate = value;
                },
                mode: CupertinoDatePickerMode.date,
                dateOrder: DatePickerDateOrder.dmy,
                initialDateTime: _selectedDate,
                minimumDate: widget.firstDate,
                maximumDate: widget.lastDate,
              ),
            ),
          ),
        ],
      ),
    );
  }
}