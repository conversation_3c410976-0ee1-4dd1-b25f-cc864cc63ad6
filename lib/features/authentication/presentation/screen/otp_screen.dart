import 'dart:async';
import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/dialog_with_image_builder.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../account_type/domain/entity/device_info.dart';
import '../../repository/auth_repository.dart';

class OTPScreen extends StatefulWidget {
  final String phoneNumber;
  final bool isForgotPassword;
  final bool isFromChangePhoneNumber;

  const OTPScreen({
    super.key,
    required this.phoneNumber,
    this.isFromChangePhoneNumber = false,
    this.isForgotPassword = false,
  });

  @override
  OTPScreenState createState() => OTPScreenState();
}

class OTPScreenState extends State<OTPScreen> {
  final _otpController = TextEditingController();
  final FocusNode _otpFocusNode = FocusNode();
  Timer? _timer;
  late final ValueNotifier<Duration> _durationNotifier;

// Request focus on the text field

  bool enableButton = false;
  String deviceId = "";

  @override
  void initState() {
    _getDeviceId();
    super.initState();
    _durationNotifier = ValueNotifier(const Duration(minutes: 1));
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) => _startTimer());
  }

  @override
  void dispose() {
    _timer?.cancel();
    _durationNotifier.dispose();
    super.dispose();
  }

  _getDeviceId() async {
    deviceId = await getDeviceId();
    log("Device id: $deviceId");
  }

  bool isLoading = false;

  void _startTimer() {
    _durationNotifier.value = const Duration(minutes: 1);
    _timer = Timer.periodic(
      Durations.extralong4,
      (timer) {
        final duration = _durationNotifier.value - Durations.extralong4;
        _durationNotifier.value = duration;
        if (duration <= Duration.zero) {
          timer.cancel();
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      isLoading: isLoading,
      appBar: AppBar(
        backgroundColor: white,
        leading: InkWell(
          onTap: () {
            context.pop();
          },
          child: const BackButtonIcon(),
        ),
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(height: 3.h),
            const EnterOTP(),
            SizedBox(height: 6.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: Text(
                '${AppLocalizations.of(context)!.type_in_the_4_digit_number_sent_to_the_number} ${widget.phoneNumber}',
                style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 5.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              child: PinCodeTextField(
                onChanged: (value) {
                  setState(() {
                    enableButton = value.length == 4;
                  });
                },
                textStyle: Theme.of(context)
                    .textTheme
                    .labelLarge!
                    .copyWith(fontSize: 20.sp, color: primaryColor),
                hintCharacter: "X",
                hintStyle: Theme.of(context)
                    .textTheme
                    .labelLarge!
                    .copyWith(color: black.withOpacity(0.5), fontSize: 20.sp),
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                controller: _otpController,
                focusNode: _otpFocusNode,
                appContext: context,
                length: 4,
                pinTheme: PinTheme(
                  shape: PinCodeFieldShape.underline,
                  activeColor: primaryColor,
                  selectedColor: Colors.grey,
                  inactiveColor: Colors.grey,
                  disabledColor: Colors.grey,
                  fieldHeight: 54,
                  fieldWidth: 10.w,
                ),
                keyboardType: TextInputType.number,
              ),
            ),
            SizedBox(height: 4.h),
            ValueListenableBuilder(
              valueListenable: _durationNotifier,
              builder: (context, duration, child) {
                return Center(
                  child: BlocBuilder<AuthBloc, AuthState>(
                    builder: (context, state) {
                      return Text.rich(
                        TextSpan(
                          children: duration <= Duration.zero
                              ? [
                                  TextSpan(
                                      text: AppLocalizations.of(context)!
                                          .did_not_recieved_otp,),
                                  const TextSpan(text: " "),
                                  if (state is AuthReSendOtpLoading)
                                    const WidgetSpan(
                                      child: SizedBox(
                                        width: 20,
                                        height: 20,
                                        child: CircularProgressIndicator(
                                          valueColor: AlwaysStoppedAnimation(
                                              primaryColor,),
                                        ),
                                      ),
                                    )
                                  else
                                    TextSpan(
                                      text: AppLocalizations.of(context)!
                                          .resend_otp,
                                      style: const TextStyle(color: Colors.red),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          if (widget.isFromChangePhoneNumber) {
                                            BlocProvider.of<AuthBloc>(context)
                                                .add(AuthChangePhoneNumber(
                                                    phoneNumber:
                                                        widget.phoneNumber,),);
                                          } else {
                                            BlocProvider.of<AuthBloc>(context)
                                                .add(
                                              AuthReSendOtp(
                                                  phoneNumber:
                                                      widget.phoneNumber,
                                                  deviceId: deviceId,),
                                            );
                                          }
                                        },
                                    ),
                                ]
                              : [
                                  TextSpan(
                                    text: Utils.appText(
                                      context: context,
                                      text: "Resend code in",
                                      arabicText: "اعد ارسال الرمز بعد",
                                    ),
                                  ),
                                  const TextSpan(text: " "),
                                  TextSpan(
                                    text: duration.inSeconds
                                        .toString()
                                        .padLeft(2, '0'),
                                    style: const TextStyle(
                                      color: primaryColor,
                                      fontWeight: FontWeight.w600,
                                      fontFeatures: [
                                        FontFeature.tabularFigures(),
                                      ],
                                    ),
                                  ),
                                  const TextSpan(text: " "),
                                  TextSpan(
                                    text: Utils.appText(
                                        context: context,
                                        text: "seconds",
                                        arabicText: "ثانيه",),
                                  ),
                                ],
                        ),
                        style: Theme.of(context).textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      );
                    },
                  ),
                );
              },
            ),
            const Gap(),
            GestureDetector(
              onTap: () {
                // _otpFocusNode.requestFocus();
                // context.go(path.login);
                context.pop();
              },
              child: Center(
                child: Text(
                  AppLocalizations.of(context)!.edit,
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall!
                      .copyWith(color: blueTextColor),
                ),
              ),
            ),
            Gap(h: 8.h),
            Center(
              child: BlocConsumer<AuthBloc, AuthState>(
                listener: (context, state) async {
                  if (state is AuthChangePhoneNumberFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                  if (state is AuthChangePhoneNumberSuccess) {
                    if (state.isSuccess) {
                      dialogWithImageBuilder(
                        onPressed: () {
                          context.go(path.home);
                        },
                        showButtonInside: true,
                        context: context,
                        image: checkedImage,
                        successText: AppLocalizations.of(context)!
                            .successfully_changed_phone_number,
                        buttonText: AppLocalizations.of(context)!.okay,
                      );
                    }
                  }

                  if (state is AuthVerifyOtpSuccess) {
                    if (widget.isFromChangePhoneNumber) {
                      BlocProvider.of<AuthBloc>(context).add(
                          AuthChangePhoneNumber(
                              phoneNumber: widget.phoneNumber,),);
                    } else {
                      await AuthRepository().getProfile();
                      final driverUser = state.user;
                      if (driverUser.status?.toLowerCase()
                          case "pending" || null) {
                        if (driverUser.fullName case "" || null) {
                          context.go(path.setProfile);
                        } else {
                          context.go(path.transitionPage);
                        }
                      } else {
                        context.go(path.home);
                      }
                    }
                  }
                  if (state is AuthVerifyOtpFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                  if (state is AuthReSendOtpSuccess) {
                    showCustomSnackbar(context, "Otp sent");
                    _startTimer();
                  }

                  if (state is AuthChangePhoneNumberOtpSentSuccess) {
                    showCustomSnackbar(context, "Otp sent");
                    _startTimer();
                  }
                  if (state is AuthReSendOtpFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                },
                builder: (context, state) {
                  return CustomButton(
                    isLoading: state is AuthVerifyOtpLoading ||
                            state is AuthChangePhoneNumberLoading
                        ? true
                        : false,
                    buttonText: AppLocalizations.of(context)!.verify,
                    borderRadius: 12,
                    onPressed: enableButton ? onVerfiyPressed : null,
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  onVerfiyPressed() {
    if (widget.isFromChangePhoneNumber) {
      BlocProvider.of<AuthBloc>(context).add(AuthChangePhoneVerify(
          otp: _otpController.text, phoneNumber: widget.phoneNumber,),);
    } else {
      BlocProvider.of<AuthBloc>(context).add(AuthVerifyOtp(
          otp: _otpController.text,
          phoneNumeber: widget.phoneNumber,
          deviceId: deviceId,),);
    }
    // context.push(path.setProfile);
  }
}

class EnterOTP extends StatelessWidget {
  const EnterOTP({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          width: MediaQuery.of(context).size.width - 30.w,
          height: 60,
          color: primaryColor,
          child: Center(
            child: Text(
              AppLocalizations.of(context)!.enter_OTP,
              style: Theme.of(context)
                  .textTheme
                  .displayLarge!
                  .copyWith(fontWeight: FontWeight.w500, color: white),
            ),
          ),
        ),
        Positioned(
          right: 0,
          child: SizedBox(
            width: 30, // adjust the width as per your requirement
            height: 60, // adjust the height as per your requirement
            child: ClipPath(
              clipper: TriangleClipper(),
              child: Container(
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class TriangleClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.moveTo(0, size.height / 2);
    path.lineTo(size.width, 0);
    path.lineTo(size.width, size.height);
    path.close();
    return path;
  }

  @override
  bool shouldReclip(TriangleClipper oldClipper) => false;
}
