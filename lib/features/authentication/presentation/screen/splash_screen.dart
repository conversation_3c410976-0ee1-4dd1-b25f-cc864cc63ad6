import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:yalla_gai_driver/core/constants/app_utils.dart';
import 'package:yalla_gai_driver/core/error/exception.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/ride_request/ride_request_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/system_service/system_services_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/booking/domain/entity/location_entity.dart';
import 'package:yalla_gai_driver/features/booking/domain/entity/ride_request.dart';
import 'package:yalla_gai_driver/features/booking/repository/counter_repository.dart';
import 'package:yalla_gai_driver/features/booking/repository/trip_repository.dart';
import 'package:yalla_gai_driver/features/setting/bloc/app_version/app_version_cubit.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../booking/bloc/service_bloc/service_bloc.dart';

// ignore_for_file: use_build_context_synchronously

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  SplashScreenState createState() => SplashScreenState();
}

class SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback(
      (timeStamp) => _initialize(),
    );
  }

  Future<void> _initialize() async {
    print("==>>> here socket connection 1");
    final accessToken = LocalStorage.instance.accessToken ?? '';
    print("==>>> here socket connection 2");

    if (accessToken.isEmpty) {
      print("==>>> here socket connection 3");
      if (LocalStorage.instance.isFirstTime ?? true) {
        print("==>>> here socket connection 4");
        nextPage = path.onboarding;
      } else {
        nextPage = path.login;
      }
      print("==>>> here socket connection 5");
      Timer(Durations.extralong4, () => context.go(nextPage));
      return;
    }
    print("==>>> here socket connection 6");

    AuthRepository().fetchDriverLocally();
    print("==>>> here socket connection 7");

    _shouldUpdateLocation();
    try {
      print("==>>> here socket connection 08");
      final driverProfile = await AuthRepository().getProfile();
      print("==>>> here socket connection 8");
      context.read<SystemServicesCubit>().fetchServicesList();
      print("==>>> here socket connection 9");
      BlocProvider.of<ServiceBloc>(context)
          .add(const ServiceChangeStatus(id: null, status: 'off'));
      print("==>>> here socket connection 10");
      BlocProvider.of<AppVersionCubit>(context).getAppVersion();
      print("==>>> here socket connection 11");
      BlocProvider.of<TripBloc>(context).add(GetActiveTrip());
      print("==>>> here socket connection 12");
      context.read<TripBloc>().add(const RejectTripReason());

      print("==>>> here socket connection 13");
      final driverUser = driverProfile.user;
      if (driverUser?.status?.toLowerCase() case "pending" || null) {
        if (driverUser?.fullName case "" || null) {
          nextPage = path.setProfile;
        } else {
          nextPage = path.transitionPage;
        }
        context.go(nextPage);
      } else {
        nextPage = path.home;
      }
    } on UnauthorizedException catch (_) {
      nextPage = path.login;
      context.go(nextPage);
    }
  }

  var nextPage = "";

  Future<void> _shouldUpdateLocation() async {
    final locationService = context.read<LocationService>();
    final permissionStatus = await locationService.checkPermission();
    print("==>> here socket connection 01");
    switch (permissionStatus) {
      case LocationPermission.denied ||
            LocationPermission.deniedForever ||
            LocationPermission.unableToDetermine:
        break;
      case LocationPermission.whileInUse || LocationPermission.always:
        final location = await locationService.getCurrentLocation();
        TripRepository().updateLocation(
          lat: '${location.latitude}',
          lng: "${location.longitude}",
          direction: "${location.heading}",
          byGoogle: true,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<TripBloc, TripState>(
      listener: (context, state) {
        print("==>>> here socket connection listen");
        if (state is TripAcceptNewRequestSuccess) {
          if (state.requestAccept.newRequest != null) {
            context
                .read<RideRequestCubit>()
                .setComingData(state.requestAccept.newRequest!);
          }
          var status2 =
              int.tryParse(state.requestAccept.trip!.status.toString()) ?? 1;
          context.read<TripBloc>().add(GetCorrectState(status: status2));
          print("==>>> here socket connection init");
          CustomSocket.connectAndListener(context: context);
          LocationEntity pickUpLocation = LocationEntity(
            locationText: state.requestAccept.newRequest?.pickupLocation ?? "",
            latitude: state.requestAccept.newRequest?.pickupLatitude ?? 0,
            longitude: state.requestAccept.newRequest?.pickupLongitude ?? 0,
            streetName: null,
          );

          LocationEntity dropLocation = LocationEntity(
            locationText:
                state.requestAccept.newRequest?.destinationLocation ?? "",
            latitude: state.requestAccept.newRequest?.destinationLatitude ?? 0,
            longitude:
                state.requestAccept.newRequest?.destinationLongitude ?? 0,
            streetName: null,
          );

          RideRequest rideRequest = RideRequest(
            fare: state.requestAccept.newRequest?.fare.toString() ?? "",
            id: state.requestAccept.newRequest?.id.toString() ?? "",
            name: "John Doe",
            profilePicture: "https://example.com/profile_picture.jpg",
            userPickUpLocation: pickUpLocation,
            userDropLocation: dropLocation,
          );

          CustomSocket.reSubscribeChannel(
            channelName:
                'private-user-send-message-channel_${globalDriverProfile.user?.id}',
          );
          if (status2 == 1 || status2 == 2) {
            Future.delayed(Duration.zero, () async {
              AppUtils.waitTime =
                  await TripRepository().getWatingTime('waiting');
              CounterRepository()
                  .getCounterFees()
                  .then((value) => AppUtils.counterFees = value);
              context.push(
                path.acceptRide,
                extra: {
                  "rideRequest": rideRequest,
                  "requestAccept": state.requestAccept,
                },
              );
            });
          } else if (status2 > 2) {
            context.push(
              path.tripStarted,
              extra: {
                'rideRequest': context.read<RideRequestCubit>().requestData,
                "requestAccept": state.requestAccept,
              },
            );
          }
        } else if (state is TripGetNewRequestFaulire) {
          Timer(const Duration(milliseconds: 100), () {
            if (nextPage != "") {
              context.go(nextPage);
            }
          });
        }
      },
      child: Scaffold(
        body: Stack(
          fit: StackFit.expand,
          children: [
            DecoratedBox(
              decoration: const BoxDecoration(color: Colors.black54),
              position: DecorationPosition.foreground,
              child: Image.asset(
                splashScreenImage, // Replace with your image path
                fit: BoxFit.cover,
                width: double.infinity,
                height: double.infinity,
              ),
            ),
            Center(
              child: SvgPicture.asset(
                yallaGaiWhiteLogo,
                width: 180,
                height: 180,
                color: white, // Replace with your image path
                fit: BoxFit.contain,
              ),
            ),

            Positioned.fill(
              bottom: 0,
              child: Image.asset(
                splashBuildingsImage, // Replace with your image path
                fit: BoxFit.contain,
                alignment: Alignment.bottomCenter,
              ),
            ),

            Positioned(
              bottom: MediaQuery.paddingOf(context).bottom + 160,
              left: 0,
              right: 0,
              height: 64,
              child: const Center(
                child: CircularProgressIndicator(
                  color: primaryColor,
                  strokeWidth: 6,
                  backgroundColor: Colors.white,
                  strokeCap: StrokeCap.round,
                ),
              ),
            ),
            // Add your widgets on top of the background image and overlay here
          ],
        ),
      ),
    );
  }
}
