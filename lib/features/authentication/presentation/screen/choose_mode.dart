import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/black_logo.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class ChooseMode extends StatelessWidget {
  const ChooseMode({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: white,
        body: SafeArea(
            child: SingleChildScrollView(
                child: Center(
          child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const BlackLogo(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Image.asset(
                      manWithBag,
                      width: 180,
                      height: 250,
                    ),
                    Container(
                        height: 250, width: 1, color: black.withOpacity(0.3)),
                    SvgPicture.asset(manSittingOnCar)
                  ],
                ),
                Gap(
                  h: 2.h,
                ),
                Text(AppLocalizations.of(context)!.passenger_or_driver,
                    style: Theme.of(context).textTheme.displayMedium!.copyWith(
                          fontFamily: "Plus Jakarta Sans",
                        )),
                Gap(
                  h: 2.h,
                ),
                Text(AppLocalizations.of(context)!.you_can_Change_mode_later
                  ,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontFamily: "Plus Jakarta Sans",
                        )),
                Gap(
                  h: 4.h,
                ),
                Container(
                    width: MediaQuery.of(context).size.width - 15.w,
                    height: 1,
                    color: black.withOpacity(0.1)),
                Gap(
                  h: 4.h,
                ),
                CustomButton(
                  buttonText: AppLocalizations.of(context)!.passenger,
                  borderRadius: 4,
                  onPressed: () {},
                  width: 60.w,
                  height: 8.h,
                ),
                Gap(
                  h: 2.h,
                ),
                CustomButton(
                  buttonText: AppLocalizations.of(context)!.driver,
                  borderRadius: 4,
                  onPressed: () {},
                  width: 60.w,
                  height: 8.h,
                ),
              ]),
        ))));
  }
}
