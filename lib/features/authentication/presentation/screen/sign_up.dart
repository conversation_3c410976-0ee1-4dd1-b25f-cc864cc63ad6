import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/black_logo.dart';
import '../../../../core/shared_widgets/divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import 'package:intl_phone_field/intl_phone_field.dart';

import '../widgets/circular_check_box.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class SignUpPage extends StatefulWidget {
  const SignUpPage({super.key});

  @override
  SignUpPageState createState() => SignUpPageState();
}

class SignUpPageState extends State<SignUpPage> {
  final _formKey = GlobalKey<FormState>();
  String _phoneEditingController = "";
  bool checkBoxValue = false;
  bool isPhoneValid = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: white,
        body: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                const BlackLogo(),
                Gap(h: 4.h),
                Align(
                  alignment: Alignment.center,
                  child: Text(
                    AppLocalizations.of(context)!.enter_your_mobile_number,
                    textAlign: TextAlign.left,
                    style: Theme.of(context).textTheme.displayLarge!.copyWith(),
                  ),
                ),
                Gap(h: 4.h),
                const CustomDivider(),
                Gap(h: 6.h),
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      SizedBox(
                        width: 85.w,
                        child: IntlPhoneField(
                          textAlignVertical: TextAlignVertical.center,
                          decoration: InputDecoration(
                            focusColor: primaryColor,
                            border: const UnderlineInputBorder(),
                            label: Text(
                                AppLocalizations.of(context)!.mobile_number,
                                style: Theme.of(context).textTheme.labelSmall),
                          ),
                          initialCountryCode: 'JO',
                          flagsButtonPadding: const EdgeInsets.all(4),
                          dropdownIconPosition: IconPosition.trailing,
                          flagsButtonMargin:
                              const EdgeInsets.only(right: 20, bottom: 0),
                          dropdownDecoration: BoxDecoration(
                            border: Border.all(color: black.withOpacity(0.4)),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          onChanged: (phone) {
                            // Handle phone number changes

                            setState(() {
                              isPhoneValid = phone.isValidNumber();

                              _phoneEditingController = phone.completeNumber;
                            });
                          },
                        ),
                      ),
                      Gap(
                        h: 3.h,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                              AppLocalizations.of(context)!.do_you_have_account,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(fontWeight: FontWeight.bold)),
                          Gap(w: 1.w),
                          GestureDetector(
                            onTap: () {
                              context.push(path.login);
                            },
                            child: Text(
                                AppLocalizations.of(context)!.click_here,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodyMedium!
                                    .copyWith(
                                        color: blueTextColor,
                                        fontWeight: FontWeight.bold)),
                          ),
                        ],
                      ),
                      Gap(
                        h: 8.h,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomNavigationBar: Container(
          color: white,
          height: 25.h,
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Align(
                    alignment: Alignment.center,
                    child: CircularCheckbox(
                        text: AppLocalizations.of(context)!
                            .i_agree_to_terms_and_conditions,
                        value: checkBoxValue,
                        onChanged: (agreed) => {
                              setState(() {
                                checkBoxValue = agreed;
                              })
                            })),
                Gap(
                  h: 2.h,
                ),
                CustomButton(
                  buttonText: AppLocalizations.of(context)!.continue_,
                  borderRadius: 12,
                  onPressed: checkBoxValue ? onContinuePressed : null,
                )
              ],
            ),
          ),
        ));
  }

  onContinuePressed() {
    if (!isPhoneValid) {
      return;
    }
    context.push(path.otp, extra: {"phoneNumber": _phoneEditingController});
  }
}
