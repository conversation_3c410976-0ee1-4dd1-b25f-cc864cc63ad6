// import 'package:flutter/material.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:go_router/go_router.dart';
// import 'package:intl_phone_field/phone_number.dart';
// import 'package:responsive_sizer/responsive_sizer.dart';
// import 'package:yalla_gai_driver/core/utils/images.dart';

// import '../../../../core/shared_widgets/custom_button.dart';
// import '../../../../core/routes/paths.dart' as path;
// import '../../../../core/shared_widgets/gap.dart';
// import '../../../../core/utils/colors.dart';
// import 'package:intl_phone_field/intl_phone_field.dart';

// class LoginPage extends StatefulWidget {
//   const LoginPage({super.key});

//   @override
//   _LoginPageState createState() => _LoginPageState();
// }

// class _LoginPageState extends State<LoginPage> {
//   final _formKey = GlobalKey<FormState>();
//   String _phoneEditingController = "";
//   bool checkBoxValue = false;
//   bool isPhoneValid = false;

//   @override
//   void initState() {
//     super.initState();
//     // getDeviceInformation();
//   }

//   // void getDeviceInformation() async {
//   //   if (!mounted) return;
//   //   final deviceInfo = await platformState();
//   //   deviceInfo.fold((l) {
//   //     print(l.errorMessage);
//   //   }, (r) {
//   //     // print(r['model']);
//   //     // print(r);
//   //   });
//   // }

//   Future<String?> validatePhoneNumber(PhoneNumber? phoneNumber) async {
//     if (phoneNumber == null || phoneNumber.completeNumber.isEmpty) {
//       return 'Phone number is required';
//     } else if (phoneNumber.number.characters.first == '0') {
//       return 'first number can not be 0';
//     } else if (phoneNumber.number.length != 9) {
//       return 'number length should be 9';
//     }

//     return null;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       backgroundColor: white,
//       body: SafeArea(
//         child: SingleChildScrollView(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: <Widget>[
//               Container(
//                 height: 65.h,
//                 decoration: BoxDecoration(
//                   gradient: const LinearGradient(
//                     begin: Alignment.topCenter,
//                     end: Alignment.bottomCenter,
//                     colors: [
//                       gradientLightColor,
//                       gradientDarkColor,
//                       primaryColorDark
//                     ],
//                   ),
//                   boxShadow: [
//                     BoxShadow(
//                       color: black.withOpacity(0.5),
//                       offset: const Offset(0, 1),
//                       blurRadius: 0.4.h,
//                       spreadRadius: 0.1.h,
//                     ),
//                   ],
//                   borderRadius: BorderRadius.only(
//                     bottomLeft: Radius.circular(10.w),
//                     bottomRight: Radius.circular(10.w),
//                   ),
//                 ),
//                 width: 100.w,
//                 child: Column(
//                   children: [
//                     Gap(h: 10.h),
//                     Align(
//                       alignment: Alignment.centerLeft,
//                       child: Padding(
//                         padding: EdgeInsets.only(left: 10.w),
//                         child: Image.asset(
//                           yallaGaiLogo,
//                           width: 40.w,
//                         ),
//                       ),
//                     ),
//                     Gap(h: 5.h),
//                     Align(
//                       alignment: Alignment.topRight,
//                       child: Container(
//                         width: 15.w,
//                         height: 5.h,
//                         decoration: BoxDecoration(
//                           color: white,
//                           borderRadius: BorderRadius.only(
//                             topLeft: Radius.circular(3.w),
//                             bottomLeft: Radius.circular(3.w),
//                           ),
//                         ),
//                         child: Align(
//                           alignment: Alignment.centerRight,
//                           child: Container(
//                             height: 1.h,
//                             width: 4.w,
//                             decoration: BoxDecoration(
//                               color: secondaryColor,
//                               borderRadius: BorderRadius.only(
//                                 topLeft: Radius.circular(3.w),
//                                 bottomLeft: Radius.circular(3.w),
//                               ),
//                             ),
//                           ),
//                         ),
//                       ),
//                     ),
//                     Gap(h: 4.h),
//                     SvgPicture.asset(
//                       'assets/images/login_component.svg',
//                       colorFilter: const ColorFilter.mode(white, BlendMode.dst),
//                     ),
//                     Gap(h: 5.h),
//                     Align(
//                       alignment: Alignment.centerLeft,
//                       child: Padding(
//                         padding: EdgeInsets.only(left: 10.w),
//                         child: Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Text(
//                               "Welcome to",
//                               style: Theme.of(context)
//                                   .textTheme
//                                   .displayLarge!
//                                   .copyWith(
//                                       color: white,
//                                       fontFamily: 'Plus Jakarta Sans'),
//                             ),
//                             Gap(h: 2.h),
//                             Text(
//                               "Yalla Gai",
//                               style: Theme.of(context)
//                                   .textTheme
//                                   .displayLarge!
//                                   .copyWith(
//                                       color: white,
//                                       fontSize: 13.w,
//                                       fontWeight: FontWeight.bold,
//                                       fontFamily: 'Plus Jakarta Sans'),
//                             ),
//                           ],
//                         ),
//                       ),
//                     )
//                   ],
//                 ),
//               ),
//               Gap(h: 4.h),
//               Align(
//                 alignment: Alignment.centerLeft,
//                 child: Padding(
//                   padding: EdgeInsets.only(left: 8.w),
//                   child: Text(
//                     'Have a great journey with YallaGai',
//                     textAlign: TextAlign.left,
//                     style: Theme.of(context)
//                         .textTheme
//                         .displayMedium!
//                         .copyWith(fontFamily: 'Plus Jakarta Sans'),
//                   ),
//                 ),
//               ),
//               Gap(h: 4.h),
//               Form(
//                 key: _formKey,
//                 child: Column(
//                   children: [
//                     SizedBox(
//                       width: 85.w,
//                       child: IntlPhoneField(
//                         disableLengthCheck: true,
//                         // invalidNumberMessage:
//                         //     'please do not use 0 in leading charachter',
//                         validator: validatePhoneNumber,
//                         textAlignVertical: TextAlignVertical.center,
//                         decoration: InputDecoration(
//                           focusColor: primaryColor,
//                           border: const UnderlineInputBorder(),
//                           label: Text("Mobile Number",
//                               style: Theme.of(context).textTheme.labelSmall),
//                         ),
//                         initialCountryCode: 'JO',
//                         flagsButtonPadding: const EdgeInsets.all(4),
//                         dropdownIconPosition: IconPosition.trailing,
//                         flagsButtonMargin:
//                             const EdgeInsets.only(right: 20, bottom: 0),
//                         dropdownDecoration: BoxDecoration(
//                           border: Border.all(color: black.withOpacity(0.4)),
//                           borderRadius: BorderRadius.circular(4),
//                         ),
//                         onChanged: (phone) {
//                           setState(() {
//                             try {
//                               isPhoneValid = phone.isValidNumber();
//                             } catch (e) {
//                               print('phone validation error');
//                             }
//                             if (phone.number.characters.first == '0') {
//                               isPhoneValid = false;
//                               // ScaffoldMessenger.of(context).showSnackBar(
//                               // const SnackBar(
//                               //     content: Text(
//                               //         'omit the first 0 from phone number')));
//                             }
//                             _phoneEditingController = phone.completeNumber;
//                           });
//                         },
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//       bottomNavigationBar: Container(
//         color: white,
//         height: 10.h,
//         child: Center(
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.start,
//             children: [
//               CustomButton(
//                 buttonText: "Next",
//                 borderRadius: 4.w,
//                 onPressed: isPhoneValid ? onContinuePressed : null,
//               )
//             ],
//           ),
//         ),
//       ),
//     );
//   }

//   onContinuePressed() {
//     if (!isPhoneValid) {
//       return;
//     }
//     context.push(path.otp, extra: {"phoneNumber": _phoneEditingController});
//   }
// }
