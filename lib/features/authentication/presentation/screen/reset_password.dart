import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../../../core/shared_widgets/custom_textfield.dart';
import '../../../../core/shared_widgets/divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class ResetPassword extends StatefulWidget {
  const ResetPassword({super.key});

  @override
  ResetPasswordState createState() => ResetPasswordState();
}

class ResetPasswordState extends State<ResetPassword> {
  var newPasswordController = TextEditingController();
  var confirmPasswordController = TextEditingController();
  bool checkBoxValue = false;
  bool isPhoneValid = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Gap(h: 6.h),
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: EdgeInsets.only(left: 8.w),
                  child: Text(AppLocalizations.of(context)!.reset_password,
                      textAlign: TextAlign.left,
                      style: Theme.of(context)
                          .textTheme
                          .displayMedium!
                          .copyWith(fontWeight: FontWeight.bold)),
                ),
              ),
              const Gap(),
              const CustomDivider(),
              const Gap(),
              CustomTextField(
                width: 85.5.w,
                labelText: AppLocalizations.of(context)!.enter_new_password,
                isPassword: true,
                validator: (v) => null,
                textEditingController: newPasswordController,
                borderRadius: 4,
              ),
              const Gap(),
              CustomTextField(
                width: 85.5.w,
                isPassword: true,
                labelText: AppLocalizations.of(context)!.confirm_password,
                validator: (v) => null,
                textEditingController: confirmPasswordController,
                borderRadius: 4,
              ),
              Gap(h: 4.h),
              CustomButton(
                width: 80.w,
                height: 7.h,
                buttonText: AppLocalizations.of(context)!.reset_password,
                borderRadius: 12,
                onPressed: onContinuePressed,
              ),
            ],
          ),
        ),
      ),
    );
  }

  onContinuePressed() {
    if (newPasswordController.text != confirmPasswordController.text) {
      showCustomSnackbar(
          context, AppLocalizations.of(context)!.passwords_should_be_the_same);
      return;
    }
    context.go(path.login);
  }
}
