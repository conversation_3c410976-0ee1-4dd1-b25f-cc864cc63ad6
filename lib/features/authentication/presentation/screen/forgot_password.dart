import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class ForgotPassword extends StatefulWidget {
  const ForgotPassword({super.key});

  @override
  ForgotPasswordState createState() => ForgotPasswordState();
}

class ForgotPasswordState extends State<ForgotPassword> {
  final _formKey = GlobalKey<FormState>();
  String _phoneEditingController = "";
  bool checkBoxValue = false;
  bool isPhoneValid = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Gap(h: 4.h),
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: EdgeInsets.only(left: 8.w),
                  child: Text(AppLocalizations.of(context)!.forgot_assword,
                      textAlign: TextAlign.left,
                      style: Theme.of(context)
                          .textTheme
                          .displayMedium!
                          .copyWith(fontWeight: FontWeight.bold)),
                ),
              ),
              const Gap(),
              const CustomDivider(),
              Gap(h: 6.h),
              Align(
                alignment: Alignment.centerLeft,
                child: Padding(
                  padding: EdgeInsets.only(left: 8.w),
                  child: Text(
                    AppLocalizations.of(context)!.enter_your_mobile_number,
                    textAlign: TextAlign.left,
                    style: Theme.of(context)
                        .textTheme
                        .displayMedium!
                        .copyWith(fontWeight: FontWeight.bold, fontSize: 20.sp),
                  ),
                ),
              ),
              Gap(h: 6.h),
              Form(
                key: _formKey,
                child: Column(
                  children: [
                    SizedBox(
                      width: 85.w,
                      child: IntlPhoneField(
                        textAlignVertical: TextAlignVertical.center,
                        decoration: InputDecoration(
                          focusColor: primaryColor,
                          border: const UnderlineInputBorder(),
                          label: Text(
                              AppLocalizations.of(context)!.mobile_number,
                              style: Theme.of(context).textTheme.labelSmall),
                        ),
                        initialCountryCode: 'JO',
                        flagsButtonPadding: const EdgeInsets.all(4),
                        dropdownIconPosition: IconPosition.trailing,
                        flagsButtonMargin:
                            const EdgeInsets.only(right: 20, bottom: 0),
                        dropdownDecoration: BoxDecoration(
                          border: Border.all(color: black.withOpacity(0.4)),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        onChanged: (phone) {
                          // Handle phone number changes

                          setState(() {
                           
                              isPhoneValid = phone.isValidNumber();
                        

                            _phoneEditingController = phone.completeNumber;
                          });
                        },
                      ),
                    ),
                    Gap(
                      h: 3.h,
                    ),
                    CustomButton(
                      width: 80.w,
                      height: 7.h,
                      borderRadius: 12,
                      onPressed: onContinuePressed,
                      buttonText: AppLocalizations.of(context)!.continue_,
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  onContinuePressed() {
    if (!isPhoneValid) {
      return;
    }
    context.push(path.otp, extra: {
      "phoneNumber": _phoneEditingController,
      'isForgotPassword': true
    });
  }
}
