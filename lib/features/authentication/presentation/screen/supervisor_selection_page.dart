import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';

import '../../../../core/routes/paths.dart' as path;

class TransisionPageFromLogin extends StatelessWidget {
  const TransisionPageFromLogin({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 4.w),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Card(
              // alignment: Alignment.center,
              // decoration: BoxDecoration(
              //   borderRadius: BorderRadius.circular(20),
              //   border: Border.all()
              elevation: 2,
              color: white,
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.h),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Image(
                      height: 65.w,
                      width: 65.w,
                      image: const AssetImage(groupArt),
                    ),
                    const Gap(),
                    const Divider(),
                    Text(
                      AppLocalizations.of(context)!
                          .you_must_go_to_an_agent_to_charge_the_account_to_accept_it_please_do_give_id_number_phone_number,
                      style: const TextStyle(fontWeight: FontWeight.w700, fontSize: 18),
                    )
                  ],
                ),
              ),
              // ),
            ),
            Gap(h: 8.h),
            InkWell(
              onTap: () {
                context.push(path.supervisor);
              },
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 5.w),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  color: primaryColor,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    const Icon(Icons.phone, color: white),
                    const Gap(w: 12),
                    Text(
                      AppLocalizations.of(context)!.contact_the_supervisor,
                      style: const TextStyle(color: white, fontSize: 20),
                    )
                  ],
                ),
              ),
            ),
            Gap(h: 10.h)
          ],
        ),
      ),
    );
  }
}
