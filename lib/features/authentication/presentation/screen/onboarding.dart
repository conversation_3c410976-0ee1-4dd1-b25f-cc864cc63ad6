import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';

class Onboarding extends StatefulWidget {
  const Onboarding({super.key});

  @override
  State<Onboarding> createState() => _OnboardingState();
}

class _OnboardingState extends State<Onboarding> with TickerProviderStateMixin {
  List<OnboardingItem> onboardingItems = [];

  int currentIndex = 0;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  SharedPreferences? preferences;

  init() async {
    preferences = await SharedPreferences.getInstance();
    setState(() {});
  }

  @override
  void initState() {
    init();
    super.initState();

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.decelerate,
      ),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _changePage() {
    _animationController.reverse().then((_) {
      setState(() {
        if (currentIndex == onboardingItems.length - 1) {
          currentIndex = 0;
        } else {
          currentIndex++;
        }
      });
      _animationController.forward();
    });
  }

  @override
  Widget build(BuildContext context) {
    onboardingItems = [
      OnboardingItem(
        backgroundImage: onboardingImageOne,
        text: AppLocalizations.of(context)!.recent_rides,
      ),
      OnboardingItem(
        backgroundImage: onboardingImageTwo,
        text: AppLocalizations.of(context)!.real_time_tracking,
      ),
      OnboardingItem(
        backgroundImage: onboardingImageThree,
        text: AppLocalizations.of(context)!.reliable_service,
        isLastPage: true,
      ),
    ];
    return Scaffold(
      body: Stack(
        children: [
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _fadeAnimation,
              builder: (context, child) {
                return Container(
                  color: black,
                  child: Opacity(
                    opacity: _fadeAnimation.value,
                    child: Image.asset(
                      onboardingItems[currentIndex].backgroundImage,
                      fit: BoxFit.cover,
                    ),
                  ),
                );
              },
            ),
          ),
          Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              if (currentIndex != 2)
                SafeArea(
                  bottom: false,
                  minimum: EdgeInsets.all(6.w),
                  child: Align(
                    alignment: AlignmentDirectional.centerEnd,
                    child: GestureDetector(
                      onTap: () async {
                        LocalStorage.instance.isFirstTime = false;
                        context.go(path.login);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 5.w, vertical: 0.8.h),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(14),
                          color: white.withOpacity(0.4),
                        ),
                        child: Text(AppLocalizations.of(context)!.skip,
                            style: Theme.of(context).textTheme.labelSmall!.copyWith(color: white)),
                      ),
                    ),
                  ),
                ),
              const Spacer(),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Column(
                    children: [
                      Text(
                          maxLines: 2,
                          onboardingItems[currentIndex].text.split(' ')[0],
                          style: Theme.of(context).textTheme.displayLarge!.copyWith(color: white)),
                      SizedBox(height: 1.h),
                      Text(
                          maxLines: 2,
                          onboardingItems[currentIndex].text.split(' ')[1],
                          style: Theme.of(context).textTheme.displayLarge!.copyWith(color: white)),
                    ],
                  ),
                  GestureDetector(
                    onTap: () async {
                      if (onboardingItems[currentIndex].isLastPage) {
                        LocalStorage.instance.isFirstTime = false;
                        context.go(path.login);
                      } else {
                        _changePage();
                      }
                    },
                    child: Container(
                      width: 70,
                      height: 70,
                      decoration: const BoxDecoration(
                        color: white,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.arrow_forward,
                        color: black,
                      ),
                    ),
                  ),
                ],
              ),
              SizedBox(height: 6.h),
            ],
          ),
        ],
      ),
    );
  }
}

class OnboardingItem {
  final String backgroundImage;
  final String text;
  final bool isLastPage;

  OnboardingItem({
    required this.backgroundImage,
    required this.text,
    this.isLastPage = false,
  });
}
