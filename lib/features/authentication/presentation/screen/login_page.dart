import 'dart:developer';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl_phone_field/countries.dart';
import 'package:intl_phone_field/country_picker_dialog.dart';
import 'package:intl_phone_field/intl_phone_field.dart';
import 'package:intl_phone_field/phone_number.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/features/account_type/domain/entity/device_info.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/links/links_cubit.dart';
import 'package:yalla_gai_driver/features/authentication/presentation/widgets/confirm_phone_dialog.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../repository/auth_repository.dart';

// import 'dart:nativewrappers/_internal/vm/lib/core_patch.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late final FocusNode _focusNode;
  final _formKey = GlobalKey<FormState>();
  String _phoneEditingController = "";
  bool checkBoxValue = false;
  bool isPhoneValid = false;
  PhoneNumber phoneNumber =
      PhoneNumber(countryCode: "", countryISOCode: "", number: "");

  @override
  void initState() {
    _getDeviceId();
    _focusNode = FocusNode();
    super.initState();
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  String deviceId = '';

  _getDeviceId() async {
    deviceId = await getDeviceId();
    print("Device id: $deviceId");
    context.read<LinksCubit>().fetchLinks();
  }

  Future<String?> validatePhoneNumber(
    PhoneNumber? phoneNumber,
    BuildContext context,
  ) async {
    setState(() {
      isPhoneValid = false;
    });
    final number = phoneNumber?.number.split(" ").join("");
    log(number ?? "");
    if (phoneNumber == null || phoneNumber.completeNumber.isEmpty) {
      return AppLocalizations.of(context)!.phone_number_is_required;
    } else if (!(number?.length == (country.minLength) ||
        number?.length == (country.maxLength))) {
      if (country.maxLength == country.minLength) {
        return "${AppLocalizations.of(context)!.number_length_should_be} ${country.minLength}";
      }
      return "${AppLocalizations.of(context)!.number_length_should_be} ${country.minLength} ${AppLocalizations.of(context)!.or} ${country.maxLength}";
    }
    setState(() {
      isPhoneValid = true;
    });
    return null;
  }

  Country country = const Country(
    name: "name",
    flag: "flag",
    code: "code",
    dialCode: "dialCode",
    nameTranslations: {},
    minLength: 9,
    maxLength: 10,
  );
  final countries = CountryManager().countries;
  late CountryWithPhoneCode? userCountryInfo = countries
      .where((e) => e.countryCode.toLowerCase() == "JO".toLowerCase())
      .firstOrNull;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: white,
      body: GestureDetector(
        onTap: () => FocusScope.of(context).unfocus(),
        child: SafeArea(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  padding: EdgeInsets.only(bottom: 2.h),
                  constraints: BoxConstraints(minHeight: 6.h),
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        gradientLightColor,
                        gradientDarkColor,
                        primaryColorDark,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: black.withOpacity(0.5),
                        offset: const Offset(0, 1),
                        blurRadius: 0.4.h,
                        spreadRadius: 0.1.h,
                      ),
                    ],
                    borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(10.w),
                      bottomRight: Radius.circular(10.w),
                    ),
                  ),
                  width: 100.w,
                  child: Column(
                    children: [
                      Gap(h: 7.h),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: EdgeInsets.only(left: 10.w),
                          child: Image.asset(
                            yallaGaiLogo,
                            width: 40.w,
                          ),
                        ),
                      ),
                      Gap(h: 5.h),
                      Align(
                        alignment: Alignment.topRight,
                        child: Container(
                          width: 15.w,
                          height: 5.h,
                          decoration: BoxDecoration(
                            color: white,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(3.w),
                              bottomLeft: Radius.circular(3.w),
                            ),
                          ),
                          child: Align(
                            alignment: Alignment.centerRight,
                            child: Container(
                              height: 1.h,
                              width: 4.w,
                              decoration: BoxDecoration(
                                color: secondaryColor,
                                borderRadius: BorderRadius.only(
                                  topLeft: Radius.circular(3.w),
                                  bottomLeft: Radius.circular(3.w),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),
                      Gap(h: 4.h),
                      SvgPicture.asset(
                        'assets/images/login_component.svg',
                        colorFilter:
                            const ColorFilter.mode(white, BlendMode.dst),
                      ),
                      Gap(h: 5.h),
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: EdgeInsets.only(left: 10.w),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                AppLocalizations.of(context)!.welcome_to,
                                style: Theme.of(context)
                                    .textTheme
                                    .displayLarge!
                                    .copyWith(
                                      color: white,
                                      fontFamily: 'Plus Jakarta Sans',
                                    ),
                              ),
                              Gap(h: 2.h),
                              Text(
                                AppLocalizations.of(context)!.yalla_gai,
                                style: Theme.of(context)
                                    .textTheme
                                    .displayLarge!
                                    .copyWith(
                                      color: white,
                                      fontSize: 13.w,
                                      fontWeight: FontWeight.bold,
                                      fontFamily: 'Plus Jakarta Sans',
                                    ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Gap(h: 4.h),
                Align(
                  alignment: Alignment.centerLeft,
                  child: Padding(
                    padding: EdgeInsets.only(left: 8.w),
                    child: Text(
                      AppLocalizations.of(context)!
                          .have_a_great_journey_with_yallaGai,
                      textAlign: TextAlign.left,
                      style: Theme.of(context)
                          .textTheme
                          .displayMedium!
                          .copyWith(fontFamily: 'Plus Jakarta Sans'),
                    ),
                  ),
                ),
                Gap(h: 4.h),
                Form(
                  key: _formKey,
                  child: Column(
                    children: [
                      SizedBox(
                        width: 85.w,
                        child: Directionality(
                          textDirection: TextDirection.ltr,
                          child: IntlPhoneField(
                            disableLengthCheck: true,
                            autofocus: false,
                            focusNode: _focusNode,
                            languageCode:
                                Localizations.localeOf(context).languageCode,
                            pickerDialogStyle: PickerDialogStyle(
                              searchFieldInputDecoration: InputDecoration(
                                hintText: Utils.appText(
                                  context: context,
                                  text: "Search Country",
                                  arabicText: "بحث البلد",
                                ),
                              ),
                            ),
                            // invalidNumberMessage:
                            //     'please do not use 0 in leading charachter',
                            validator: (value) =>
                                validatePhoneNumber(value, context),
                            textAlignVertical: TextAlignVertical.center,
                            decoration: InputDecoration(
                              focusColor: primaryColor,
                              border: const UnderlineInputBorder(),
                              label: Text(
                                userCountryInfo?.exampleNumberMobileNational ??
                                    "************",
                                style: Theme.of(context).textTheme.labelSmall,
                              ),
                            ),
                            initialCountryCode: 'JO',
                            onCountryChanged: (value) {
                              phoneNumber.countryCode = value.fullCountryCode;
                              userCountryInfo = countries
                                  .where(
                                    (e) =>
                                        e.countryCode.toLowerCase() ==
                                        value.code.toLowerCase(),
                                  )
                                  .firstOrNull;
                              print(
                                "onCountryChanged => ${value.code} $userCountryInfo",
                              );
                              log(value.code);
                              country = value;
                              setState(() {});
                            },
                            flagsButtonPadding: const EdgeInsets.all(4),
                            dropdownIconPosition: IconPosition.trailing,
                            flagsButtonMargin:
                                const EdgeInsets.only(right: 20, bottom: 0),
                            dropdownDecoration: BoxDecoration(
                              border: Border.all(color: black.withOpacity(0.4)),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            inputFormatters: [
                              if (userCountryInfo != null)
                                LibPhonenumberTextFormatter(
                                  phoneNumberType: PhoneNumberType.mobile,
                                  phoneNumberFormat:
                                      PhoneNumberFormat.international,
                                  country: userCountryInfo!,
                                  inputContainsCountryCode: false,
                                  shouldKeepCursorAtEndOfInput: false,
                                  additionalDigits: 1,
                                ),
                            ],
                            onChanged: (phone) {
                              setState(() {
                                //   try {
                                //     isPhoneValid = phone.isValidNumber();
                                //   } catch (e) {}
                                // if (phone.number.characters.first == '0') {
                                //   isPhoneValid = false;
                                // ScaffoldMessenger.of(context).showSnackBar(
                                // const SnackBar(
                                //     content: Text(
                                //         'omit the first 0 from phone number')));
                                // }
                                phoneNumber = phone;
                              });
                            },
                          ),
                        ),
                      ),
                      const Gap(h: 12),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 6.w),
                        child: Text.rich(
                          TextSpan(
                            children: [
                              TextSpan(
                                text: AppLocalizations.of(context)!
                                    .terms_first_part,
                              ),
                              const TextSpan(text: " "),
                              TextSpan(
                                text: AppLocalizations.of(context)!
                                    .terms_of_service,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue,
                                  decoration: TextDecoration.underline,
                                  decorationColor: Colors.blue,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    final link = context
                                        .read<LinksCubit>()
                                        .links
                                        .where(
                                          (element) =>
                                              element.type ==
                                              "terms_conditions",
                                        )
                                        .firstOrNull;
                                    if (link != null)
                                      launchUrlString(link.link);
                                  },
                              ),
                              const TextSpan(text: " "),
                              TextSpan(text: AppLocalizations.of(context)!.and),
                              const TextSpan(text: " "),
                              TextSpan(
                                text: AppLocalizations.of(context)!
                                    .privacy_policy,
                                style: const TextStyle(
                                  fontWeight: FontWeight.w600,
                                  color: Colors.blue,
                                  decoration: TextDecoration.underline,
                                  decorationColor: Colors.blue,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    final link = context
                                        .read<LinksCubit>()
                                        .links
                                        .where(
                                          (element) =>
                                              element.type == "privacy_policy",
                                        )
                                        .firstOrNull;
                                    if (link != null)
                                      launchUrlString(link.link);
                                  },
                              ),
                              TextSpan(
                                text: AppLocalizations.of(context)!
                                    .terms_last_part,
                              ),
                            ],
                          ),
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ],
                  ),
                ),
                Gap(h: 5.h),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: white,
        height: 10.h,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              BlocConsumer<AuthBloc, AuthState>(
                listener: (context, state) async {
                  if (state is AuthSendOtpFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                  if (state is AuthSendOtpSuccess) {
                    if (state.user == null) {
                      context.push(
                        path.otp,
                        extra: {"phoneNumber": _phoneEditingController},
                      );
                    } else {
                      final driverUser = state.user;
                      if (driverUser?.status?.toLowerCase()
                          case "pending" || null) {
                        if (driverUser?.fullName case "" || null) {
                          await AuthRepository().getProfile();
                          context.go(path.setProfile);
                        } else {
                          context.go(path.transitionPage);
                        }
                      } else {
                        context.go(path.home);
                      }
                    }
                  }
                },
                builder: (context, state) {
                  return CustomButton(
                    isLoading: state is AuthSendOtpLoading ? true : false,
                    buttonText: AppLocalizations.of(context)!.next,
                    borderRadius: 4.w,
                    onPressed: isPhoneValid ? onContinuePressed : null,
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  onContinuePressed() async {
    _focusNode.unfocus();
    if (!isPhoneValid) {
      return;
    }
    if (phoneNumber.number.characters.first == "0") {
      phoneNumber.number = phoneNumber.number.substring(1);
    }
    _phoneEditingController = phoneNumber.completeNumber;
    _phoneEditingController = _phoneEditingController.split(" ").join();
    if (!_phoneEditingController.contains("+")) {
      _phoneEditingController = "+$_phoneEditingController";
    }
    bool result = await showDialog<bool>(
          context: context,
          builder: (context) {
            return ConfirmPhoneDialog(
              phoneNumber: phoneNumber.number,
              countryCode: phoneNumber.countryCode,
            );
          },
        ) ??
        false;
    FocusManager.instance.primaryFocus?.unfocus();
    if (result) {
      // ignore: use_build_context_synchronously
      BlocProvider.of<AuthBloc>(context).add(
        AuthSendOtp(
          phoneNumber: _phoneEditingController,
          deviceId: deviceId,
        ),
      );
      _focusNode.unfocus();
    } else {
      return;
    }
    // context.push(path.otp, extra: {"phoneNumber": _phoneEditingController});
  }
}
