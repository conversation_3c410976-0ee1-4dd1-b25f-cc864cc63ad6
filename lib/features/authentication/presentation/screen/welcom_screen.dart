import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/black_logo.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class WelcomePage extends StatelessWidget {
  const WelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned(
            bottom: 100,
            right: 30,
            child: SvgPicture.asset(welcomeRandomShapes),
          ),
          Positioned(
            bottom: 0,
            child: SvgPicture.asset(welcomeBigArc),
          ),
          Positioned(
            right: 0,
            top: 100,
            child: SvgPicture.asset(welcomeSmallCircle),
          ),
          Positioned(
            left: 40,
            bottom: 250,
            child: SvgPicture.asset(tinyRedCircle),
          ),
          Padding(
            padding: EdgeInsets.only(top: 3.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Gap(h: 2.h),
                const BlackLogo(),
                SvgPicture.asset(
                  height: 236,
                  width: 162,
                  welcomeIllustration,
                ),
                SizedBox(height: 3.h),
                Text(
                  AppLocalizations.of(context)!.welcome_to_yalla_gai,
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(
                      color: black,
                      fontWeight: FontWeight.bold,
                      fontFamily: 'Plus Jakarta Sans'),
                ),
                SizedBox(height: 2.h),
                const CustomDivider(),
                SizedBox(height: 6.h),
                CustomButton(
                    buttonText: AppLocalizations.of(context)!.login,
                    borderRadius: 14,
                    height: 8.h,
                    width: 55.w,
                    onPressed: () {
                      context.push(path.login);
                    }),
                SizedBox(height: 4.h),
                CustomButton(
                    buttonText: AppLocalizations.of(context)!.sign_up,
                    borderRadius: 14,
                    borderOnly: true,
                    height: 8.h,
                    width: 55.w,
                    onPressed: () {
                      context.push(path.signUp);
                    }),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
