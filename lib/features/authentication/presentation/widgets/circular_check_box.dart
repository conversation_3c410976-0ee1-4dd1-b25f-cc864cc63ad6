import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';

class CircularCheckbox extends StatefulWidget {
  final String text;
  final bool value;
  final Function(bool) onChanged;

  const CircularCheckbox({
    super.key,
    required this.text,
    required this.value,
    required this.onChanged,
  });

  @override
  CircularCheckboxState createState() => CircularCheckboxState();
}

class CircularCheckboxState extends State<CircularCheckbox> {
  bool _value = false;

  @override
  void initState() {
    super.initState();
    _value = widget.value;
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        setState(() {
          _value = !_value;
          widget.onChanged(_value);
        });
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Checkbox(
            value: _value,
            onChanged: (value) {
              setState(() {
                _value = value!;
                widget.onChanged(_value);
              });
            },
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.0),
            ),
            activeColor: primaryColor,
          ),
          Gap(w: 1.w),
          SizedBox(
            width: MediaQuery.sizeOf(context).width * 0.8,
            child: Text(widget.text,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontWeight: FontWeight.w500,
                      color: grey,
                    )),
          ),
        ],
      ),
    );
  }
}
