import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_button.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';

class ConfirmPhoneDialog extends StatelessWidget {
  final String phoneNumber;
  final String countryCode;

  const ConfirmPhoneDialog({
    super.key,
    required this.phoneNumber,
    required this.countryCode,
  });

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        width: 90.w,
        height: 30.h,
        child: Material(
          color: Colors.white,
          borderRadius: BorderRadius.circular(2.w),
          child: Padding(
            padding: EdgeInsets.all(2.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(
                  Utils.appText(
                    context: context,
                    text: "Is this your correct phone number?",
                    arabicText: "هل هذا هو رقم هاتفك الصحيح",
                  ),
                  // AppLocalizations.of(context)!
                  //     .have_a_great_journey_with_yallaGai,
                  // "is your correct phone number?\n($countryCode)$phoneNumber",
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(fontFamily: 'Plus Jakarta Sans'),
                ),
                Text(
                  "($countryCode) $phoneNumber",
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.titleLarge!.copyWith(fontFamily: 'Plus Jakarta Sans'),
                  textDirection: TextDirection.ltr,
                ),
                CustomButton(
                  buttonText: AppLocalizations.of(context)!.yes,
                  borderRadius: 4.w,
                  onPressed: () {
                    Navigator.pop(context, true);
                  },
                ),
                CustomButton(
                  buttonText: AppLocalizations.of(context)!.no,
                  borderRadius: 4.w,
                  color: secondaryColor,
                  onPressed: () {
                    Navigator.pop(context, false);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
