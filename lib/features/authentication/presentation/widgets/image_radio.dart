import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';

class ImageRadio extends StatelessWidget {
  final String imagePath;
  final int value;
  final int groupValue;
  final String label;
  void Function(int?) onChanged;

  ImageRadio({
    super.key,
    required this.imagePath,
    required this.value,
    required this.groupValue,
    required this.label,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onChanged(value);
      },
      child: Column(
        children: [
          SvgPicture.asset(
            imagePath,
          ),
          const Gap(h: 5),
          Text(label,
              style: Theme.of(context)
                  .textTheme
                  .labelSmall!
                  .copyWith(fontFamily: "Plus Jakarta Sans", color: black)),
          Radio(
            value: value,
            groupValue: groupValue,
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }
}
