import 'package:flutter/material.dart';
import '../../../../core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/shared_widgets/custom_divider.dart';

class TextUnderLine extends StatelessWidget {
  final String text;

  const TextUnderLine({super.key, required this.text});

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      Padding(
        padding: EdgeInsets.only(
            left: Utils.isAribic(context) ? 0 : 9.w,
            right: Utils.isAribic(context) ? 9.w : 0.w),
        child: <PERSON>gn(
            alignment: Utils.isAribic(context)
                ? Alignment.centerRight
                : Alignment.centerLeft,
            child: Text(text,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(color: grey))),
      ),
      const CustomDivider(indent: 40)
    ]);
  }
}
