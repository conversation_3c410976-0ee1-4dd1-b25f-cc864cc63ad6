import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

class CustomDropDown extends StatefulWidget {
  final TextEditingController controller;
  final List<String> list;
  const CustomDropDown(
      {super.key, required this.controller, required this.list});

  @override
  CustomDropDownState createState() => CustomDropDownState();
}

class CustomDropDownState extends State<CustomDropDown> {
  final _formKey = GlobalKey<FormState>();
  // String selected = 'Arial';
  late String selected;
  @override
  void initState() {
    super.initState();
    // widget.controller.text = selected;
  }

  @override
  Widget build(BuildContext context) {
    selected = widget.list[0];
    widget.controller.text = selected;

    return Form(
      key: _formKey,
      child: Column(
        children: <Widget>[
          Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16.0),
                // decoration: BoxDecoration(
                //   border: const Border(
                //     bottom: BorderSide(
                //       color: Colors.black, // Set the color of the border
                //       width: 1.0, // Set the width of the border
                //     ),
                //   ),
                //   borderRadius: BorderRadius.circular(8.0),
                // ),
                child: DropdownButtonHideUnderline(
                  child: DropdownButton<String>(
                    value: selected,
                    alignment: Alignment.bottomLeft,
                    menuMaxHeight: 60.h,
                    iconSize: 40,
                    elevation: 0,
                    isDense: true,
                    iconEnabledColor: Colors.grey,
                    isExpanded: true,
                    onChanged: (newValue) {
                      setState(() {
                        selected = newValue ?? '';
                        widget.controller.text = newValue ?? '';
                      });
                    },
                    items: widget.list.map((String item) {
                      return DropdownMenuItem<String>(
                        value: item,
                        child: Text(
                          item,
                          style: Theme.of(context)
                              .textTheme
                              .bodySmall!
                              .copyWith(color: Colors.grey),
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
              const Divider()
            ],
          ),
        ],
      ),
    );
  }
}
