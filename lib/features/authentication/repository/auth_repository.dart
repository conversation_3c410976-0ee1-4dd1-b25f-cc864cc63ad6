import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:http/http.dart' as http;
import 'package:http/http.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/error/exception.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/model/driver_profile.dart';
import 'package:yalla_gai_driver/features/authentication/model/link_model.dart';
import 'package:yalla_gai_driver/features/authentication/model/user.dart';
import 'package:yalla_gai_driver/features/booking/repository/trip_repository.dart';

import '../../../core/constants/end_points.dart';
import '../model/general_info.dart';
import '../model/language.dart';

DriverProfile globalDriverProfile = DriverProfile();

class AuthRepository {
  final _httpClient = HttpApiClient();

  AuthRepository();

  Future<User?> sendOtp(String phoneNumber, String deviceId) async {
    print("===>>>>>>> here send otp api call $deviceId");
    var response = await _httpClient.post(
      EndPoints.sendOtp,
      body: jsonEncode({
        'mobile_number': phoneNumber,
        'device_id': deviceId,
      }),
    );

    print("===>>>>>>> here send otp api call 2 $deviceId");
    Map<String, dynamic> responseData = jsonDecode(response.body);
    print("===>>>>>>> here send otp api call $responseData");
    if (responseData['access_token'] != null) {
      LocalStorage.instance.accessToken = responseData['access_token'];
      LocalStorage.instance.userId = responseData['data']['id'].toString();
      LocalStorage.instance.roleId = responseData['data']['role_id'].toString();
      LocalStorage.instance.setString("role", responseData['role'].toString());
      return User.fromJson(responseData['data']);
    }
    return null;
  }

/////////////////////
  ///  change phone number
  Future<bool> changePhoneNumber(String phoneNumber) async {
    var response = await _httpClient.post(
      EndPoints.changePhoneNumber,
      body: jsonEncode({'mobile_number': phoneNumber}),
    );

    Map<String, dynamic> responseData = json.decode(response.body);
    if (responseData['status'] != true) {
      return true;
    } else {
      final errors = responseData["message"];
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  Future<bool> changePhoneNumberVerify(String otp) async {
    var response = await _httpClient.post(
      EndPoints.verifyPhoneChange,
      body: jsonEncode({'otp': otp}),
    );

    Map<String, dynamic> responseData = json.decode(response.body);

    if (responseData['status'] != true) {
      return true;
    } else {
      final errors = responseData["message"];
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

/////////////////////
  ///  signup
  Future<User> verifyOtp(
      {required String otp,
      required String phoneNumber,
      required String deviceId,}) async {
    final response = await _httpClient.post(
      EndPoints.verifyOtp,
      body: jsonEncode({
        'mobile_number': phoneNumber,
        'otp': otp,
        'device_id': deviceId,
      }),
    );

    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (responseData['access_token'] != null) {
      LocalStorage.instance.accessToken = responseData['access_token'];
      LocalStorage.instance.roleId = responseData['data']['role_id'].toString();
      LocalStorage.instance.userId = responseData['data']['id'].toString();
      LocalStorage.instance.setString("role", responseData['role'].toString());
      return User.fromJson(responseData['data']);
    } else {
      final errors = responseData["message"];
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  ////////////////////////////
  /// signup
  Future<User> storeProfile(Profile profile) async {
    var request = MultipartRequest('POST', EndPoints.profileUpdate);

    request.files.add(
      http.MultipartFile(
        'profile_image',
        profile.profileImage.readAsBytes().asStream(),
        File(profile.profileImage.path).lengthSync(),
        filename: profile.profileImage.path.split("/").last,
      ),
    );
    for (int i = 0; i < profile.criminalRecord.length; i++) {
      var multipartFile = await MultipartFile.fromPath(
          'criminal_record_images[$i]', profile.criminalRecord[i]?.path ?? "",);
      request.files.add(multipartFile);
    }
    for (int i = 0; i < profile.driverIdImages.length; i++) {
      var multipartFile = await MultipartFile.fromPath(
          'driver_id_images[$i]', profile.driverIdImages[i]?.path ?? "",);
      request.files.add(multipartFile);
    }
    for (int i = 0; i < profile.carImages.length; i++) {
      var multipartFile = await MultipartFile.fromPath(
        'car_images[$i]',
        profile.carImages[i]?.path ?? "",
      );
      request.files.add(multipartFile);
    }

    for (int i = 0; i < profile.drivingLicences.length; i++) {
      var multipartFile = await http.MultipartFile.fromPath(
          'driving_licences_images[$i]',
          profile.drivingLicences[i]?.path ?? "",);
      request.files.add(multipartFile);
    }
    for (int i = 0; i < profile.driverCertificates.length; i++) {
      var multipartFile = await http.MultipartFile.fromPath(
        'driver_certificates_images[$i]',
        profile.driverCertificates[i]?.path ?? "",
      );
      request.files.add(multipartFile);
    }

    request.fields['_method'] = "POST";
    request.fields['full_name'] = profile.englishFullName;
    request.fields['arabic_full_name'] = profile.arabicFullName;
    request.fields['gender'] = profile.gender;
    request.fields['id_number'] = profile.idNumber;
    request.fields['car_model_id'] = profile.carIndustryId.toString();
    request.fields['car_model_brand_id'] = profile.carModelId.toString();
    request.fields['plate_number'] = profile.plateNumber;
    request.fields['year_of_manufacturer'] = profile.yearOfManufacturer;
    request.fields['date_of_birth'] = profile.dateOfBirth;
    request.fields['term_condition_id'] = "2";
    request.fields['privacy_policy_id'] = "1";
    request.fields['car_categories_ids'] = profile.carCategoryId.toString();
    request.fields['car_color'] = profile.carColor;

    var response = await _httpClient.send(request);
    var responseData = await response.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);
    Map<String, dynamic> valueMap = jsonDecode(responseString);

    getProfile();
    if (valueMap["status"] == false) {
      final errors = valueMap["errors"].values.toList();
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
    var user = User.fromJson(valueMap['data']);
    return user;
  }

  /// signup
  Future<bool> updateAccountType() async {
    await _httpClient.send(
        http.MultipartRequest('POST', EndPoints.profileUpdateAccountType),);
    return true;
  }

  ////////////////////////////
  /// signup
  Future<User> updateProfile(UpdateProfile updateProfile) async {
    var request = http.MultipartRequest('POST', EndPoints.profileUpdate);
    if (updateProfile.profileImage != null) {
      request.files.add(
        http.MultipartFile(
          'profile_image',
          updateProfile.profileImage!.readAsBytes().asStream(),
          File(updateProfile.profileImage!.path).lengthSync(),
          filename: updateProfile.profileImage!.path.split("/").last,
        ),
      );
    }

    request.fields['_method'] = "put";
    request.fields['arabic_first_name'] = updateProfile.arabicFirstName ?? "";
    request.fields['arabic_last_name'] = updateProfile.arabicLastName ?? "";
    request.fields['first_name'] = updateProfile.firstName ?? "";
    request.fields['last_name'] = updateProfile.lastName ?? "";

    var res = await _httpClient.send(request);
    var responseData = await res.stream.toBytes();
    var responseString = String.fromCharCodes(responseData);
    Map<String, dynamic> valueMap = jsonDecode(responseString);

    if (valueMap["status"] == false) {
      final errors = valueMap["errors"].values.toList();
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
    return User.fromJson(valueMap['data']);
  }

  Future<bool> changeAvaliablity(String status) async {
    final response = await _httpClient.post(
      EndPoints.changeAvaliablity,
      body: jsonEncode({"availibility_status": status}),
    );

    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (responseData["status"] == false) {
      final errors = responseData["errors"].values.toList();
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
    return true;
  }

  Future<DriverProfile> getProfile() async {
    final response = await _httpClient.get(EndPoints.getProfile);
      print("==>>> here socket connection 081");

    DriverProfile driverProfile =
        DriverProfile.fromJson(jsonDecode(response.body)["data"]);
    driverProfile.user?.avalabilityStatus =
        globalDriverProfile.user?.avalabilityStatus ?? "off";

    globalDriverProfile = driverProfile;

    LocalStorage.instance
        .setString("driver-profile", jsonEncode(driverProfile.toJson()));
    LocalStorage.instance.setString("name",
        "${driverProfile.user?.fullName ?? ""} ${driverProfile.user?.arabicFullName ?? ""}",);
    LocalStorage.instance
        .setString("phone_number", driverProfile.user?.mobileNumber ?? "");
    LocalStorage.instance
        .setString("profile_image", driverProfile.user?.profileImage ?? "");
    LocalStorage.instance
        .setString("rate", driverProfile.user?.driver?.plateNumber ?? "5");
    cityId = driverProfile.user?.cityId ?? '';
    LocalStorage.instance.setString("cityId", driverProfile.user?.cityId ?? '');
    LocalStorage.instance.setString(
        "plate_number", driverProfile.user?.driver?.plateNumber ?? "",);
    LocalStorage.instance.setString("availibility_status",
        driverProfile.user?.driver?.availibilityStatus ?? "",);
    return driverProfile;
  }

  DriverProfile? fetchDriverLocally() {
    final response = LocalStorage.instance.getString("driver-profile");
    if (response != null) {
      globalDriverProfile = DriverProfile.fromJson(jsonDecode(response));
      globalDriverProfile.changeAvailabilityStatus(false);
      return globalDriverProfile;
    }
    return null;
  }

  Future<UserTripInfo> getUserTripInformation() async {
    final response = await _httpClient.get(EndPoints.getUserTripInformation);
    return UserTripInfo.fromJson(jsonDecode(response.body));
  }

  Future<GeneralInformation> getGeneralInformation() async {
    final response = await _httpClient.get(EndPoints.getGeneralInformation);
    return GeneralInformation.fromJson(jsonDecode(response.body));
  }

  Future<List<LanguageData>> getLanguage() async {
    final response = await _httpClient.get(EndPoints.getLanguage);
    LocalStorage.instance.setString("cityId",
        jsonDecode(response.body)['data']['city_id']?.toString() ?? "0",);
    return (jsonDecode(response.body)['data'] as List)
        .map((e) => LanguageData.fromJson(e))
        .toList();
  }

  Future<bool> updateLanguage(String languageId) async {
    var response = await _httpClient.put(
      EndPoints.updateLanguage,
      body: jsonEncode({
        'language_id': languageId,
        'user_id': LocalStorage.instance.userId,
      }),
    );

    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (responseData["status"] == false) {
      final errors = responseData["errors"].values.toList();
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
    return true;
  }

  /// update language
  Future<List<LinkInfo>> getGeneralLinks() async {
    var response = await _httpClient.get(EndPoints.generalLinks('3'));
    Map<String, dynamic> responseData = jsonDecode(response.body);

    if (responseData["status"] == false) {
      final errors = responseData["errors"].values.toList();
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
    if (responseData["data"] is Iterable) {
      return List.from(responseData["data"])
          .map((e) => LinkInfo.fromJson(e))
          .toList();
    }
    return [];
  }

  Future<void> updateRateApp() async {
    await _httpClient.get(EndPoints.rateApp());
  }

  Future<bool> deleteAccount() async {
    final response = await _httpClient.get(EndPoints.deleteAccount);
    final responseData = jsonDecode(response.body);
    if (responseData["status"] == false) {
      final errors = responseData["errors"].values.toList();
      String message =
          errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }

    SharedPreferences preferences = await SharedPreferences.getInstance();
    preferences.clear();
    globalDriverProfile = DriverProfile();
    return true;
  }
}
