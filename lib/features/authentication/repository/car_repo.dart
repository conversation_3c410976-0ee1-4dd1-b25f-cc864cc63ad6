import 'dart:convert';
import 'dart:developer';

import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/booking/repository/trip_repository.dart';

import '../../../core/constants/end_points.dart';
import '../model/car_brand_model.dart';
import '../model/car_category_model.dart';
import '../model/car_model_model.dart';

class CarRepo {
  final _httpClient = HttpApiClient();

  /// get car company
  Future<List<CarBrandModel>> getCarBrands() async {
    final response = await _httpClient.get(EndPoints.getCarBrand);
    final responseData = jsonDecode(response.body);
    log(response.body);

    List<CarBrandModel> carBrand = [];
    for (var brands in responseData['data']) {
      carBrand.add(CarBrandModel.fromJson(brands));
    }
    return carBrand;
  }

  Future<List<CarModelModel>> getCarModel() async {
    final response = await _httpClient.get(EndPoints.getCarModel);
    final responseData = jsonDecode(response.body);
    List<CarModelModel> carModel = [];
    for (var model in responseData['data']) {
      carModel.add(CarModelModel.fromJson(model));
    }
    return carModel;
  }

  Future<List<CarCategoryModel>> getCarCategory() async {
    final response = await _httpClient.get(EndPoints.getCarCategory(cityId));
    final responseData = jsonDecode(response.body);

    List<CarCategoryModel> carCategory = [];
    for (var cat in responseData['data']) {
      carCategory.add(CarCategoryModel.fromJson(cat));
    }
    log("$responseData $cityId filtered${carCategory.where((e) => e.cityId != cityId && e.type! == "normal" && e.serviceId == null).toList()}");

    return carCategory;
  }
}
