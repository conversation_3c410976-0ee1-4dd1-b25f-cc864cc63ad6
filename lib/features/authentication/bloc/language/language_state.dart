part of 'language_bloc.dart';

abstract class LanguageState extends Equatable {
  const LanguageState();

  @override
  List<Object> get props => [];
}

final class LangauageInitial extends LanguageState {}

class LanguageGetLoading extends LanguageState {}

class LanguageGetSuccess extends LanguageState {
  final List<LanguageData> language;
  const LanguageGetSuccess({required this.language});
}

class LanguageGetFaulire extends LanguageState {
  final String message;
  const LanguageGetFaulire({required this.message});
}

class LanguageUpdateLoading extends LanguageState {}

class LanguageUpdateSuccess extends LanguageState {
  final bool success;
  const LanguageUpdateSuccess({required this.success});
}

class LanguageUpdateFaulire extends LanguageState {
  final String message;
  const LanguageUpdateFaulire({required this.message});
}
