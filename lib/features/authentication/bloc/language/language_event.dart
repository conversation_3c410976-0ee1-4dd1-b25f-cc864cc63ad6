part of 'language_bloc.dart';

abstract class LangauageEvent extends Equatable {
  const LangauageEvent();

  @override
  List<Object> get props => [];
}

class LanguageGet extends LangauageEvent {
  const LanguageGet();
  @override
  List<Object> get props => [];
}

class LanguageUpdate extends LangauageEvent {
  final String languageId;
  const LanguageUpdate({required this.languageId});
  @override
  List<Object> get props => [];
}
