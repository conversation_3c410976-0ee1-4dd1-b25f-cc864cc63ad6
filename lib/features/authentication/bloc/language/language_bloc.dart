import '../../model/language.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

part 'language_event.dart';
part 'language_state.dart';

class LanaguageBloc extends Bloc<LangauageEvent, LanguageState> {
  AuthRepository authRepository = AuthRepository();
  LanaguageBloc() : super(LangauageInitial()) {
    //////get language
    on<LanguageGet>((event, emit) async {
      try {
        emit(LanguageGetLoading());
        List<LanguageData> language = await authRepository.getLanguage();
        emit(LanguageGetSuccess(language: language));
      } catch (e) {
        emit(LanguageGetFaulire(message: e.toString()));
      }
    });

    //////get language
    on<LanguageUpdate>((event, emit) async {
      try {
        emit(LanguageUpdateLoading());
        final language = await authRepository.updateLanguage(event.languageId);
        emit(LanguageUpdateSuccess(success: language));
      } catch (e) {
        emit(LanguageUpdateFaulire(message: e.toString()));
      }
    });
  }
}
