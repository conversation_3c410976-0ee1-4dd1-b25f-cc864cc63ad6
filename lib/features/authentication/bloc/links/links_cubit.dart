import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:yalla_gai_driver/features/authentication/model/link_model.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

part 'links_state.dart';

class LinksCubit extends Cubit<LinksState> {
  AuthRepository authRepository;

  LinksCubit(this.authRepository) : super(LinksStateInitial());

  List<LinkInfo> links = [];

  Future fetchLinks() async {
    emit(LinksStateLoading());
    try {
      links = await authRepository.getGeneralLinks();
      emit(LinksStateLoaded());
    } catch (e) {
      log(e.toString());
      emit(LinksStateError());
    }
    return;
  }
}
