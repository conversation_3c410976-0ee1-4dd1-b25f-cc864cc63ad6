import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/authentication/model/user.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

import '../../model/driver_profile.dart';
import '../../model/general_info.dart';

part 'auth_event.dart';
part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  DriverProfile user = DriverProfile();
  AuthRepository authRepository = AuthRepository();

  AuthBloc() : super(AuthInitial()) {
    //////send otp
    on<AuthSendOtp>((event, emit) async {
      try {
        emit(AuthSendOtpLoading());
        print("===>>> here send otp api ");
        final user = await authRepository.sendOtp(event.phoneNumber, event.deviceId);
        emit(AuthSendOtpSuccess( user: user));
      } catch (e) {
        emit(AuthSendOtpFaulire(message: e.toString()));
      }
    });
    ////// re send otp
    on<AuthReSendOtp>((event, emit) async {
      try {
        emit(AuthReSendOtpLoading());
        final user = await authRepository.sendOtp(event.phoneNumber, event.deviceId);
        emit(AuthReSendOtpSuccess(message: user != null));
      } catch (e) {
        emit(AuthReSendOtpFaulire(message: e.toString()));
      }
    });
    //////verify otp
    on<AuthVerifyOtp>((event, emit) async {
      try {
        emit(AuthVerifyOtpLoading());
        final user =
            await authRepository.verifyOtp(otp: event.otp, deviceId: event.deviceId, phoneNumber: event.phoneNumeber);
        emit(AuthVerifyOtpSuccess(user: user));
      } catch (e) {
        emit(AuthVerifyOtpFaulire(message: e.toString()));
      }
    });
    //////AuthSetupProfile
    on<AuthSetupProfile>((event, emit) async {
      try {
        emit(AuthSetupProfileLoading());
        final user = await authRepository.storeProfile(event.profile);
        await authRepository.updateAccountType();
        emit(AuthSetupProfileSuccess(user: user));
      } catch (e) {
        emit(AuthSetupProfileFaulire(message: e.toString()));
      }
    });
    //////AuthUpdateProfile
    on<AuthUpdateProfile>((event, emit) async {
      try {
        emit(AuthUpdateProfileLoading());

        await authRepository.updateProfile(event.updateProfile);
        emit(const AuthUpdateProfileSuccess());
      } catch (e) {
        emit(AuthUpdateProfileFaulire(message: e.toString()));
      }
    });
    //////change phone number
    on<AuthChangePhoneNumber>((event, emit) async {
      try {
        emit(AuthChangePhoneNumberLoading());
        final user = await authRepository.changePhoneNumber(event.phoneNumber);
        emit(const AuthChangePhoneNumberOtpSentSuccess());
      } catch (e) {
        emit(AuthChangePhoneNumberFaulire(message: e.toString()));
      }
    });
    //////change phone number
    on<AuthChangePhoneVerify>((event, emit) async {
      try {
        emit(AuthChangePhoneNumberLoading());
        final user = await authRepository.changePhoneNumberVerify(event.otp);
        emit(AuthChangePhoneNumberSuccess(isSuccess: user));
      } catch (e) {
        emit(AuthChangePhoneNumberFaulire(message: e.toString()));
      }
    });
    //////change phone number
    on<AuthGetProfile>((event, emit) async {
      try {
        emit(AuthGetProfileLoading());
        user = await authRepository.getProfile();

        // CustomSocket.reSubscribeChannel(channelName: "un-ban-user_${user.user?.id}");
        // CustomSocket.reSubscribeChannel(channelName: "ban-user_${user.user?.id}");
        CustomSocket.reSubscribeChannel(channelName: "private-un-ban-user_${user.user?.id}");
        CustomSocket.reSubscribeChannel(channelName: "private-ban-user_${user.user?.id}");
        emit(AuthGetProfileSuccess(driverProfile: user));
      } catch (e) {
        emit(AuthGetProfileFaulire(message: e.toString()));
      }
    });
    //////AuthChangeAvaliablity
    on<AuthChangeAvaliablity>((event, emit) async {
      try {
        emit(AuthChangeAvaliablityLoading());
        final success = await authRepository.changeAvaliablity(event.status);
        emit(AuthChangeAvaliablitySuccess(message: success));
      } catch (e) {
        emit(AuthChangeAvaliablityFaulire(message: e.toString()));
      }
    });
    //////AuthGetInformation
    on<AuthGetInformation>((event, emit) async {
      try {
        emit(AuthGetInformationLoading());
        final generalInformation = await authRepository.getUserTripInformation();

        emit(AuthGetInformationSuccess(generalInformation: generalInformation));
      } catch (e) {
        emit(AuthGetInformationFaulire(message: e.toString()));
      }
    });

    //////DeleteUserAccount
    on<AuthDeleteAccount>((event, emit) async {
      try {
        emit(const AuthDeleteAccountLoading());
        final result = await authRepository.deleteAccount();
        if (result) {
          emit(const AuthDeleteAccountSuccess());
        } else {
          emit(const AuthDeleteAccountFailure(message: null));
        }
      } catch (e) {
        emit(AuthDeleteAccountFailure(message: e.toString()));
      }
    });
  }

//////get car brands
}
