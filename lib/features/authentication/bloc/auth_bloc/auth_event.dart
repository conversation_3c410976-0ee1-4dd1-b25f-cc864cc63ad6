part of 'auth_bloc.dart';

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object> get props => [];
}

class AuthSendOtp extends AuthEvent {
  final String phoneNumber;
  final String deviceId;

  const AuthSendOtp({required this.phoneNumber, required this.deviceId});

  @override
  List<Object> get props => [deviceId];
}

class AuthReSendOtp extends AuthEvent {
  final String phoneNumber;
  final String deviceId;

  const AuthReSendOtp({required this.phoneNumber, required this.deviceId});

  @override
  List<Object> get props => [deviceId];
}

class AuthVerifyOtp extends AuthEvent {
  final String otp;
  final String deviceId;
  final String phoneNumeber;

  const AuthVerifyOtp({required this.otp, required this.phoneNumeber, required this.deviceId});

  @override
  List<Object> get props => [deviceId];
}

class AuthSetupProfile extends AuthEvent {
  final Profile profile;

  const AuthSetupProfile({
    required this.profile,
  });

  @override
  List<Object> get props => [profile];
}

class AuthChangePhoneNumber extends AuthEvent {
  final String phoneNumber;

  const AuthChangePhoneNumber({
    required this.phoneNumber,
  });

  @override
  List<Object> get props => [phoneNumber];
}

class AuthChangePhoneVerify extends AuthEvent {
  final String otp;
  final String phoneNumber;

  const AuthChangePhoneVerify({
    required this.otp,
    required this.phoneNumber,
  });

  @override
  List<Object> get props => [otp, phoneNumber];
}

class AuthGetProfile extends AuthEvent {
  const AuthGetProfile();

  @override
  List<Object> get props => [];
}

class AuthChangeAvaliablity extends AuthEvent {
  final String status;

  const AuthChangeAvaliablity({required this.status});

  @override
  List<Object> get props => [];
}

class AuthGetInformation extends AuthEvent {
  const AuthGetInformation();

  @override
  List<Object> get props => [];
}

class AuthUpdateProfile extends AuthEvent {
  final UpdateProfile updateProfile;

  const AuthUpdateProfile({required this.updateProfile});

  @override
  List<Object> get props => [];
}

class UpdateProfile {
  final String? arabicFirstName;
  final String? arabicLastName;
  final String? firstName;
  final String? lastName;
  final File? profileImage;

  UpdateProfile(
      {required this.arabicFirstName,
      required this.arabicLastName,
      required this.firstName,
      required this.lastName,
      required this.profileImage});
}

class AuthDeleteAccount extends AuthEvent{
  const AuthDeleteAccount();
}
