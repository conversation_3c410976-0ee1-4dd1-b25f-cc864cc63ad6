part of 'auth_bloc.dart';

abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object> get props => [];
}

final class AuthInitial extends AuthState {}

class AuthSendOtpLoading extends AuthState {}

class AuthLoading extends AuthState {}

class AuthSendOtpSuccess extends AuthState {
  final User? user;

  const AuthSendOtpSuccess({this.user});
}

class AuthSendOtpFaulire extends AuthState {
  final String message;

  const AuthSendOtpFaulire({required this.message});
}

class AuthChangeAvaliablityLoading extends AuthState {}

class AuthChangeAvaliablitySuccess extends AuthState {
  final bool message;

  const AuthChangeAvaliablitySuccess({required this.message});
}

class AuthChangeAvaliablityFaulire extends AuthState {
  final String message;

  const AuthChangeAvaliablityFaulire({required this.message});
}

class AuthGetInformationLoading extends AuthState {}

class AuthGetInformationSuccess extends AuthState {
  final UserTripInfo generalInformation;

  const AuthGetInformationSuccess({required this.generalInformation});
}

class AuthGetInformationFaulire extends AuthState {
  final String message;

  const AuthGetInformationFaulire({required this.message});
}

class AuthReSendOtpLoading extends AuthState {}

class AuthReSendOtpSuccess extends AuthState {
  final bool message;

  const AuthReSendOtpSuccess({required this.message});
}

class AuthReSendOtpFaulire extends AuthState {
  final String message;

  const AuthReSendOtpFaulire({required this.message});
}

class AuthVerifyOtpLoading extends AuthState {}

class AuthVerifyOtpSuccess extends AuthState {
  final User user;

  const AuthVerifyOtpSuccess({required this.user});
}

class AuthVerifyOtpFaulire extends AuthState {
  final String message;

  const AuthVerifyOtpFaulire({required this.message});
}

class AuthSetupProfileLoading extends AuthState {}

class AuthSetupProfileSuccess extends AuthState {
  final User user;

  const AuthSetupProfileSuccess({required this.user});
}

class AuthSetupProfileFaulire extends AuthState {
  final String message;

  const AuthSetupProfileFaulire({required this.message});
}

class AuthChangePhoneNumberLoading extends AuthState {}

class AuthChangePhoneNumberSuccess extends AuthState {
  final bool isSuccess;

  const AuthChangePhoneNumberSuccess({required this.isSuccess});
}

class AuthChangePhoneNumberOtpSentSuccess extends AuthState {
  const AuthChangePhoneNumberOtpSentSuccess();
}

class AuthChangePhoneNumberFaulire extends AuthState {
  final String message;

  const AuthChangePhoneNumberFaulire({required this.message});
}

class AuthGetProfileLoading extends AuthState {}

class AuthGetProfileSuccess extends AuthState {
  final DriverProfile driverProfile;

  const AuthGetProfileSuccess({required this.driverProfile});
}

class AuthGetProfileFaulire extends AuthState {
  final String message;

  const AuthGetProfileFaulire({required this.message});
}

class AuthUpdateProfileLoading extends AuthState {}

class AuthUpdateProfileSuccess extends AuthState {
  const AuthUpdateProfileSuccess();
}

class AuthUpdateProfileFaulire extends AuthState {
  final String message;

  const AuthUpdateProfileFaulire({required this.message});
}

class AuthDeleteAccountLoading extends AuthState {
  const AuthDeleteAccountLoading();
}

class AuthDeleteAccountSuccess extends AuthState {
  const AuthDeleteAccountSuccess();
}

class AuthDeleteAccountFailure extends AuthState {
  final String? message;

  const AuthDeleteAccountFailure({required this.message});
}
