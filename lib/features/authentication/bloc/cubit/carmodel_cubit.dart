import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:yalla_gai_driver/features/authentication/model/car_brand_model.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

import '../../model/car_category_model.dart';
import '../../model/car_model_model.dart';
import '../../repository/car_repo.dart';

part 'carmodel_state.dart';

class CarmodelCubit extends Cubit<CarmodelState> {
  CarRepo carRepo;

  CarmodelCubit(this.carRepo) : super(CarmodelInitial());

  List<CarBrandModel> allCarBrands = [];

  Future fetchCarBrands() async {
    emit(CarmodelLoading());
    try {
      allCarBrands = await carRepo.getCarBrands();
      emit(CarmodelLoaded());
      fetchCarModel();
    } catch (e) {
      log(e.toString());
      emit(CarmodelError());
    }
    return;
  }

  CarBrandModel selectedCarBrand = CarBrandModel();

  selectCarBrands({CarBrandModel? car}) async {
    emit(CarmodelLoading());
    selectedCarBrand = car!;
    emit(CarmodelLoaded());
  }

  List<CarModelModel> allCarModel = [];

  fetchCarModel() async {
    emit(CarmodelLoading());
    try {
      allCarModel = await carRepo.getCarModel();
      emit(CarmodelLoaded());
      getCarModelByCarBrand(brand: allCarBrands.first.model!);
    } catch (e) {
      log(e.toString());
      emit(CarmodelError());
    }
  }

  List<CarModelModel> filteredCarModel = [];

  getCarModelByCarBrand({required String brand}) {
    for (var b in allCarBrands) {
      emit(CarmodelLoading());
      if (brand == b.model) {
        log("ccc${b.model}");
        selectCarBrands(car: b);
        filteredCarModel.clear();
        filteredCarModel = allCarModel.where((e) => e.modelId == selectedCarBrand.id.toString()).toList();
      }
    }
    getCarCategory();
    emit(CarmodelLoaded());
  }

  List<CarCategoryModel> carCategory = [];

  getCarCategory() async {
    emit(CarmodelLoading());
    try {
      carCategory.clear();
      carCategory = await carRepo.getCarCategory();
      log('mess${carCategory.length}');
      getFilteredCarCatByDriverCarCatId(
          ids: globalDriverProfile.user?.driver?.carCategoriesIds?.split(',').map(int.parse).toList() ?? []);
      emit(CarmodelLoaded());
    } catch (e) {
      log(e.toString());

      emit(CarmodelError());
    }
  }

  List<CarCategoryModel> selectedCarCat = [];

  selectCarCategory({CarCategoryModel? car}) async {
    emit(CarmodelLoading());
    if (!selectedCarCat.contains(car)) {
      selectedCarCat.add(car!);
    } else {
      selectedCarCat.remove(car);
    }
    log('legngth${selectedCarCat.length}');
    emit(CarmodelLoaded());
  }

  CarModelModel selectedCarModel = CarModelModel();

  selectCarmodel({required String model}) {
    for (var b in allCarModel) {
      emit(CarmodelLoading());
      if (model == b.name) {
        selectedCarModel = b;
      }
    }
    emit(CarmodelLoaded());
  }

  List<CarCategoryModel> filteredcarCategory = [];

  getFilteredCarCatByDriverCarCatId({required List ids}) {
    filteredcarCategory.clear();
    emit(CarmodelLoading());

    for (var cat in carCategory) {
      if (ids.any((element) => element.toString() == cat.id)) {
        filteredcarCategory.add(cat);
      }
    }
    log(filteredcarCategory.length.toString());
    emit(CarmodelLoaded());
  }
}
