part of 'payment_cubit.dart';

sealed class PaymentState extends Equatable {
  const PaymentState();

  @override
  List<Object?> get props => [];
}

final class PaymentInitial extends PaymentState {}

final class Paymentloading extends PaymentState {}

final class PaymentLoaded extends PaymentState {}

final class CardAddedState extends PaymentState {}

final class CardAddedError extends PaymentState {
  final String message;

  const CardAddedError(this.message);

  @override
  List<Object> get props => [message];
}

final class PaymentError extends PaymentState {
  final String? message;

  const PaymentError([this.message]);

  @override
  List<Object?> get props => [message];
}

final class PaymentSuccess extends PaymentState {}
