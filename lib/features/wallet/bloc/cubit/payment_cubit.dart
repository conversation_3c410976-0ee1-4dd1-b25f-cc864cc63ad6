import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';

import '../../model/card_model.dart';
import '../../repository/wallet_repository.dart';

part 'payment_state.dart';

class PaymentCubit extends Cubit<PaymentState> {
  WalletRepository walletRepository;

  PaymentCubit(this.walletRepository) : super(PaymentInitial());
  List<CardModel> allCards = [];

  fetchCards() async {
    try {
      emit(Paymentloading());
      allCards = await walletRepository.getCards();
      emit(PaymentLoaded());
    } catch (e) {
      log(e.toString());
      emit(PaymentError());
    }
  }

  deleteCards(id) async {
    try {
      emit(Paymentloading());
      await walletRepository.deleteCards(id: id);
      fetchCards();
    } catch (e) {
      log(e.toString());
      emit(PaymentError());
    }
  }

  addCards({expYear, cvv, expMonth, cardNumber, cardName}) async {
    try {
      emit(Paymentloading());
      await walletRepository.addCards(
          expYear: expYear, cvv: cvv, expMonth: expMonth, cardNumber: cardNumber, cardName: cardName);
      emit(CardAddedState());
      fetchCards();
    } catch (e) {
      log(e.toString());
      emit(CardAddedError(e.toString()));
    }
  }

  String tranferNumber = '';
  String paymentType = '';
  String selectedAmount = '';

  selectPaymentAmount({amount, String? type, String? mobileNumber}) {
    emit(Paymentloading());
    // paymentType = type!;
    if (type == 'transfer') {
      tranferNumber = mobileNumber!;
    }
    selectedAmount = amount.toString();
    emit(PaymentLoaded());
  }

  payCard({required double amount}) async {
    try {
      emit(Paymentloading());
      final cardData = await walletRepository.paymentWithCard(amount: amount * 100);

      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          merchantDisplayName: 'YallaGai',
          paymentIntentClientSecret: cardData["payment_intent_client_secret"],
          customerEphemeralKeySecret: cardData["ephemeral_key"],
          customerId: cardData["customer_id"],
          style: ThemeMode.light,
          appearance: PaymentSheetAppearance(
            colors: PaymentSheetAppearanceColors(primary: primaryColor),
            primaryButton: PaymentSheetPrimaryButtonAppearance(
              colors: PaymentSheetPrimaryButtonTheme(
                light: PaymentSheetPrimaryButtonThemeColors(
                  background: primaryColor,
                  text: Colors.white,
                ),
                dark: PaymentSheetPrimaryButtonThemeColors(
                  background: primaryColor,
                  text: Colors.white,
                ),
              ),
            ),
          ),
        ),
      );

      await Stripe.instance.presentPaymentSheet(options: PaymentSheetPresentOptions());

      emit(PaymentSuccess());
    } on StripeException catch (e) {
      emit(PaymentError(e.error.localizedMessage.toString()));
    } catch (e) {
      log(e.toString());
      emit(PaymentError());
    }
  }

  transfer({required double amount, required String mobileNumber}) async {
    try {
      emit(Paymentloading());
      await walletRepository.transferToOtherUser(amount: amount * 100, mobileNumber: mobileNumber);
      emit(PaymentSuccess());
    } catch (e, stacktrace) {
      log(e.toString());
      log(stacktrace.toString());
      emit(PaymentError());
    }
  }

  bool payment = false;

  selectPaymentmethod(value) {
    emit(Paymentloading());
    payment = value;
    emit(PaymentLoaded());
  }
}
