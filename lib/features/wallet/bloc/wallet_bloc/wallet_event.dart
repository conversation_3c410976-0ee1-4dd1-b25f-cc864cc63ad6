part of 'wallet_bloc.dart';

abstract class WalletEvent extends Equatable {
  const WalletEvent();
}

// class WalletGetGallet extends WalletEvent {
//   const WalletGetGallet();

//   @override
//   List<Object?> get props => [];
// }

class WalletGetMyTransaction extends WalletEvent {
  const WalletGetMyTransaction();

  @override
  List<Object?> get props => [];
}

class WalletGetNeighbourhood extends WalletEvent {
  final String id;
  const WalletGetNeighbourhood({required this.id});

  @override
  List<Object?> get props => [];
}

class WalletRechargeCard extends WalletEvent {
  final String code;
  const WalletRechargeCard({required this.code});

  @override
  List<Object?> get props => [];
}

class WalletRechargeCardCopy extends WalletEvent {
  final String phoneNumber;
  final String amount;
  const WalletRechargeCardCopy({
    required this.amount,
    required this.phoneNumber,
  });

  @override
  List<Object?> get props => [];
}
