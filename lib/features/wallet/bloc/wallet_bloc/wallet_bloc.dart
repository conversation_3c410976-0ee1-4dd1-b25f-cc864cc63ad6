import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/features/wallet/model/transcation.dart';
import 'package:yalla_gai_driver/features/wallet/model/wallet.dart';
import 'package:yalla_gai_driver/features/wallet/repository/wallet_repository.dart';

part 'wallet_event.dart';
part 'wallet_state.dart';

class WalletBloc extends Bloc<WalletEvent, WalletState> {
  WalletRepository walletRepository = WalletRepository();

  List<WalletTransaction> transactions = [];
  WalletBloc() : super(WalletInitial()) {
    ////// Charge Account Driver
    // on<WalletGetGallet>((event, emit) async {
    //   try {
    //     emit(WalletGetLoading());
    //     Wallet? wallet = await walletRepository.getMyWallet();
    //     emit(WalletGetSuccess(wallet: wallet));
    //   } catch (e) {
    //     emit(WalletGetFaulire(message: e.toString()));
    //   }
    // });
    ////// Wallet Get My Transaction

    on<WalletGetMyTransaction>((event, emit) async {
      try {
        emit(WalletGetMyTransactionLoading());
        final result = await walletRepository.getTransactions();
        transactions = result;
        emit(const WalletGetMyTransactionSuccess());
      } catch (e) {
        emit(WalletGetMyTransactionFaulire(message: e.toString()));
      }
    });
    ////// Wallet Get My Transaction
    on<WalletGetNeighbourhood>((event, emit) async {
      try {
        emit(WalletGetNeighbourhoodLoading());
        await walletRepository.getNeighbourhood(event.id);
        emit(const WalletGetNeighbourhoodSuccess());
      } catch (e) {
        emit(WalletGetNeighbourhoodFaulire(message: e.toString()));
      }
    });
    ////// Wallet Get My Transaction
    on<WalletRechargeCard>((event, emit) async {
      try {
        emit(WalletRechargeCardLoading());
        await walletRepository.rechargeCard(event.code);
        emit(const WalletRechargeCardSuccess());
      } catch (e) {
        emit(WalletRechargeCardFaulire(message: e.toString()));
      }
    });
    ////// Wallet Recharge Card
    on<WalletRechargeCardCopy>((event, emit) async {
      try {
        emit(WalletRechargeCardCopyLoading());
        await walletRepository.rechargeCardCopy(event.phoneNumber, event.amount);
        emit(const WalletRechargeCardCopySuccess());
      } catch (e) {
        emit(WalletRechargeCardCopyFaulire(message: e.toString()));
      }
    });
  }
}
