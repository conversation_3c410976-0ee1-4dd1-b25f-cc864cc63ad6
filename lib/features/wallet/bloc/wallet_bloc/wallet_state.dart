part of 'wallet_bloc.dart';

abstract class WalletState extends Equatable {
  const WalletState();

  @override
  List<Object> get props => [];
}

final class WalletInitial extends WalletState {}

final class WalletGetLoading extends WalletState {}

final class WalletGetSuccess extends WalletState {
  final Wallet? wallet;
  const WalletGetSuccess({required this.wallet});
}

final class WalletGetFaulire extends WalletState {
  final String message;
  const WalletGetFaulire({required this.message});
}

final class WalletGetMyTransactionLoading extends WalletState {}

final class WalletGetMyTransactionSuccess extends WalletState {
  const WalletGetMyTransactionSuccess();
}

final class WalletGetMyTransactionFaulire extends WalletState {
  final String message;
  const WalletGetMyTransactionFaulire({required this.message});
}

final class WalletGetNeighbourhoodLoading extends WalletState {}

final class WalletGetNeighbourhoodSuc<PERSON> extends WalletState {
  const WalletGetNeighbourhoodSuccess();
}

final class WalletGetNeighbourhoodFaulire extends WalletState {
  final String message;
  const WalletGetNeighbourhoodFaulire({required this.message});
}

final class WalletRechargeCardLoading extends WalletState {}

final class WalletRechargeCardSuccess extends WalletState {
  const WalletRechargeCardSuccess();
}

final class WalletRechargeCardFaulire extends WalletState {
  final String message;
  const WalletRechargeCardFaulire({required this.message});
}

final class WalletRechargeCardCopyLoading extends WalletState {}

final class WalletRechargeCardCopySuccess extends WalletState {
  const WalletRechargeCardCopySuccess();
}

final class WalletRechargeCardCopyFaulire extends WalletState {
  final String message;
  const WalletRechargeCardCopyFaulire({required this.message});
}
