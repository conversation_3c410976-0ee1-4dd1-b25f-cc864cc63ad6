import 'dart:convert';
import 'dart:developer' as developer;

import 'package:http/http.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/wallet/model/wallet.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';
import '../model/card_model.dart';
import '../model/transcation.dart';

class WalletRepository {
  final _httpClient = HttpApiClient();

  Future<Wallet?> getMyWallet() async {
    var response = await _httpClient.get(EndPoints.getMyWallet(LocalStorage.instance.userId ?? ''));

    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (responseData['status'] == true) {
      if (responseData["data"] == null) {
        return null;
      }
      return Wallet.fromJson(responseData["data"]);
    } else {
      final errors = responseData["message"];
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  Future<List<WalletTransaction>> getTransactions() async {
    var response = await _httpClient.get(EndPoints.getMyTransaction());
    Map<String, dynamic> responseData = jsonDecode(response.body);
    return (responseData["data"] as Iterable? ?? []).map((e) => WalletTransaction.fromJson(e)).toList();
  }

  Future getNeighbourhood(String id) async {
    var response = await _httpClient.get(EndPoints.getNeighbourhood());
    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (responseData['status'] == true) {
      return;
    } else {
      final errors = responseData["errors"].values.toList();
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  Future rechargeCard(String id) async {
    final response = await _httpClient.send(MultipartRequest('POST', EndPoints.rechargeCard(id)));
    final responseBody = await response.stream.toBytes();
    var responseData = await jsonDecode(String.fromCharCodes(responseBody));
    developer.log("url${EndPoints.rechargeCard(id)} responseData$responseData status${response.statusCode}");
  }

/////////////////////
  ///  get rechargeCard
  Future rechargeCardCopy(String phoneNumber, String amount) async {
    final response = await _httpClient.send(MultipartRequest('POST', EndPoints.rechargeCardCopy()));
    final responseBody = await response.stream.toBytes();
    var responseData = await jsonDecode(String.fromCharCodes(responseBody));

    return;
  }

  Future getCards() async {
    final response = await _httpClient.get(EndPoints.fetchCards());
    final responseData = jsonDecode(response.body);
    List<CardModel> card = [];
    for (var cad in responseData['data']) {
      card.add(CardModel.fromJson(cad));
    }
    return card;
  }

  Future deleteCards({id}) async {
    final response = await _httpClient.delete(EndPoints.deleteCards(id));
    final body = jsonDecode(response.body);
    developer.log(body.toString());
  }

  Future addCards({expYear, cvv, expMonth, cardNumber, cardName}) async {
    final response = await _httpClient.post(
      EndPoints.addCard(),
      body: jsonEncode({
        "card_name": cardName,
        'card_number': cardNumber,
        "exp_month": expMonth,
        "cvv": cvv,
        "exp_year": expYear,
      }),
    );

    final data = jsonDecode(response.body);

    developer.log(data.toString());
  }

  Future<Map<String, dynamic>> paymentWithCard({amount}) async {
    final response = await _httpClient.post(
      EndPoints.payCard(),
      body: jsonEncode({"amount": amount.toString()}),
    );
    final responseData = jsonDecode(response.body);
    return responseData["card_data"] as Map<String, dynamic>;
  }

  Future transferToOtherUser({required double amount, String? cardId, required String mobileNumber}) async {
    final response = await _httpClient.post(
      EndPoints.transfer(),
      body: jsonEncode({
        "amount": amount.toString(),
        if (cardId != null && cardId.isNotEmpty) 'card_id': cardId,
        "mobile_number": mobileNumber,
      }),
    );
    final data = jsonDecode(response.body);
    developer.log("here$data${response.statusCode}");
  }
}
