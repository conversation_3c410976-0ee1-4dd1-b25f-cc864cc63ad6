import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/features/booking/bloc/system_service/system_services_cubit.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/wallet_bloc/wallet_bloc.dart';
import 'package:yalla_gai_driver/features/wallet/model/transcation.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import '../../../booking/presentation/widgets/custom_drawer.dart';
import '../widgets/transaction_card.dart';
import '../widgets/wallet_app_bar.dart';

class Wallet extends StatefulWidget {
  const Wallet({super.key});

  @override
  State<Wallet> createState() => _WalletState();
}

class _WalletState extends State<Wallet> {
  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: white,
      drawerScrimColor: Colors.transparent,
      drawer: CustomDrawer(),
      body: ScaffoldBody(),
    );
  }
}

class ScaffoldBody extends StatefulWidget {
  const ScaffoldBody({super.key});

  @override
  State<ScaffoldBody> createState() => _ScaffoldBodyState();
}

class _ScaffoldBodyState extends State<ScaffoldBody> {
  String transactionType = "all";

  @override
  void initState() {
    super.initState();
    BlocProvider.of<WalletBloc>(context).add(const WalletGetMyTransaction());
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Scaffold(
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            const WalletAppBar(),
            Stack(
              children: [
                SvgPicture.asset(
                  height: 24.h,
                  // width: 90.w,
                  fit: BoxFit.fill,
                  walletTicket,
                ),
                Positioned(
                  bottom: 16.h,
                  left: 16.w,
                  child: Text(
                    AppLocalizations.of(context)!.balance,
                    selectionColor: white,
                    style: Theme.of(context)
                        .textTheme
                        .displayLarge!
                        .copyWith(fontSize: 26, fontWeight: FontWeight.w700, color: white),
                  ),
                ),
                Positioned(
                  bottom: 5.3.h,
                  left: 16.w,
                  child:
                      //  BlocBuilder<WalletBloc, WalletState>(
                      //     builder: (context, state) {
                      //   if (state is WalletGetLoading) {
                      //     return const SizedBox(
                      //       height: 15,
                      //       width: 100,
                      //       child: LinearProgressIndicator(
                      //         color: Colors.white,
                      //       ),
                      //     );
                      //   }
                      //   if (state is WalletGetSuccess) {
                      //     return Text(
                      //       state.wallet == null
                      //           ? "0 ${Utils.appText(context: context, text: globalDriverProfile.user?.country?.englishCurrency ?? '', arabicText: globalDriverProfile.user?.country?.arabicCurrency ?? '')}"
                      //           : "${state.wallet?.driverWallet?.balance ?? "0"} ${state.wallet?.currency ?? Utils.appText(context: context, text: globalDriverProfile.user?.country?.englishCurrency ?? '', arabicText: globalDriverProfile.user?.country?.arabicCurrency ?? '')}",
                      //       selectionColor: white,
                      //       style: Theme.of(context)
                      //           .textTheme
                      //           .displayLarge!
                      //           .copyWith(
                      //               fontSize: 40,
                      //               fontWeight: FontWeight.w700,
                      //               color: white),
                      //     );
                      //   }
                      //   return
                      Text(
                    // "${double.parse(driver?.balance ?? '0') / 100}${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                    "${double.parse(driver?.balance ?? '0').round() / 100} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                    selectionColor: white,
                    style: Theme.of(context)
                        .textTheme
                        .displayLarge!
                        .copyWith(fontSize: 28.sp, fontWeight: FontWeight.w700, color: white),
                  ),

                  // }),
                ),
              ],
            ),
            const SizedBox(height: 10.0),
            Builder(builder: (context) {
              final balanceTransferEnabled = context.select<SystemServicesCubit, bool>((value) {
                final serviceData = value.services.where((element) => element.type == "balance_transfer").firstOrNull;
                return serviceData == null ? true : serviceData.status == "active";
              });

              final balanceIncreaseEnabled = context.select<SystemServicesCubit, bool>((value) {
                final serviceData = value.services.where((element) => element.type == "balance_increase").firstOrNull;
                return serviceData == null ? true : serviceData.status == "active";
              });

              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  if (balanceIncreaseEnabled)
                    CustomButton(
                      buttonText: AppLocalizations.of(context)!.top_up,
                      borderRadius: 10,
                      color: primaryColorDark,
                      height: 6.h,
                      width: 40.w,
                      onPressed: () {
                        context.push(path.walletTopUp, extra: {"dropOff": false});
                      },
                    ),
                  if (balanceTransferEnabled)
                    CustomButton(
                      buttonText: AppLocalizations.of(context)!.send,
                      borderOnly: true,
                      width: 40.w,
                      borderRadius: 10,
                      // color: white,
                      height: 6.h,
                      onPressed: () {
                        context.push(path.walletSend, extra: {"dropOff": false});
                      },
                    ),
                ],
              );
            }),
            const Gap(),
            const CustomDivider(),
            const Gap(),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 6.w),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Flexible(
                    child: CustomButton(
                        buttonText: AppLocalizations.of(context)!.all,
                        color: primaryColor,
                        borderOnly: transactionType != "all",
                        borderRadius: 12,
                        height: 6.h,
                        width: 30.w,
                        onPressed: () {
                          setState(() {
                            transactionType = "all";
                          });
                        }),
                  ),
                  const SizedBox(width: 8.0),
                  Flexible(
                    child: CustomButton(
                        buttonText: AppLocalizations.of(context)!.sent,
                        color: primaryColor,
                        borderOnly: transactionType != "sent",
                        borderRadius: 12,
                        height: 6.h,
                        width: 30.w,
                        onPressed: () {
                          setState(() {
                            transactionType = "sent";
                          });
                        }),
                  ),
                  const SizedBox(width: 8.0),
                  Flexible(
                    child: CustomButton(
                        buttonText: AppLocalizations.of(context)!.received,
                        borderOnly: transactionType != "receive",
                        borderRadius: 12,
                        height: 6.h,
                        width: 30.w,
                        onPressed: () {
                          setState(() {
                            transactionType = "receive";
                          });
                        }),
                  ),
                ],
              ),
            ),
            const Gap(),
            const CustomDivider(),
            Builder(builder: (context) {
              final driver = context.watch<AuthBloc>().user.user;
              List<WalletTransaction> transactions = context.watch<WalletBloc>().transactions;

              transactions = switch (transactionType) {
                "sent" => transactions.where((element) => element.receiverId != driver?.id?.toString()).toList(),
                "receive" => transactions.where((element) => element.receiverId == driver?.id?.toString()).toList(),
                _ => transactions,
              };

              return ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemBuilder: (context, index) {
                  return Center(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: TransactionCard(data: transactions[index]),
                    ),
                  );
                },
                itemCount: transactions.length,
              );
            }),
            Gap(h: MediaQuery.paddingOf(context).bottom),
          ],
        ),
      ),
    );
  }
}
