import 'package:flutter/material.dart';
import '../widgets/payment_methods.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class WalletSendPayment extends StatefulWidget {
  const WalletSendPayment({super.key});

  @override
  State<WalletSendPayment> createState() => _WalletSendPaymentState();
}

class _WalletSendPaymentState extends State<WalletSendPayment> {
  String successText =
      "Congratulations, You have successfully transferred amount from your Account";

  @override
  Widget build(BuildContext context) {
    successText =
        AppLocalizations.of(context)!.successfully_transferred_from_account;

    return PaymentMethods(successText: successText, type: "send");
  }
}
