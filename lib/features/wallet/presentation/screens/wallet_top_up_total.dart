import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/dialog_with_image_builder.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/cubit/payment_cubit.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import '../../../booking/presentation/widgets/custom_drawer.dart';
import '../widgets/wallet_app_bar.dart';

class WalletTopUpTotal extends StatelessWidget {
  const WalletTopUpTotal({
    super.key,
    required this.topUpAmount,
  });

  final int topUpAmount;

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return BlocListener<PaymentCubit, PaymentState>(
      listener: (context, state) {
        if (state is PaymentSuccess) {
          dialogWithImageBuilder(
            context: context,
            image: banknote,
            successText: AppLocalizations.of(context)!.successfully_added_to_wallet,
            buttonText: AppLocalizations.of(context)!.okay,
          );
        }
      },
      child: AppScaffold(
        isLoading: context.watch<PaymentCubit>().state is Paymentloading,
        backgroundColor: white,
        drawerScrimColor: Colors.transparent,
        drawer: const CustomDrawer(),
        body: SizedBox(
          height: double.infinity,
          width: double.infinity,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              const WalletAppBar(),
              Container(
                margin: const EdgeInsets.all(20.0),
                width: 100,
                height: 100,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(color: black.withOpacity(0.3)),
                ),
                child: Center(
                  child: Text(AppLocalizations.of(context)!.top_up),
                ),
              ),
              const SizedBox(height: 10.0),
              Divider(
                height: 5,
                thickness: 1,
                indent: 5.w,
                endIndent: 5.w,
                color: black.withOpacity(0.3),
              ),
              const SizedBox(height: 40.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Text(
                    AppLocalizations.of(context)!.total_amount,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')} $topUpAmount.00",
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                    ),
                  )
                ],
              ),
              const SizedBox(height: 40.0),
              CustomButton(
                buttonText: AppLocalizations.of(context)!.pay,
                borderRadius: 10,
                height: 6.h,
                width: 45.w,
                onPressed: () {
                  context.read<PaymentCubit>().payCard(amount: topUpAmount.toDouble());
                },
              ),
            ],
          ),
        ),
      ),
    );
  }
}
