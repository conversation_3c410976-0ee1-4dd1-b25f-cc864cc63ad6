 

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_textfield.dart';
import 'package:yalla_gai_driver/core/shared_widgets/dialog_with_image_builder.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../../../core/utils/colors.dart';
import '../../../booking/presentation/widgets/custom_drawer.dart';
import '../../bloc/wallet_bloc/wallet_bloc.dart';
import '../widgets/wallet_app_bar.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class BalanceRechargePage extends StatefulWidget {
  const BalanceRechargePage({super.key});

  @override
  State<BalanceRechargePage> createState() => _BalanceRechargePageState();
}

class _BalanceRechargePageState extends State<BalanceRechargePage> {
  final rechargeNumberController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      isLoading: context.watch<WalletBloc>().state is WalletRechargeCardLoading,
      backgroundColor: white,
      drawerScrimColor: Colors.transparent,
      drawer: const CustomDrawer(),
      body: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children:  [
            const WalletAppBar(),
            const Gap(),
            Text(
              AppLocalizations.of(context)!.balance_recharge_card,
              style: const TextStyle(
                fontSize: 26,
                fontWeight: FontWeight.bold,
              ),
            ),
            Gap(h: 5.h),
            CustomTextField(
                width: 75.w,
                hasBorder: true,
                borderRadius: 20,
                validator: (val) {
                  return '';
                },
                textEditingController: rechargeNumberController,
                labelText: AppLocalizations.of(context)!.enter_recharge_number),
            const SizedBox(height: 40.0),
            BlocConsumer<WalletBloc, WalletState>(listener: (context, state) {
              if (state is WalletRechargeCardCopyFaulire) {
                showCustomSnackbar(context, state.message);
              }
              if (state is WalletRechargeCardSuccess) {
                dialogWithImageBuilder(
                    context: context,
                    image: banknote,
                    successText: AppLocalizations.of(context)!
                        .successfully_added_to_wallet,
                    buttonText: AppLocalizations.of(context)!.okay);
                rechargeNumberController.clear();
              }
            }, builder: (context, state) {
              return CustomButton(
                isLoading:
                    state is WalletRechargeCardCopyLoading ? true : false,
                buttonText: AppLocalizations.of(context)!.add,
                borderRadius: 10,
                height: 6.h,
                width: 80.w,
                onPressed: () {
                  BlocProvider.of<WalletBloc>(context).add(
                      WalletRechargeCard(code: rechargeNumberController.text));
                },
              );
            }),
            Gap(h: 2.h),
          ],
        ),
      ),
    );
  }
}
