import 'dart:async';
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import '../../../booking/presentation/widgets/custom_drawer.dart';
import '../widgets/wallet_app_bar.dart';

class WalletTopUp extends StatefulWidget {
  const WalletTopUp({super.key});

  @override
  State<WalletTopUp> createState() => _WalletTopUpState();
}

class _WalletTopUpState extends State<WalletTopUp> {
  int topUpAmount = 3;
  List<int> topUpAmounts = [10, 25, 50];

  Timer? _timer;

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Scaffold(
      backgroundColor: white,
      drawerScrimColor: Colors.transparent,
      drawer: const CustomDrawer(),
      body: SizedBox(
        height: double.infinity,
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            const WalletAppBar(),
            Container(
              margin: const EdgeInsets.all(20.0),
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: black.withOpacity(0.3)),
              ),
              child: Center(
                child: Text(AppLocalizations.of(context)!.top_up),
              ),
            ),
            const SizedBox(height: 10.0),
            Divider(
              height: 5,
              thickness: 1,
              indent: 5.w,
              endIndent: 5.w,
              color: black.withOpacity(0.3),
            ),
            const SizedBox(height: 15.0),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                GestureDetector(
                  onTap: () => setState(() {
                    topUpAmount = max(3, topUpAmount - 1);
                  }),
                  onLongPressStart: (details) {
                    if(_timer != null) return;
                    _timer = Timer.periodic(Durations.medium2, (timer) {
                      setState(() {
                        topUpAmount -= 1;
                      });
                      if(topUpAmount == 0) timer.cancel();
                    });
                  },
                  onLongPressEnd: (details) {
                    _timer?.cancel();
                    _timer = null;
                  },
                  child: const Icon(size: 36, Icons.remove_circle),
                ),
                Text(
                  "$topUpAmount ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                  style: TextStyle(fontWeight: FontWeight.w600, fontSize: 24, color: black.withOpacity(0.5)),
                ),
                GestureDetector(
                  onTap: () => setState(() {
                      topUpAmount += 1;
                    }),
                  onLongPressStart: (details) {
                    if(_timer != null) return;
                    _timer = Timer.periodic(Durations.medium2, (timer) {
                      setState(() {
                        topUpAmount += 1;
                      });
                    });
                  },
                  onLongPressEnd: (details) {
                    _timer?.cancel();
                    _timer = null;
                  },
                  child: const Icon(size: 36, Icons.add_circle),
                ),
              ],
            ),
            const SizedBox(height: 10.0),
            Divider(
              height: 5,
              thickness: 1,
              indent: 10.w,
              endIndent: 10.w,
              color: primaryColor,
            ),
            const SizedBox(height: 20.0),
            SizedBox(
              height: 5.h,
              child: ListView.builder(
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemCount: topUpAmounts.length,
                itemBuilder: (context, index) => Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8.0),
                  child: CustomButton(
                      borderOnly: true,
                      borderColor: primaryColor,
                      buttonText:
                          "${topUpAmounts[index]} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                      borderRadius: 10,
                      height: 4.h,
                      width: 25.w,
                      onPressed: () {
                        setState(() {
                          topUpAmount = topUpAmounts[index];
                        });
                      }),
                ),
              ),
            ),
            const SizedBox(height: 40.0),
            CustomButton(
                buttonText: AppLocalizations.of(context)!.continue_,
                borderRadius: 10,
                height: 6.h,
                width: 45.w,
                onPressed: topUpAmount > 0
                    ? () {
                        context.push(path.walletTopUpTotal, extra: {"topUpAmount": topUpAmount});
                      }
                    : null),
            Gap(h: 2.h),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              child: CustomButton(
                  buttonText: AppLocalizations.of(context)!.balance_recharge_card,
                  borderRadius: 10,
                  // height: 6.h,
                  width: double.infinity,
                  onPressed: () {
                    context.push(path.balanceRechargePage);
                  }),
            ),
          ],
        ),
      ),
    );
  }
}
