import 'dart:async';
import 'dart:developer' as developer;
import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
import 'package:flutter_svg/svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/dialog_with_image_builder.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import '../../../booking/presentation/widgets/custom_drawer.dart';
import '../../bloc/cubit/payment_cubit.dart';
import '../widgets/wallet_app_bar.dart';

class WalletSend extends StatefulWidget {
  const WalletSend({super.key});

  @override
  State<WalletSend> createState() => _WalletSendState();
}

class _WalletSendState extends State<WalletSend> {
  int sendAmount = 0;
  List<int> sendAmounts = [5, 10, 15];
  final phoneNumberTextEditingController = TextEditingController();

  Timer? _timer;

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return BlocListener<PaymentCubit, PaymentState>(
      listener: (context, state) {
        if (state is PaymentSuccess) {
          dialogWithImageBuilder(
            context: context,
            image: banknote,
            successText: AppLocalizations.of(context)!.successfully_transferred_from_account,
            buttonText: AppLocalizations.of(context)!.okay,
          );
        }
      },
      child: AppScaffold(
        isLoading: context.watch<PaymentCubit>().state is Paymentloading,
        backgroundColor: white,
        drawerScrimColor: Colors.transparent,
        drawer: const CustomDrawer(),
        body: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const WalletAppBar(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    margin: const EdgeInsets.all(20.0),
                    width: 25.w,
                    height: 25.w,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: black.withOpacity(0.3)),
                    ),
                    child: Center(
                      child: Text(AppLocalizations.of(context)!.a),
                    ),
                  ),
                  SvgPicture.asset(dashedLine),
                  Container(
                    margin: const EdgeInsets.all(20.0),
                    width: 25.w,
                    height: 25.w,
                    decoration:
                        BoxDecoration(shape: BoxShape.circle, border: Border.all(color: black.withOpacity(0.3))),
                    child: Center(
                      child: Text(AppLocalizations.of(context)!.b),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 30.0),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 5.w),
                child: Directionality(
                  textDirection: TextDirection.ltr,
                  child: CustomCountrySelector(
                    onChanged: (countryCode, number) async {
                      developer.log("$countryCode $number");
                      setState(() {
                        phoneNumberTextEditingController.text = "";
                      });
                      try {
                        final data = await parse(number.replaceAll(RegExp(" "), ""), region: countryCode);
                        developer.log(data.toString());
                        setState(() {
                          phoneNumberTextEditingController.text = data['e164'];
                          developer.log('phone${phoneNumberTextEditingController.text}');
                        });
                      } catch (e) {
                        print("message::::${e.toString()}");
                        return "Invalid Number";
                      }
                    },
                  ),
                ),
              ),
              const SizedBox(height: 15.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () => setState(() {
                      sendAmount = max(3, sendAmount - 1);
                    }),
                    onLongPressStart: (details) {
                      if (_timer != null) return;
                      _timer = Timer.periodic(Durations.medium2, (timer) {
                        setState(() {
                          sendAmount -= 1;
                        });
                        if (sendAmount == 0) timer.cancel();
                      });
                    },
                    onLongPressEnd: (details) {
                      _timer?.cancel();
                      _timer = null;
                    },
                    child: const Icon(size: 36, Icons.remove_circle),
                  ),
                  Text(
                    "$sendAmount ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                    style: TextStyle(fontWeight: FontWeight.w600, fontSize: 24, color: black.withOpacity(0.5)),
                  ),
                  GestureDetector(
                    onTap: () => setState(() {
                      sendAmount += 1;
                    }),
                    onLongPressStart: (details) {
                      if (_timer != null) return;
                      _timer = Timer.periodic(Durations.medium2, (timer) {
                        setState(() {
                          sendAmount += 1;
                        });
                      });
                    },
                    onLongPressEnd: (details) {
                      _timer?.cancel();
                      _timer = null;
                    },
                    child: const Icon(size: 36, Icons.add_circle),
                  ),
                ],
              ),
              const SizedBox(height: 10.0),
              Divider(
                height: 5,
                thickness: 1,
                indent: 10.w,
                endIndent: 10.w,
                color: primaryColor,
              ),
              const SizedBox(height: 20.0),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  3,
                  (index) => Padding(
                    padding: EdgeInsets.only(right: 5.w),
                    child: CustomButton(
                      borderOnly: true,
                      borderColor: primaryColor,
                      buttonText:
                          "${sendAmounts[index]} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                      borderRadius: 10,
                      height: 4.h,
                      width: 24.w,
                      onPressed: () {
                        setState(() {
                          sendAmount = sendAmounts[index];
                        });
                      },
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 24.0),
              Container(
                alignment: AlignmentDirectional.centerStart,
                padding: const EdgeInsets.symmetric(horizontal: 20),
                child: Row(
                  children: [
                    Icon(Icons.info_outline_rounded, color: Colors.grey.shade700),
                    Gap(w: 12),
                    Expanded(
                      child: Text(
                        AppLocalizations.of(context)!.amount_send_note,
                        style: TextStyle(color: Colors.grey.shade700, fontSize: 16),
                        textAlign: TextAlign.start,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 40.0),
              CustomButton(
                buttonText: AppLocalizations.of(context)!.pay,
                borderRadius: 10,
                height: 6.h,
                width: 45.w,
                onPressed: sendAmount > 0 && phoneNumberTextEditingController.text.trim().isNotEmpty
                    ? () {
                        context.read<PaymentCubit>().transfer(
                              amount: sendAmount.toDouble(),
                              mobileNumber: phoneNumberTextEditingController.text.trim(),
                            );
                      }
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CustomCountrySelector extends StatefulWidget {
  const CustomCountrySelector({super.key, required this.onChanged});

  final Function(String countryCode, String phoneNumber) onChanged;

  @override
  State<CustomCountrySelector> createState() => _CustomCountrySelectorState();
}

class _CustomCountrySelectorState extends State<CustomCountrySelector> {
  final phoneController = TextEditingController();
  final _focusNode = FocusNode();

  final countries = CountryManager().countries;

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(final BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    final userCountryInfo =
        countries.where((e) => e.phoneCode == driver?.country?.internationalKey.toString()).firstOrNull;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: SingleChildScrollView(
        padding: EdgeInsets.only(
          bottom: max(0, 24 - MediaQuery.of(context).padding.bottom),
        ),
        child: Column(
          children: [
            /// Phone input
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    textAlign: TextAlign.center,
                    keyboardType: TextInputType.phone,
                    controller: phoneController,
                    focusNode: _focusNode,
                    decoration: InputDecoration(
                      hintText: userCountryInfo?.exampleNumberMobileNational,
                    ),
                    onTapOutside: (event) => _focusNode.unfocus(),
                    onChanged: (value) {
                      widget.onChanged(userCountryInfo.countryCode, phoneController.text);
                    },
                    validator: (value) {
                      if (value == "") {
                        return "Phone Number";
                      }
                      return null;
                    },
                    inputFormatters: [
                      LibPhonenumberTextFormatter(
                        phoneNumberType: PhoneNumberType.mobile,
                        phoneNumberFormat: PhoneNumberFormat.international,
                        country: userCountryInfo!,
                        inputContainsCountryCode: false,
                        additionalDigits: 1,
                        shouldKeepCursorAtEndOfInput: false,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
