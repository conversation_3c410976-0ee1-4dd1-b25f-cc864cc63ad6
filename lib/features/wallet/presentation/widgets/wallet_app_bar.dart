import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/shared_widgets/back_button.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class WalletAppBar extends StatefulWidget {
  const WalletAppBar({super.key});

  @override
  State<WalletAppBar> createState() => _WalletAppBarState();
}

class _WalletAppBarState extends State<WalletAppBar> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SvgPicture.asset(
          walletAppBar,
        ),
        Positioned(
            top: 6.h,
            left: 3.w,
            child: SvgBackButton(
              svgImage: SvgPicture.asset(
                width: 36,
                height: 36,
                backButton,
              ),
            )),
        Positioned(
          bottom: 5.h,
          left: 40.w,
          child: Text(
            AppLocalizations.of(context)!.my_wallet,
            selectionColor: white,
            style: Theme.of(context).textTheme.displayLarge!.copyWith(
                fontSize: 24, fontWeight: FontWeight.w700, color: black),
          ),
        ),
      ],
    );
  }
}
