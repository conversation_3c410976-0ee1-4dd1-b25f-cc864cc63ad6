import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/features/wallet/model/transcation.dart';

import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';

class TransactionCard extends StatefulWidget {
  final WalletTransaction data;

  const TransactionCard({super.key, required this.data});

  @override
  State<TransactionCard> createState() => _TransactionCardState();
}

class _TransactionCardState extends State<TransactionCard> {
  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Stack(
      children: [
        Container(
            padding: const EdgeInsets.all(10),
            height: 15.h,
            decoration:
                BoxDecoration(color: primaryColorDark.withOpacity(0.1), borderRadius: BorderRadius.circular(12)),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Text(DateFormat("dd MMM yyyy hh:mm a").format(widget.data.createdAt),
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold, fontSize: 12)),
                const CustomDivider(indent: 10),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Text(
                        "${widget.data.amount / 100} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                        style: Theme.of(context).textTheme.displayLarge!.copyWith(fontWeight: FontWeight.bold)),
                    Container(width: 1, height: 40, color: grey),
                    widget.data.receiverId != driver?.id?.toString()
                        ? const Icon(Icons.arrow_circle_down_rounded, color: Colors.red, size: 40)
                        : const Icon(Icons.arrow_circle_up_rounded, color: primaryColor, size: 40),
                    Text(
                        "${widget.data.amount / 100} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              fontWeight: FontWeight.bold,
                            ))
                  ],
                )
              ],
            )),
        PositionedDirectional(
          top: 0,
          start: 0,
          child: Container(
            constraints: BoxConstraints(maxWidth: 50.w),
            padding: const EdgeInsets.symmetric(horizontal: 8),
            height: 5.h,
            decoration: const BoxDecoration(
              color: primaryColorDark,
              borderRadius: BorderRadius.only(topLeft: Radius.circular(12), bottomRight: Radius.circular(12)),
            ),
            child: Center(
              widthFactor: 1.0,
              child: Text(
                widget.data.referenceId,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold, color: white),
                textAlign: TextAlign.center,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
