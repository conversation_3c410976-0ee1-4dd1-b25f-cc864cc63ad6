import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/routes/paths.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/dialog_with_image_builder.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/cubit/payment_cubit.dart';

import '../../../../core/shared_widgets/back_button.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../booking/presentation/widgets/custom_drawer.dart';
import '../../bloc/wallet_bloc/wallet_bloc.dart';
import '../../model/card_model.dart';
import 'payment_method.dart';

class PaymentMethods extends StatefulWidget {
  const PaymentMethods({super.key, this.successText, this.type});

  final String? successText;
  final String? type;

  @override
  State<PaymentMethods> createState() => _PaymentMethodsState();
}

class _PaymentMethodsState extends State<PaymentMethods> {
  @override
  void initState() {
    context.read<PaymentCubit>().fetchCards();
    super.initState();
  }

  CardModel seletedcard = CardModel();

  @override
  Widget build(BuildContext context) {
    final allCard = context.watch<PaymentCubit>().allCards;
    return BlocListener<PaymentCubit, PaymentState>(
      listener: (context, state) {
        if (state is PaymentSuccess) {
          dialogWithImageBuilder(
              context: context,
              image: banknote,
              successText: widget.successText ?? AppLocalizations.of(context)!.successfully_added_to_wallet,
              buttonText: AppLocalizations.of(context)!.okay);
        }
      },
      child: AppScaffold(
        isLoading: context.watch<PaymentCubit>().state is Paymentloading,
        backgroundColor: white,
        drawerScrimColor: Colors.transparent,
        drawer: const CustomDrawer(),
        body: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Padding(
                padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 3.w),
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: SvgBackButton(
                        svgImage: SvgPicture.asset(
                          width: 36,
                          height: 36,
                          backButton,
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.center,
                      child: Text(
                        AppLocalizations.of(context)!.payment_methods,
                        style: Theme.of(context).textTheme.displayLarge!.copyWith(color: black),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 10.0),
              Divider(
                height: 5,
                thickness: 1,
                indent: 5.w,
                endIndent: 5.w,
                color: black.withOpacity(0.3),
              ),
              const SizedBox(height: 10.0),
              Expanded(
                child: ListView(
                  children: [
                    Column(
                        children: List.generate(
                      allCard.length,
                      (index) => Slidable(
                        key: ValueKey(allCard[index].id),

                        // The start action pane is the one at the left or the top side.
                        startActionPane: ActionPane(
                            // A motion is a widget used to control how the pane animates.
                            motion: const ScrollMotion(),

                            // A pane can dismiss the Slidable.
                            dismissible: DismissiblePane(
                              onDismissed: () {
                                //   if (direction == DismissDirection.startToEnd) {
                                //     print("Item deleted");
                                //   }
                              },
                            ),
                            children: [
                              SlidableAction(
                                spacing: 10.sp,
                                borderRadius: BorderRadius.circular(20),
                                onPressed: (BuildContext context) {
                                  context.read<PaymentCubit>().deleteCards(allCard[index].id);
                                },
                                backgroundColor: const Color(0xFFFE4A49),
                                foregroundColor: Colors.white,
                                icon: Icons.delete,
                                label: 'Delete',
                              )
                            ]),
                        child: WalletTopupCards(
                          paymentImage: allCard[index].cardImage ?? '',
                          paymentName: allCard[index].cardNumber ?? '',
                          onPressed: () {
                            setState(() {
                              seletedcard = allCard[index];
                            });
                          },
                          isSelected: allCard[index] == seletedcard,
                        ),
                      ),
                    )),
                    if (widget.type == "send")
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            seletedcard = CardModel(cardName: 'wallet');
                          });
                        },
                        child: Container(
                          margin: const EdgeInsets.all(15.0),
                          padding: EdgeInsets.symmetric(horizontal: 6.w),
                          height: 12.h,
                          decoration:
                              BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(20), boxShadow: [
                            BoxShadow(
                              color: black.withOpacity(0.08),
                              spreadRadius: 4,
                              blurRadius: 8,
                              offset: const Offset(20, 2), // changes the position of the shadow
                            ),
                            BoxShadow(
                              color: black.withOpacity(0.25),
                              spreadRadius: 0,
                              blurRadius: 4,
                              offset: const Offset(0, 4), // changes the position of the shadow
                            ),
                          ]),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Icon(
                                Icons.wallet,
                                size: 30.sp,
                                color: Colors.grey,
                              ),
                              Text(
                                AppLocalizations.of(context)!.my_wallet,
                                style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.w800),
                              ),
                              Container(
                                width: 6.w,
                                height: 6.w,
                                decoration:
                                    BoxDecoration(border: Border.all(), borderRadius: BorderRadius.circular(30)),
                                child: Icon(Icons.circle,
                                    size: 18.sp, color: seletedcard.cardName == 'wallet' ? Colors.green : Colors.white),
                              )
                            ],
                          ),
                        ),
                      ),
                    if (widget.type == "send") const SizedBox(height: 30.0),
                    if (widget.type == "payment" || widget.type == "send")
                      Align(
                        alignment: Alignment.center,
                        child: BlocConsumer<WalletBloc, WalletState>(listener: (context, state) {
                          if (state is WalletRechargeCardCopyFaulire) {
                            showCustomSnackbar(context, state.message);
                          }
                          if (state is WalletRechargeCardCopySuccess) {
                            dialogWithImageBuilder(
                                context: context,
                                image: banknote,
                                successText:
                                    widget.successText ?? AppLocalizations.of(context)!.successfully_added_to_wallet,
                                buttonText: AppLocalizations.of(context)!.okay);
                          }
                        }, builder: (context, state) {
                          return CustomButton(
                            isLoading: state is WalletRechargeCardCopyLoading ? true : false,
                            buttonText: AppLocalizations.of(context)!.pay,
                            borderRadius: 10,
                            height: 6.h,
                            width: 80.w,
                            onPressed: seletedcard.cardName == null
                                ? null
                                : () {
                                    /*widget.type == "send"
                                        ? context.read<PaymentCubit>().transfer(cardId: seletedcard.id ?? '')
                                        : BlocProvider.of<PaymentCubit>(context)
                                            .payCard(cardId: seletedcard.id.toString());*/
                                  },
                          );
                        }),
                      ),
                    if (widget.type == "payment" || widget.type == "send") const SizedBox(height: 30.0),
                    Align(
                      alignment: Alignment.center,
                      child: CustomButton(
                        buttonText: AppLocalizations.of(context)!.add_new_card,
                        borderRadius: 10,
                        color: white,
                        borderOnly: true,
                        height: 6.h,
                        width: 45.w,
                        onPressed: () {
                          context.push(addCard);
                        },
                      ),
                    ),
                    const SizedBox(height: 30.0),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
