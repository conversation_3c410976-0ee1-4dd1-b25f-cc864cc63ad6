import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/cubit/payment_cubit.dart';

import '../../../../core/utils/colors.dart';

class PaymentMethod extends StatefulWidget {
  const PaymentMethod(
      {super.key,
      required this.paymentImage,
      required this.paymentName,
      required this.onPressed,
      required this.isSelected});

  final String paymentImage;
  final String paymentName;
  final void Function() onPressed;
  final bool isSelected;

  @override
  State<PaymentMethod> createState() => _PaymentMethodState();
}

class _PaymentMethodState extends State<PaymentMethod> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onPressed,
      child: Padding(
        padding: const EdgeInsets.only(right: 10.0),
        child: Container(
            height: 12.h,
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: black.withOpacity(0.08),
                  spreadRadius: 4,
                  blurRadius: 65,
                  offset: const Offset(20, 2), // changes the position of the shadow
                ),
                BoxShadow(
                  color: black.withOpacity(0.25),
                  spreadRadius: 0,
                  blurRadius: 4,
                  offset: const Offset(0, 4), // changes the position of the shadow
                ),
              ],
              color: white,
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(10),
                bottomRight: Radius.circular(10),
              ),
            ),
            child: Row(
              // mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SizedBox(width: 5.w),
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.rectangle,
                    borderRadius: BorderRadius.circular(5),
                    border: Border.all(
                      color: black.withOpacity(0.3),
                    ),
                  ),
                  child: SvgPicture.asset(
                    height: 4.h,
                    width: 10.w,
                    widget.paymentImage,
                  ),
                ),
                SizedBox(width: 15.w),
                Text(
                  widget.paymentName,
                  style: Theme.of(context).textTheme.displayLarge!.copyWith(color: black, fontWeight: FontWeight.w600),
                ),
                // Container(
                //   height: 20,
                //   width: 20,
                //   decoration: BoxDecoration(
                //     shape: BoxShape.circle,
                //     border: Border.all(
                //       color: black,
                //     ),
                //   ),
                //   child: Padding(
                //     padding: const EdgeInsets.all(2.0),
                //     child: Container(
                //       decoration: BoxDecoration(
                //         shape: BoxShape.circle,
                //         color: widget.isSelected ? primaryColor : white,
                //       ),
                //     ),
                //   ),
                // ),
              ],
            )),
      ),
    );
  }
}

class WalletTopupCards extends StatefulWidget {
  const WalletTopupCards(
      {super.key,
      required this.paymentImage,
      required this.paymentName,
      required this.onPressed,
      required this.isSelected});

  final String paymentImage;
  final String paymentName;
  final void Function() onPressed;
  final bool isSelected;

  @override
  State<WalletTopupCards> createState() => _WalletTopupCardsState();
}

class _WalletTopupCardsState extends State<WalletTopupCards> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onPressed,
      child: Container(
          margin: const EdgeInsets.all(15.0),
          height: 12.h,
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: black.withOpacity(0.08),
                spreadRadius: 4,
                blurRadius: 65,
                offset: const Offset(20, 2), // changes the position of the shadow
              ),
              BoxShadow(
                color: black.withOpacity(0.25),
                spreadRadius: 0,
                blurRadius: 4,
                offset: const Offset(0, 4), // changes the position of the shadow
              ),
            ],
            color: white,
            borderRadius: const BorderRadius.all(
              Radius.circular(20),
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              CachedNetworkImage(
                height: 15.w,
                width: 15.w,
                imageUrl: "${AppEnvironment.publicBaseUrl}${widget.paymentImage}",
                fit: BoxFit.cover,
                placeholder: (context, url) => const CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation(primaryColor),
                ),
                errorWidget: (context, url, error) => Icon(Icons.error, size: 20.sp),
              ),
              Text(
                "**** **** ****${widget.paymentName.substring(widget.paymentName.length - 4)}",
                style: Theme.of(context).textTheme.displayLarge!.copyWith(color: black, fontWeight: FontWeight.w600),
              ),
              context.watch<PaymentCubit>().payment == true
                  ? const SizedBox(
                      height: 20,
                      width: 20,
                    )
                  : Container(
                      height: 20,
                      width: 20,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: black,
                        ),
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(2.0),
                        child: Container(
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: widget.isSelected ? primaryColor : white,
                          ),
                        ),
                      ),
                    ),
            ],
          )),
    );
  }
}
