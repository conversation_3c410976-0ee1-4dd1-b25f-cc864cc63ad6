class WalletTransaction {
  String id;
  String senderId;
  String receiverId;
  String referenceId;
  double amount;
  DateTime createdAt;
  DateTime updatedAt;

  WalletTransaction({
    required this.id,
    required this.receiverId,
    required this.senderId,
    required this.referenceId,
    required this.amount,
    required this.createdAt,
    required this.updatedAt,
  });

  factory WalletTransaction.fromJson(Map<String, dynamic> json) {
    return WalletTransaction(
      id: json['id'].toString(),
      senderId: json['sender_id'] as String,
      receiverId: json['receiver_id'] as String,
      referenceId: json['reference_id'] as String,
      amount: double.tryParse(json['amount'].toString()) ?? 0,
      createdAt: DateTime.parse(json['created_at']).toLocal(),
      updatedAt: DateTime.parse(json['updated_at']).toLocal(),
    );
  }
}
