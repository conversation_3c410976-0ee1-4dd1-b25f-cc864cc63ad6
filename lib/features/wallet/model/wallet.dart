class Wallet {
  String? currency;
  DriverWallet? driverWallet;

  Wallet({
    required this.currency,
    required this.driverWallet,
  });

  factory Wallet.fromJson(Map<String, dynamic> json) {
    return Wallet(
      currency: json['currency']?.toString(),
      driverWallet: json['driver_wallet'] == null
          ? null
          : DriverWallet.fromJson(json['driver_wallet']),
    );
  }
}

class DriverWallet {
  String? id;
  String? driverId;
  String? enduserId;
  String? supervisorId;
  String? senderId;
  String? balance;
  String? activityStatus;
  String? payment;
  String? credit;
  String? createdAt;
  DateTime? updatedAt;

  DriverWallet({
    required this.id,
    required this.driverId,
    this.enduserId,
    required this.supervisorId,
    this.senderId,
    required this.balance,
    required this.activityStatus,
    required this.payment,
    required this.credit,
    required this.createdAt,
    this.updatedAt,
  });

  factory DriverWallet.fromJson(Map<String, dynamic> json) {
    return DriverWallet(
      id: json['id']?.toString(),
      driverId: json['driver_id']?.toString(),
      enduserId: json['enduser_id']?.toString(),
      supervisorId: json['supervisor_id']?.toString(),
      senderId: json['sender_id']?.toString(),
      balance: json['balance']?.toString(),
      activityStatus: json['activity_status']?.toString(),
      payment: json['payment']?.toString(),
      credit: json['credit']?.toString(),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }
}
