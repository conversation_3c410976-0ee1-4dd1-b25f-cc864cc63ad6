class CardModel {
  String? id;
  String? cardName;
  String? cardNumber;
  String? expMonth;
  String? expYear;
  String? cvv;
  String? cardImage;
  String? userId;

  CardModel({
    this.id,
    this.cardName,
    this.cardNumber,
    this.expMonth,
    this.expYear,
    this.cvv,
    this.cardImage,
    this.userId,
  });

  CardModel.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    cardName = json['card_name']?.toString();
    cardNumber = json['card_number']?.toString();
    expMonth = json['exp_month']?.toString();
    expYear = json['exp_year']?.toString();
    cvv = json['cvv']?.toString();
    cardImage = json['card_image']?.toString();
    userId = json['user_id']?.toString();
  }
}
