class Neighbourhood {
  String? id;
  String? countryId;
  String? cityId;
  String? name;
  String? createdAt;
  String? updatedAt;
  City? city;
  List<User?>? users;

  Neighbourhood({
    this.id,
    this.countryId,
    this.cityId,
    this.name,
    this.createdAt,
    this.updatedAt,
    this.city,
    this.users,
  });

  factory Neighbourhood.fromJson(Map<String, dynamic> json) {
    return Neighbourhood(
      id: json['id']?.toString(),
      countryId: json['country_id']?.toString(),
      cityId: json['city_id']?.toString(),
      name: json['name']?.toString(),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at']?.toString(),
      city: json['city'] == null ? null : City.fromJson(json['city']),
      users: json['users'] == null
          ? null
          : List<User>.from(json['users']?.map((x) => User.fromJson(x))),
    );
  }
}

class City {
  String? id;
  String? countryId;
  String? arabicName;
  String? name;
  String? image;
  String? createdAt;
  String? updatedAt;

  City({
    this.id,
    this.countryId,
    this.arabicName,
    this.name,
    this.image,
    this.createdAt,
    this.updatedAt,
  });

  factory City.fromJson(Map<String, dynamic> json) {
    return City(
      id: json['id']?.toString(),
      countryId: json['country_id']?.toString(),
      arabicName: json['arabic_name']?.toString(),
      name: json['name']?.toString(),
      image: json['image']?.toString(),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at']?.toString(),
    );
  }
}

class User {
  String? id;
  String? deviceId;
  String? roleId;
  String? neighbourhoodId;
  String? blockConditionId;
  String? languageId;
  String? distanceId;
  String? email;
  String? latitude;
  String? longitude;
  String? location;
  String? otp;
  String? mobileNumber;
  String? arabicFirstName;
  String? arabicLastName;
  String? fullName;
  String? firstName;
  String? lastName;
  String? status;
  String? activationStatus;
  String? reason;
  String? gender;
  String? couponId;
  String? privacyPolicyId;
  String? termConditionId;
  String? accountTypeId;
  String? addressId;
  String? emailVerifiedAt;
  String? password2;
  String? profileImage;
  String? idPhoto;
  String? createdAt;
  String? updatedAt;

  User({
    this.id,
    this.deviceId,
    this.roleId,
    this.neighbourhoodId,
    this.blockConditionId,
    this.languageId,
    this.distanceId,
    this.email,
    this.latitude,
    this.longitude,
    this.location,
    this.otp,
    this.mobileNumber,
    this.arabicFirstName,
    this.arabicLastName,
    this.fullName,
    this.firstName,
    this.lastName,
    this.status,
    this.activationStatus,
    this.reason,
    this.gender,
    this.couponId,
    this.privacyPolicyId,
    this.termConditionId,
    this.accountTypeId,
    this.addressId,
    this.emailVerifiedAt,
    this.password2,
    this.profileImage,
    this.idPhoto,
    this.createdAt,
    this.updatedAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id']?.toString(),
      deviceId: json['device_id']?.toString(),
      roleId: json['role_id']?.toString(),
      neighbourhoodId: json['neighbourhood_id']?.toString(),
      blockConditionId: json['block_condition_id']?.toString(),
      languageId: json['language_id']?.toString(),
      distanceId: json['distance_id']?.toString(),
      email: json['email']?.toString(),
      latitude: json['latitude']?.toString(),
      longitude: json['longitude']?.toString(),
      location: json['location']?.toString(),
      otp: json['otp']?.toString(),
      mobileNumber: json['mobile_number']?.toString(),
      arabicFirstName: json['arabic_first_name']?.toString(),
      arabicLastName: json['arabic_last_name']?.toString(),
      fullName: json['full_name']?.toString(),
      firstName: json['first_name']?.toString(),
      lastName: json['last_name']?.toString(),
      status: json['status']?.toString(),
      activationStatus: json['activation_status']?.toString(),
      reason: json['reason']?.toString(),
      gender: json['gender']?.toString(),
      couponId: json['coupon_id']?.toString(),
      privacyPolicyId: json['privacy_policy_id']?.toString(),
      termConditionId: json['term_condition_id']?.toString(),
      accountTypeId: json['account_type_id']?.toString(),
      addressId: json['address_id']?.toString(),
      emailVerifiedAt: json['email_verified_at']?.toString(),
      password2: json['password_2']?.toString(),
      profileImage: json['profile_image']?.toString(),
      idPhoto: json['id_photo']?.toString(),
      createdAt: json['created_at']?.toString(),
      updatedAt: json['updated_at']?.toString(),
    );
  }
}
