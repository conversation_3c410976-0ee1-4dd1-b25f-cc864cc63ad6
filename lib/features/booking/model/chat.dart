class ChatMessage {
  String? senderId;
  String? receiverId;
  String? message;
  DateTime? updatedAt;
  DateTime? createdAt;
  String? id;

  ChatMessage({
    this.senderId,
    this.receiverId,
    this.message,
    this.updatedAt,
    this.createdAt,
    this.id,
  });

  factory ChatMessage.fromJson(Map<String, dynamic> json) {
    return ChatMessage(
      senderId: json['sender_id'].toString(),
      receiverId: json['receiver_id'].toString(),
      message: json['message']?.toString(),
      updatedAt: json['updated_at'] == null
          ? null
          : DateTime.parse(json['updated_at']),
      createdAt: json['created_at'] == null
          ? null
          : DateTime.parse(json['created_at']),
      id: json['id']?.toString(),
    );
  }
}
