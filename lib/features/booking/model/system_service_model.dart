class SystemService {
  SystemService({
    required this.id,
    required this.type,
    required this.status,
    required this.description,
  });

  final int? id;
  final String? type;
  final String? status;
  final String? description;

  factory SystemService.fromJson(Map<String, dynamic> json) {
    return SystemService(
      id: json["id"],
      type: json["type"],
      status: json["status"],
      description: json["description"],
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "type": type,
        "status": status,
        "description": description,
      };
}
