class Service {
  String? id;
  String? arabicName;
  String? name;
  String? logo;
  String? status;
  String? userId;
  // List<City> ?cities;
  // List<String>? cars;

  Service({
    this.id,
    this.arabicName,
    this.name,
    this.logo,
    this.status,
    this.userId,
    // this.cities,
    // this.cars,
  });

  factory Service.fromJson(Map<String, dynamic> json) {
    // var cityList = json['cities'] as List;
    // List<City> cities = cityList.map((city) => City.fromJson(city)).toList();

    // var carList = json['cars'] as List;
    // List<Car> cars = carList.map((car) => Car.fromJson(car)).toList();

    return Service(
      id: json['id']?.toString(),
      arabicName: json['arabic_name']?.toString(),
      name: json['name']?.toString(),
      logo: json['logo']?.toString(),
      status: json['status']?.toString(),
      userId: json['user_id']?.toString(),
      // cities: cities,
    );
  }
}

// class City {
//   String id;
//   String userId;
//   int countryId;
//   String arabicName;
//   String name;
//   String image;
//   Country country;

//   City({
//     this.id,
//     this.userId,
//     this.countryId,
//     this.arabicName,
//     this.name,
//     this.image,
//     this.country,
//   });

//   factory City.fromJson(Map<String, dynamic> json) {
//     return City(
//       id: json['id'],
//       userId: json['user_id'],
//       countryId: json['country_id'],
//       arabicName: json['arabic_name'],
//       name: json['name'],
//       image: json['image'],
//       country: Country.fromJson(json['country']),
//     );
//   }
// }

// class Country {
//   String id;
//   int englishCurrencyId;
//   int arabicCurrencyId;
//   int internationalKeyId;
//   int countryFlagId;
//   String arabicName;
//   String name;
//   String createdAt;
//   String updatedAt;
//   EnglishCurrency englishCurrency;
//   ArabicCurrency arabicCurrency;
//   CountryFlag countryFlag;
//   InternationalKey internationalKey;

//   Country({
//     this.id,
//     this.englishCurrencyId,
//     this.arabicCurrencyId,
//     this.internationalKeyId,
//     this.countryFlagId,
//     this.arabicName,
//     this.name,
//     this.createdAt,
//     this.updatedAt,
//     this.englishCurrency,
//     this.arabicCurrency,
//     this.countryFlag,
//     this.internationalKey,
//   });

//   factory Country.fromJson(Map<String, dynamic> json) {
//     return Country(
//       id: json['id'],
//       englishCurrencyId: json['english_currency_id'],
//       arabicCurrencyId: json['arabic_currency_id'],
//       internationalKeyId: json['international_key_id'],
//       countryFlagId: json['country_flag_id'],
//       arabicName: json['arabic_name'],
//       name: json['name'],
//       createdAt: json['created_at'],
//       updatedAt: json['updated_at'],
//       englishCurrency: EnglishCurrency.fromJson(json['english_currency']),
//       arabicCurrency: ArabicCurrency.fromJson(json['arabic_currency']),
//       countryFlag: CountryFlag.fromJson(json['country_flag']),
//       internationalKey: InternationalKey.fromJson(json['international_key']),
//     );
//   }
// }

// class EnglishCurrency {
//   String id;
//   String name;

//   EnglishCurrency({
//     this.id,
//     this.name,
//   });

//   factory EnglishCurrency.fromJson(Map<String, dynamic> json) {
//     return EnglishCurrency(
//       id: json['id'],
//       name: json['name'],
//     );
//   }
// }

// class ArabicCurrency {
//   String id;
//   String name;

//   ArabicCurrency({
//     this.id,
//     this.name,
//   });

//   factory ArabicCurrency.fromJson(Map<String, dynamic> json) {
//     return ArabicCurrency(
//       id: json['id'],
//       name: json['name'],
//     );
//   }
// }

// class CountryFlag {
//   String id;
//   String countryFlag;

//   CountryFlag({
//     this.id,
//     this.countryFlag,
//   });

//   factory CountryFlag.fromJson(Map<String, dynamic> json) {
//     return CountryFlag(
//       id: json['id'],
//       countryFlag: json['country_flag'],
//     );
//   }
// }

// class InternationalKey {
//   String id;
//   String internationalKey;

//   InternationalKey({
//     this.id,
//     this.internationalKey,
//   });

//   factory InternationalKey.fromJson(Map<String, dynamic> json) {
//     return InternationalKey(
//       id: json['id'],
//       internationalKey: json['international_key'],
//     );
//   }
// }
