class TripStatus {
  String? id;
  String? driverId;
  String? enduserId;
  String? newRequestId;
  String? cancellationReasonId;
  String? ratingOptionId;
  dynamic status;
  String? paymentMethod;
  dynamic reportedDistance;
  dynamic lastLatLocation;
  dynamic lastLongLocation;
  dynamic pickupLatitude;
  dynamic pickupLongitude;
  dynamic destinationLatitude;
  dynamic destinationLongitude;
  dynamic cost;
  dynamic expectedCost;
  dynamic tripTime;
  dynamic priceId;
  dynamic cancelledBy;
  dynamic reason;
  dynamic driverRating;
  dynamic driverNote;
  dynamic enduserNote;
  dynamic enduserRating;
  dynamic origin;
  dynamic destination;
  String? referenceId;
  dynamic couponPaidAmount;
  dynamic walletPaidAmount;
  dynamic cashPaidAmount;
  dynamic commissionPaidAmount;
  dynamic carId;
  dynamic destinationArrivalTime;
  dynamic pickupArrivalTime;
  String? createdAt;
  String? updatedAt;

  TripStatus(
      {this.id,
      this.driverId,
      this.enduserId,
      this.newRequestId,
      this.cancellationReasonId,
      this.ratingOptionId,
      this.status,
      this.paymentMethod,
      this.reportedDistance,
      this.lastLatLocation,
      this.lastLongLocation,
      this.pickupLatitude,
      this.pickupLongitude,
      this.destinationLatitude,
      this.destinationLongitude,
      this.cost,
      this.expectedCost,
      this.tripTime,
      this.priceId,
      this.cancelledBy,
      this.reason,
      this.driverRating,
      this.driverNote,
      this.enduserNote,
      this.enduserRating,
      this.origin,
      this.destination,
      this.referenceId,
      this.couponPaidAmount,
      this.walletPaidAmount,
      this.cashPaidAmount,
      this.commissionPaidAmount,
      this.carId,
      this.destinationArrivalTime,
      this.pickupArrivalTime,
      this.createdAt,
      this.updatedAt});

  TripStatus.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    driverId = json['driver_id']?.toString();
    enduserId = json['enduser_id']?.toString();
    newRequestId = json['new_request_id']?.toString();
    cancellationReasonId = json['cancellation_reason_id']?.toString();
    ratingOptionId = json['rating_option_id']?.toString();
    status = json['status'];
    paymentMethod = json['payment_method'];
    reportedDistance = json['reported_distance'];
    lastLatLocation = json['last_lat_location'];
    lastLongLocation = json['last_long_location'];
    pickupLatitude = json['pickup_latitude'];
    pickupLongitude = json['pickup_longitude'];
    destinationLatitude = json['destination_latitude'];
    destinationLongitude = json['destination_longitude'];
    cost = json['cost'];
    expectedCost = json['expected_cost'];
    tripTime = json['trip_time'];
    priceId = json['price_id']?.toString();
    cancelledBy = json['cancelled_by'];
    reason = json['reason'];
    driverRating = json['driver_rating'];
    driverNote = json['driver_note'];
    enduserNote = json['enduser_note'];
    enduserRating = json['enduser_rating'];
    origin = json['origin'];
    destination = json['destination'];
    referenceId = json['reference_id']?.toString();
    couponPaidAmount = json['coupon_paid_amount'];
    walletPaidAmount = json['wallet_paid_amount'];
    cashPaidAmount = json['cash_paid_amount'];
    commissionPaidAmount = json['commission_paid_amount'];
    carId = json['car_id']?.toString();
    destinationArrivalTime = json['destination_arrival_time'];
    pickupArrivalTime = json['pickup_arrival_time'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}
