class Trip {
  String? id;
  String? driverId;
  String? endUserId;
  String? newRequestId;
  String? start;
  String? end;
  String? driverRating;
  String? customerRating;
  String? origin;
  String? destination;
  String? createdAt;
  String? updatedAt;

  Trip({
    this.id,
    this.driverId,
    this.endUserId,
    this.newRequestId,
    this.start,
    this.end,
    this.driverRating,
    this.customerRating,
    this.origin,
    this.destination,
    this.createdAt,
    this.updatedAt,
  });

  factory Trip.fromJson(Map<String, dynamic> json) => Trip(
        id: json['id'],
        driverId: json['driver_id'],
        endUserId: json['enduser_id'],
        newRequestId: json['new_request_id'],
        start: json['start'],
        end: json['end'],
        driverRating: json['driver_rating'],
        customerRating: json['customer_rating'],
        origin: json['origin'],
        destination: json['destination'],
        createdAt: json['created_at'],
        updatedAt: json['updated_at'],
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'driver_id': driverId,
        'enduser_id': endUserId,
        'new_request_id': newRequestId,
        'start': start,
        'end': end,
        'driver_rating': driverRating,
        'customer_rating': customerRating,
        'origin': origin,
        'destination': destination,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}
