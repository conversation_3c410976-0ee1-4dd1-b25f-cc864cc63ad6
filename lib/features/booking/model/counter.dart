class CounterModel {
  String? status;
  String? tripId;
  String? userId;
  DateTime? updatedAt;
  DateTime? createdAt;
  String? id;

  CounterModel({
    this.status,
    this.tripId,
    this.userId,
    this.updatedAt,
    this.createdAt,
    this.id,
  });

  factory CounterModel.fromJson(Map<String, dynamic> json) {
    return CounterModel(
      status: json['status']?.toString(),
      tripId: json['trip_id']?.toString(),
      userId: json['user_id']?.toString(),
      updatedAt: json['updated_at'] == null ? null : DateTime.parse(json['updated_at']),
      createdAt: json['created_at'] == null ? null : DateTime.parse(json['created_at']),
      id: json['id']?.toString(),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['trip_id'] = tripId;
    data['user_id'] = userId;
    data['updated_at'] = updatedAt?.toUtc().toIso8601String();
    data['created_at'] = createdAt?.toUtc().toIso8601String();
    data['id'] = id;
    return data;
  }
}

class CounterEnd {
  String? id;
  String? time;
  String? status;
  double? price;
  String? userId;
  String? tripId;
  DateTime? createdAt;
  DateTime? updatedAt;

  CounterEnd({
    this.id,
    this.time,
    this.status,
    this.price,
    this.userId,
    this.tripId,
    this.createdAt,
    this.updatedAt,
  });

  factory CounterEnd.fromJson(Map<String, dynamic> json) {
    return CounterEnd(
      id: json['id']?.toString(),
      time: json['time']?.toString(),
      status: json['status']?.toString(),
      price: double.tryParse(json['cost'].toString()) ?? 0,
      userId: json['user_id']?.toString(),
      tripId: json['trip_id']?.toString(),
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] == null ? null : DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {};
    data['id'] = id;
    data['time'] = time;
    data['status'] = status;
    data['price'] = price;
    data['user_id'] = userId;
    data['trip_id'] = tripId;
    data['created_at'] = createdAt?.toUtc().toIso8601String();
    data['updated_at'] = updatedAt?.toUtc().toIso8601String();
    return data;
  }
}
