class ComingRideRequest {
  final String? id;
  final String? userId;
  final String? driverId;
  final String? carTypeId;
  final String? couponId;
  final dynamic pickupLocation;
  double pickupLatitude = 0;
  double pickupLongitude = 0;
  final dynamic destinationLocation;
  double? destinationLatitude;

  double? destinationLongitude;
  double? lastLatLocation;

  double? lastLongLocation;
  final DateTime? dateTime;
  final dynamic fare;
  final dynamic cost;
  final dynamic status;
  final dynamic type;
  final String? paymentMethod;
  final String? referenceId;
  final String? cancelledBy;
  final String? commissionPaidAmount;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  PreviousEndUser? user;

  ComingRideRequest({
    required this.id,
    required this.userId,
    this.driverId,
    this.user,
    required this.carTypeId,
    this.couponId,
    this.cost,
    required this.pickupLocation,
    this.pickupLatitude = 0,
    this.pickupLongitude = 0,
    required this.destinationLocation,
    this.destinationLatitude,
    this.destinationLongitude,
    this.lastLatLocation,
    this.lastLongLocation,
    this.dateTime,
    this.paymentMethod,
    this.referenceId,
    this.cancelledBy,
    this.commissionPaidAmount,
    required this.fare,
    required this.status,
    required this.type,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ComingRideRequest.fromJson(Map<String, dynamic> json) {
    return ComingRideRequest(
      id: json['id']?.toString(),
      userId: json['user_id']?.toString(),
      driverId: json['driver_id']?.toString(),
      carTypeId: json['car_type_id']?.toString(),
      couponId: json['coupon_id']?.toString(),
      pickupLocation: json['pickup_location']?.toString(),
      pickupLatitude: double.tryParse(json['pickup_latitude'].toString()) ?? 0,
      pickupLongitude: double.tryParse(json['pickup_longitude'].toString()) ?? 0,
      destinationLocation: json['destination_location']?.toString(),
      cost: double.tryParse(json['cost'].toString()) ?? 0,
      destinationLatitude:
          json['destination_latitude'] == null ? null : double.tryParse(json['destination_latitude'].toString()) ?? 0,
      destinationLongitude:
          json['destination_longitude'] == null ? null : double.tryParse(json['destination_longitude'].toString()) ?? 0,
      lastLatLocation:
          json['last_lat_location'] == null ? null : double.tryParse(json['last_lat_location'].toString()) ?? 0,
      lastLongLocation:
          json['last_long_location'] == null ? null : double.tryParse(json['last_long_location'].toString()) ?? 0,
      dateTime: json['date_time'] != null ? DateTime.parse(json['date_time'] as String) : null,
      fare: json['fare']?.toString(),
      status: json['status']?.toString(),
      type: json['type']?.toString(),
      paymentMethod: json['payment_method']?.toString(),
      referenceId: json['reference_id']?.toString(),
      cancelledBy: json['cancelled_by']?.toString(),
      commissionPaidAmount: json['commission_paid_amount']?.toString(),
      createdAt: json['created_at'] == null ? null : DateTime.parse(json['created_at'] as String).toLocal(),
      updatedAt: json['updated_at'] == null ? null : DateTime.parse(json['updated_at'] as String).toLocal(),
      user: json['end_user'] == null ? null : PreviousEndUser.fromJson(json['end_user']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'driver_id': driverId,
      'car_type_id': carTypeId,
      'coupon_id': couponId,
      'pickup_location': pickupLocation,
      'pickup_latitude': pickupLatitude,
      'pickup_longitude': pickupLongitude,
      'destination_location': destinationLocation,
      'destination_latitude': destinationLatitude,
      'destination_longitude': destinationLongitude,
      'date_time': dateTime?.toIso8601String(),
      'fare': fare,
      'status': status,
      'type': type,
      'commission_paid_amount': commissionPaidAmount,
      'payment_method': paymentMethod,
      'reference_id': referenceId,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

class PreviousEndUser {
  String? id;
  String? deviceId;
  String? roleId;
  String? cityId;
  String? countryId;
  String? neighbourhoodId;
  String? blockConditionId;
  String? languageId;
  String? distanceId;
  dynamic email;
  String? latitude;
  String? longitude;
  dynamic direction;
  dynamic location;
  String? otp;
  dynamic radius;
  String? mobileNumber;
  dynamic whatsUpNumber;
  String? fullName;
  String? arabicFullName;
  String? rating;
  String? status;
  String? loginStatus;
  String? activationStatus;
  dynamic reason;
  dynamic gender;
  dynamic couponId;
  String? privacyPolicyId;
  String? termConditionId;
  String? accountTypeId;
  String? addressId;
  dynamic emailVerifiedAt;
  dynamic password2;
  String? profileImage;
  dynamic idPhoto;
  dynamic points;
  dynamic balance;
  dynamic deviceToken;
  String? availibilityStatus;
  String? createdAt;
  String? updatedAt;

  PreviousEndUser(
      {this.id,
      this.deviceId,
      this.roleId,
      this.cityId,
      this.countryId,
      this.neighbourhoodId,
      this.blockConditionId,
      this.languageId,
      this.distanceId,
      this.email,
      this.latitude,
      this.longitude,
      this.direction,
      this.location,
      this.otp,
      this.radius,
      this.mobileNumber,
      this.whatsUpNumber,
      this.fullName,
      this.arabicFullName,
      this.rating,
      this.status,
      this.loginStatus,
      this.activationStatus,
      this.reason,
      this.gender,
      this.couponId,
      this.privacyPolicyId,
      this.termConditionId,
      this.accountTypeId,
      this.addressId,
      this.emailVerifiedAt,
      this.password2,
      this.profileImage,
      this.idPhoto,
      this.points,
      this.balance,
      this.deviceToken,
      this.availibilityStatus,
      this.createdAt,
      this.updatedAt});

  PreviousEndUser.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    deviceId = json['device_id']?.toString();
    roleId = json['role_id']?.toString();
    cityId = json['city_id']?.toString();
    countryId = json['country_id']?.toString();
    neighbourhoodId = json['neighbourhood_id']?.toString();
    blockConditionId = json['block_condition_id']?.toString();
    languageId = json['language_id']?.toString();
    distanceId = json['distance_id']?.toString();
    email = json['email'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    direction = json['direction'];
    location = json['location'];
    otp = json['otp'];
    radius = json['radius'];
    mobileNumber = json['mobile_number'];
    whatsUpNumber = json['whats_up_number'];
    fullName = json['full_name'];
    arabicFullName = json['arabic_full_name'];
    rating = json['rating'];
    status = json['status'];
    loginStatus = json['login_status'];
    activationStatus = json['activation_status'];
    reason = json['reason'];
    gender = json['gender'];
    couponId = json['coupon_id']?.toString();
    privacyPolicyId = json['privacy_policy_id']?.toString();
    termConditionId = json['term_condition_id']?.toString();
    accountTypeId = json['account_type_id']?.toString();
    addressId = json['address_id']?.toString();
    emailVerifiedAt = json['email_verified_at'];
    password2 = json['password_2'];
    profileImage = json['profile_image'];
    idPhoto = json['id_photo'];
    points = json['points'];
    balance = json['balance'];
    deviceToken = json['device_token'];
    availibilityStatus = json['availibility_status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }
}
