class NearbyUser {
  final String id;
  final String latitude;
  final String longitude;

  const NearbyUser({
    required this.id,
    required this.latitude,
    required this.longitude,
  });

  factory NearbyUser.fromJson(Map<String, dynamic> map) {
    return NearbyUser(
      id: map['id'].toString(),
      latitude: map['latitude'] as String,
      longitude: map['longitude'] as String,
    );
  }
}
