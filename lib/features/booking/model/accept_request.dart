import 'package:yalla_gai_driver/environment.dart';

class RequestAccept {
  Trip? trip;
  NewRequest? newRequest;
  User? endUser;
  User? driverUser;
  Driver? driver;

  // dynamic carCategory;

  RequestAccept({
    this.trip,
    this.newRequest,
    this.endUser,
    this.driver,
    /*this.carCategory*/
  });

  RequestAccept.fromJson(Map<String, dynamic> json) {
    trip = json['trip'] != null ? Trip.fromJson(json['trip']) : null;
    newRequest = json['newRequest'] != null ? NewRequest.fromJson(json['newRequest']) : null;
    endUser = json['end-user'] != null ? User.fromJson(json['end-user']) : null;
    driverUser = json['driver-user'] != null ? User.fromJson(json['driver-user']) : null;
    driver = json['driver'] != null ? Driver.fromJson(json['driver']) : null;
    // carCategory = json['car_category'];
  }

  RequestAccept.activeTripFromJson(Map<String, dynamic> json) {
    trip = Trip.fromJson(json);
    newRequest = json['newRequest'] != null ? NewRequest.fromJson(json['newRequest']) : null;
    endUser = json['end-user'] != null ? User.fromJson(json['end-user']) : null;
    driverUser = json['driver-user'] != null ? User.fromJson(json['driver-user']) : null;
    driver = json['driver'] != null ? Driver.fromJson(json['driver']) : null;
    // carCategory = json['car_category'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (trip != null) {
      data['trip'] = trip!.toJson();
    }
    if (newRequest != null) {
      data['newRequest'] = newRequest!.toJson();
    }
    if (endUser != null) {
      data['user'] = endUser!.toJson();
    }
    if (driver != null) {
      data['driver'] = driver!.toJson();
    }
    // data['car_category'] = carCategory;
    return data;
  }
}

class Trip {
  String? newRequestId;
  String? priceId;
  String? driverId;
  dynamic status;
  dynamic lastLatLocation;
  dynamic lastLongLocation;
  String? referenceId;
  String? enduserId;
  dynamic paymentMethod;
  String? carId;
  dynamic updatedAt;
  dynamic createdAt;
  String? id;

  Trip(
      {this.newRequestId,
      this.priceId,
      this.driverId,
      this.lastLatLocation,
      this.status,
      this.lastLongLocation,
      this.referenceId,
      this.enduserId,
      this.paymentMethod,
      this.carId,
      this.updatedAt,
      this.createdAt,
      this.id});

  Trip.fromJson(Map<String, dynamic> json) {
    newRequestId = json['new_request_id']?.toString();
    priceId = json['price_id']?.toString();
    driverId = json['driver_id']?.toString();
    lastLatLocation = json['last_lat_location'];
    lastLongLocation = json['last_long_location'];
    referenceId = json['reference_id']?.toString();
    enduserId = json['enduser_id']?.toString();
    status = json['status'];
    paymentMethod = json['payment_method'];
    carId = json['car_id']?.toString();
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['new_request_id'] = newRequestId;
    data['price_id'] = priceId;
    data['driver_id'] = driverId;
    data['last_lat_location'] = lastLatLocation;
    data['last_long_location'] = lastLongLocation;
    data['reference_id'] = referenceId;
    data['enduser_id'] = enduserId;
    data['payment_method'] = paymentMethod;
    data['car_id'] = carId;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['id'] = id;
    return data;
  }
}

class NewRequest {
  String? id;
  String? userId;
  String? driverId;
  String? carId;
  String? couponId;
  dynamic pickupLocation;
  double pickupLatitude = 0;
  double pickupLongitude = 0;
  dynamic destinationLocation;
  double? destinationLatitude;
  double? destinationLongitude;
  dynamic roadMapLatitude;
  dynamic roadMapLongitude;
  dynamic dateTime;
  dynamic fare;
  dynamic status;
  dynamic type;
  String? priceId;
  dynamic paymentMethod;
  dynamic cancelledBy;
  dynamic reason;
  String? cancellationReasonId;
  String? createdAt;
  String? updatedAt;

  NewRequest(
      {this.id,
      this.userId,
      this.driverId,
      this.carId,
      this.couponId,
      this.pickupLocation,
      this.pickupLatitude = 0,
      this.pickupLongitude = 0,
      this.destinationLocation,
      this.destinationLatitude,
      this.destinationLongitude,
      this.roadMapLatitude,
      this.roadMapLongitude,
      this.dateTime,
      this.fare,
      this.status,
      this.type,
      this.priceId,
      this.paymentMethod,
      this.cancelledBy,
      this.reason,
      this.cancellationReasonId,
      this.createdAt,
      this.updatedAt});

  NewRequest.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    driverId = json['driver_id']?.toString();
    carId = json['car_id']?.toString();
    couponId = json['coupon_id']?.toString();
    pickupLocation = json['pickup_location'];
    pickupLatitude = double.tryParse(json['pickup_latitude'].toString()) ?? 0;
    pickupLongitude = double.tryParse(json['pickup_longitude'].toString()) ?? 0;
    destinationLocation = json['destination_location'];
    destinationLatitude =
        json['destination_latitude'] == null ? null : double.tryParse(json['destination_latitude'].toString()) ?? 0;
    destinationLongitude =
        json['destination_longitude'] == null ? null : double.tryParse(json['destination_longitude'].toString()) ?? 0;
    roadMapLatitude = json['road_map_latitude'];
    roadMapLongitude = json['road_map_longitude'];
    dateTime = json['date_time'];
    fare = json['fare'];
    status = json['status'];
    type = json['type'];
    priceId = json['price_id']?.toString();
    paymentMethod = json['payment_method'];
    cancelledBy = json['cancelled_by'];
    reason = json['reason'];
    cancellationReasonId = json['cancellation_reason_id']?.toString();
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['driver_id'] = driverId;
    data['car_id'] = carId;
    data['coupon_id'] = couponId;
    data['pickup_location'] = pickupLocation;
    data['pickup_latitude'] = pickupLatitude;
    data['pickup_longitude'] = pickupLongitude;
    data['destination_location'] = destinationLocation;
    data['destination_latitude'] = destinationLatitude;
    data['destination_longitude'] = destinationLongitude;
    data['road_map_latitude'] = roadMapLatitude;
    data['road_map_longitude'] = roadMapLongitude;
    data['date_time'] = dateTime;
    data['fare'] = fare;
    data['status'] = status;
    data['type'] = type;
    data['price_id'] = priceId;
    data['payment_method'] = paymentMethod;
    data['cancelled_by'] = cancelledBy;
    data['reason'] = reason;
    data['cancellation_reason_id'] = cancellationReasonId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class User {
  String? id;
  String? deviceId;
  String? roleId;
  String? cityId;
  String? countryId;
  String? neighbourhoodId;
  String? blockConditionId;
  String? languageId;
  String? distanceId;
  String? email;
  dynamic latitude;
  dynamic longitude;
  dynamic direction;
  dynamic location;
  dynamic otp;
  dynamic radius;
  dynamic mobileNumber;
  dynamic fullName;
  dynamic arabicFullName;
  dynamic rating;
  dynamic status;
  dynamic loginStatus;
  dynamic activationStatus;
  dynamic reason;
  dynamic gender;
  String? couponId;
  String? privacyPolicyId;
  String? termConditionId;
  String? accountTypeId;
  String? addressId;
  dynamic emailVerifiedAt;
  dynamic password2;
  dynamic profileImage;
  dynamic idPhoto;
  dynamic points;
  dynamic balance;
  dynamic deviceToken;
  dynamic availibilityStatus;
  String? createdAt;
  String? updatedAt;
  Role? role;

  User(
      {this.id,
      this.deviceId,
      this.roleId,
      this.cityId,
      this.countryId,
      this.neighbourhoodId,
      this.blockConditionId,
      this.languageId,
      this.distanceId,
      this.email,
      this.latitude,
      this.longitude,
      this.direction,
      this.location,
      this.otp,
      this.radius,
      this.mobileNumber,
      this.fullName,
      this.arabicFullName,
      this.rating,
      this.status,
      this.loginStatus,
      this.activationStatus,
      this.reason,
      this.gender,
      this.couponId,
      this.privacyPolicyId,
      this.termConditionId,
      this.accountTypeId,
      this.addressId,
      this.emailVerifiedAt,
      this.password2,
      this.profileImage,
      this.idPhoto,
      this.points,
      this.balance,
      this.deviceToken,
      this.availibilityStatus,
      this.createdAt,
      this.updatedAt,
      this.role});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    deviceId = json['device_id']?.toString();
    roleId = json['role_id']?.toString();
    cityId = json['city_id']?.toString();
    countryId = json['country_id']?.toString();
    neighbourhoodId = json['neighbourhood_id']?.toString();
    blockConditionId = json['block_condition_id']?.toString();
    languageId = json['language_id']?.toString();
    distanceId = json['distance_id']?.toString();
    email = json['email'];
    latitude = json['latitude'];
    longitude = json['longitude'];
    direction = json['direction'];
    location = json['location'];
    otp = json['otp'];
    radius = json['radius'];
    mobileNumber = json['mobile_number'];
    fullName = json['full_name'];
    arabicFullName = json['arabic_full_name'];
    rating = json['rating'];
    status = json['status'];
    loginStatus = json['login_status'];
    activationStatus = json['activation_status'];
    reason = json['reason'];
    gender = json['gender'];
    couponId = json['coupon_id']?.toString();
    privacyPolicyId = json['privacy_policy_id']?.toString();
    termConditionId = json['term_condition_id']?.toString();
    accountTypeId = json['account_type_id']?.toString();
    addressId = json['address_id']?.toString();
    emailVerifiedAt = json['email_verified_at'];
    password2 = json['password_2'];
    profileImage = "${AppEnvironment.publicBaseUrl}${json['profile_image']}";
    idPhoto = json['id_photo'];
    points = json['points'];
    balance = json['balance'];
    deviceToken = json['device_token'];
    availibilityStatus = json['availibility_status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    role = json['role'] != null ? Role.fromJson(json['role']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['device_id'] = deviceId;
    data['role_id'] = roleId;
    data['city_id'] = cityId;
    data['country_id'] = countryId;
    data['neighbourhood_id'] = neighbourhoodId;
    data['block_condition_id'] = blockConditionId;
    data['language_id'] = languageId;
    data['distance_id'] = distanceId;
    data['email'] = email;
    data['latitude'] = latitude;
    data['longitude'] = longitude;
    data['direction'] = direction;
    data['location'] = location;
    data['otp'] = otp;
    data['radius'] = radius;
    data['mobile_number'] = mobileNumber;
    data['full_name'] = fullName;
    data['arabic_full_name'] = arabicFullName;
    data['rating'] = rating;
    data['status'] = status;
    data['login_status'] = loginStatus;
    data['activation_status'] = activationStatus;
    data['reason'] = reason;
    data['gender'] = gender;
    data['coupon_id'] = couponId;
    data['privacy_policy_id'] = privacyPolicyId;
    data['term_condition_id'] = termConditionId;
    data['account_type_id'] = accountTypeId;
    data['address_id'] = addressId;
    data['email_verified_at'] = emailVerifiedAt;
    data['password_2'] = password2;
    data['profile_image'] = profileImage;
    data['id_photo'] = idPhoto;
    data['points'] = points;
    data['balance'] = balance;
    data['device_token'] = deviceToken;
    data['availibility_status'] = availibilityStatus;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (role != null) {
      data['role'] = role!.toJson();
    }
    return data;
  }
}

class Role {
  String? id;
  String? role;
  String? createdAt;
  String? updatedAt;

  Role({this.id, this.role, this.createdAt, this.updatedAt});

  Role.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    role = json['role']?.toString();
    createdAt = json['created_at']?.toString();
    updatedAt = json['updated_at']?.toString();
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['role'] = role;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}

class Driver {
  String? id;
  String? userId;
  dynamic idNumber;
  dynamic dateOfBirth;
  dynamic clientsGender;
  List? criminalRecordImages;
  List? driverIdImages;
  List? drivingLicencesImages;
  List? driverCertificatesImages;
  List? carImages;
  String? carModelId;
  String? carModelBrandId;
  dynamic yearOfManufacturer;
  dynamic plateNumber;
  dynamic carColor;
  dynamic carCategoriesIds;
  dynamic currentCarCategoriesIds;
  String? createdAt;
  String? updatedAt;
  dynamic carModel;
  dynamic carModelBrand;
  dynamic joinedSince;

  Driver(
      {this.id,
      this.userId,
      this.idNumber,
      this.dateOfBirth,
      this.clientsGender,
      this.criminalRecordImages,
      this.driverIdImages,
      this.drivingLicencesImages,
      this.driverCertificatesImages,
      this.carImages,
      this.carModelId,
      this.carModelBrandId,
      this.yearOfManufacturer,
      this.plateNumber,
      this.carColor,
      this.carCategoriesIds,
      this.currentCarCategoriesIds,
      this.createdAt,
      this.updatedAt,
      this.carModel,
      this.carModelBrand,
      this.joinedSince});

  Driver.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    userId = json['user_id']?.toString();
    idNumber = json['id_number'];
    dateOfBirth = json['date_of_birth'];
    clientsGender = json['clients_gender'];
    criminalRecordImages = json['criminal_record_images'];
    driverIdImages = json['driver_id_images'];
    drivingLicencesImages = json['driving_licences_images'];
    driverCertificatesImages = json['driver_certificates_images'];
    carImages = json['car_images'];
    carModelId = json['car_model_id']?.toString();
    carModelBrandId = json['car_model_brand_id']?.toString();
    yearOfManufacturer = json['year_of_manufacturer'];
    plateNumber = json['plate_number'];
    carColor = json['car_color'];
    carCategoriesIds = json['car_categories_ids'];
    currentCarCategoriesIds = json['current_car_categories_ids'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    carModel = json['car_model'];
    carModelBrand = json['car_model_brand'];
    joinedSince = json['joined_since'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['user_id'] = userId;
    data['id_number'] = idNumber;
    data['date_of_birth'] = dateOfBirth;
    data['clients_gender'] = clientsGender;
    data['criminal_record_images'] = criminalRecordImages;
    data['driver_id_images'] = driverIdImages;
    data['driving_licences_images'] = drivingLicencesImages;
    data['driver_certificates_images'] = driverCertificatesImages;
    data['car_images'] = carImages;
    data['car_model_id'] = carModelId;
    data['car_model_brand_id'] = carModelBrandId;
    data['year_of_manufacturer'] = yearOfManufacturer;
    data['plate_number'] = plateNumber;
    data['car_color'] = carColor;
    data['car_categories_ids'] = carCategoriesIds;
    data['current_car_categories_ids'] = currentCarCategoriesIds;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    data['car_model'] = carModel;
    data['car_model_brand'] = carModelBrand;
    data['joined_since'] = joinedSince;
    return data;
  }
}
