import 'dart:convert';

import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/booking/model/nearby_user.dart';
import 'package:yalla_gai_driver/features/booking/model/service.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';

class ServiceRepository {
  final _httpClient = HttpApiClient();

  ServiceRepository();

  Future<List<Service>> getServices() async {
    var response = await _httpClient.get(EndPoints.getServices());

    Map<String, dynamic> responseData = jsonDecode(response.body);
    return (responseData['services'] as List).map((json) => Service.fromJson(json)).toList();
  }

  ///  end Counter
  Future<bool?> changeServiceStatus(String? id, String status) async {
    final response = await _httpClient.post(
      EndPoints.changeAvaliablity,
      body: jsonEncode({"availibility_status": status, "current_car_categories_ids": id}),
    );
    return true;
  }

  Future<List<NearbyUser>> getNearbyUsers() async {
    final response = await _httpClient.get(EndPoints.getNearByUsers());
    final responseData = jsonDecode(response.body);

    if (responseData["status"] == false) {
      final errors = responseData["errors"].values.toList();
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
    if (responseData["data"] is Iterable) {
      return List.from(responseData["data"]).map((e) => NearbyUser.fromJson(e)).toList();
    }
    return [];
  }
}
