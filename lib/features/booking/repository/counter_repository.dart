import 'dart:convert';

import 'package:http/http.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/booking/model/counter.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';

class CounterRepository {
  final _httpClient = HttpApiClient();

  Future<double> getCounterFees() async {
    final response = await _httpClient.get(EndPoints.getFees("counter_fee"));
    final responseData = jsonDecode(response.body);
    if (responseData["status"] == true) {
      final data = responseData['data'];
      final amount = double.tryParse(data["amount"] ?? "0.1") ?? 1;
      LocalStorage.instance.counterFees = amount;
      return amount;
    } else {
      throw ServerException(message: responseData["message"]);
    }
  }

  Future startCounter() async {
    final response = await _httpClient.send(MultipartRequest('POST', EndPoints.startCounter()));
    final responseBody = await response.stream.toBytes();
    var responseData = await jsonDecode(String.fromCharCodes(responseBody));
    return CounterModel.fromJson(responseData['data']);
  }

  Future endCounter(String tripId) async {
    var response = await _httpClient.send(MultipartRequest('POST', EndPoints.endCounter(tripId)));
    final responseBody = await response.stream.toBytes();
    var responseData = await jsonDecode(String.fromCharCodes(responseBody));
    return CounterEnd.fromJson(responseData['data']);
  }

  Future updateGender(String gender) async {
    var response = await _httpClient.post(
      EndPoints.updateGender(),
      body: jsonEncode({'clients_gender': gender}),
    );
    var responseData = jsonDecode(response.body);
    return;
  }
}
