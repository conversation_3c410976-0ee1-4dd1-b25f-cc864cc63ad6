import 'dart:convert';
import 'dart:developer';

import 'package:http/http.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/model/accept_request.dart';
import 'package:yalla_gai_driver/features/booking/model/reject_reason.dart';
import 'package:yalla_gai_driver/features/booking/model/ride_status.dart';

import '../../../../core/error/exception.dart';
import '../../../core/constants/end_points.dart';
import '../model/ride_request.dart';

dynamic cityId;

class TripRepository {
  final _httpClient = HttpApiClient();

  Future getStartedTrip(String newRequestId) async {
    var response = await _httpClient.get(EndPoints.getStartTrip(LocalStorage.instance.userId ?? "", newRequestId));
    Map<String, dynamic> responseData = jsonDecode(response.body);
    return;
  }

  Future getTripCancelReason() async {
    var response = await _httpClient.get(EndPoints.rejectTripReason());
    Map<String, dynamic> responseData = jsonDecode(response.body);
    List<TripCancelReason> reasons = [];
    for (var reason in responseData['data']) {
      final data = TripCancelReason.fromjson(reason);
      if (data.roleId == "3" && data.cityId == globalDriverProfile.user?.cityId) {
        reasons.add(TripCancelReason.fromjson(reason));
      }
    }
    return reasons;
  }

  Future endTrip(String newRequestId) async {
    var response = await _httpClient.get(EndPoints.endtrip(LocalStorage.instance.userId ?? "", newRequestId));
    Map<String, dynamic> responseData = jsonDecode(response.body);
    return;
  }

  Future storeRadius(String radius) async {
    var response = await _httpClient.post(
      EndPoints.storeRadius(),
      body: jsonEncode({'radius': radius}),
    );
    Map<String, dynamic> responseData = jsonDecode(response.body);
    if (responseData['status'] == true) {
      return;
    } else {
      final errors = responseData["errors"].values.toList();
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  Future rateCustomer(String id, String rate, String note) async {
    var response = await _httpClient.put(
      EndPoints.rateCustomer(id),
      body: jsonEncode({'enduser_rating': rate, if (note.isNotEmpty) 'enduser_note': note}),
    );
    Map<String, dynamic> responseData = jsonDecode(response.body);
    return;
  }

  Future rejectTrip(String tripID, {reasonID, reason}) async {
    final response = await _httpClient.post(
      EndPoints.rejectTrip(tripID),
      body: jsonEncode({
        'reason': reason,
        if (reasonID != null && reasonID.toString().isNotEmpty) 'cancellation_reason_id': "$reasonID",
        'user_id': globalDriverProfile.user?.id,
      }),
    );

    Map<String, dynamic> responseData = jsonDecode(response.body);
    await _rejectTripSecondMethod(tripID);
    return;
  }

  Future _rejectTripSecondMethod(String tripID) async {
    await _httpClient.send(MultipartRequest('POST', EndPoints.rejectTrip2(tripID)));
    return;
  }

  Future<List<ComingRideRequest>> getPreviousTrips() async {
    final response = await _httpClient.get(EndPoints.previousTrips());
    Map<String, dynamic> responseData = jsonDecode(response.body);

    if (responseData['status'] == true) {
      return (responseData['data'] as List).map((e) => ComingRideRequest.fromJson(e)).toList();
    } else {
      final errors = responseData["errors"].values.toList();
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  Future<RequestAccept> getActiveTrip() async {
    var response = await _httpClient.get(EndPoints.getActiveTrip());
    Map<String, dynamic> responseData = jsonDecode(response.body);

    if (responseData['status'] == true) {
      log("Active Trip:${responseData['data']}");
      final requestAccept = RequestAccept.fromJson(responseData['data']);
      return requestAccept;
    } else {
      final errors = responseData["errors"].values.toList();
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }

  Future acceptNewRequest(String tripId) async {
    var response = await _httpClient.send(MultipartRequest('POST', EndPoints.acceptNewRequest(tripId)));
    final responseBody = await response.stream.toBytes();
    var responseData = await jsonDecode(String.fromCharCodes(responseBody));
    print("accept ride => $responseData");
    return RequestAccept.fromJson(responseData['data']);
  }

  Future changeTripStatus({required String tripId, required int status}) async {
    var response = await _httpClient.post(
      EndPoints.changeTripStatus(),
      body: jsonEncode({"status": "$status", 'trip_id': tripId}),
    );
    var responseData = jsonDecode(response.body);
    return TripStatus.fromJson(responseData["data"]);
  }

  Future updateLocation({
    required String lat,
    required String lng,
    String? direction,
    String? tripId,
    bool byGoogle = true,
  }) async {
    final requestBody = {
      'latitude': lat,
      'longitude': lng,
      if (direction != null) 'direction': direction,
      if (tripId != null) 'trip_id': tripId,
      'user_id': LocalStorage.instance.userId,
    };

    var response = await _httpClient.put(
      EndPoints.updateLocation(byGoogle: byGoogle),
      body: jsonEncode(requestBody),
    );

    var responseString = response.body;
    final body = jsonDecode(responseString);

    if (body['data'] != null) {
      cityId = body['data']['city_id']?.toString();
      log("city$cityId");
    } // getLiveLocation();
    return true;
  }

  /*void getLiveLocation() async {
    try {
      final permission = await Geolocator.checkPermission();
      if (!(permission == LocationPermission.denied || permission == LocationPermission.deniedForever)) {
        final position = await Geolocator.getCurrentPosition();
        updateLocation(
            byGoogle: false,
            lat: position.latitude.toString(),
            lng: position.altitude.toString(),
            direction: position.heading.toString());
        Geolocator.getPositionStream(locationSettings: const LocationSettings(distanceFilter: 20)).listen((event) {
          updateLocation(
              byGoogle: false,
              lat: event.latitude.toString(),
              lng: event.altitude.toString(),
              direction: position.heading.toString());
        });
      } else {}
    } catch (e) {
      log(e.toString());
    }
  }*/

  Future<double> getWatingTime(String type) async {
    var response = await _httpClient.get(EndPoints.getWaitTime(type));
    Map<String, dynamic> responseData = jsonDecode(response.body);

    if (responseData['status'] == true) {
      String waitTimeString = responseData['data'] == null ? "30" : responseData['data']['waiting_time'];
      double waitTime = double.tryParse(waitTimeString) ?? 30;
      LocalStorage.instance.waitTime = waitTime;
      return waitTime;
    } else {
      final errors = responseData["errors"].values.toList();
      String message = errors.toString().replaceAll("]", '').replaceAll("[", "");
      throw ServerException(message: message);
    }
  }
}
