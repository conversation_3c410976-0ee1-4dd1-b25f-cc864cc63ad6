import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:yalla_gai_driver/core/constants/end_points.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';

class ChatsRemoteDatasource {
  final _httpClient = HttpApiClient();

  Future<http.Response> sendChat({dynamic receiverID, required String message}) async {
    return await _httpClient.post(
      EndPoints.sendChat(),
      body: jsonEncode({
        "receiver_id": "$receiverID",
        "sender_id": LocalStorage.instance.userId,
        "message": message,
      }),
    );
  }

  Future<http.Response> fetchChat({required dynamic receiverID}) async {
    return await _httpClient.get(EndPoints.fetchChat(receiverID, LocalStorage.instance.userId ?? ""));
  }
}
