import 'dart:convert';

import 'package:yalla_gai_driver/core/constants/end_points.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/features/booking/model/system_service_model.dart';

class SystemServiceRepository {
  final _httpClient = HttpApiClient();

  Future<List<SystemService>> fetchSystemServices() async {
    final response = await _httpClient.get(EndPoints.systemServicesList());

    final responseData = jsonDecode(response.body);
    if (responseData case {"data": Iterable data}) {
      return data.map((e) => SystemService.fromJson(e)).toList();
    }
    return [];
  }
}
