import 'dart:convert';

import 'package:http/http.dart' as http;
import 'package:yalla_gai_driver/core/constants/end_points.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';

class TechSupportRemoteDatasource {
  final _httpClient = HttpApiClient();

  Future<http.Response> sendChat({required String message}) async {
    return await _httpClient.post(
      EndPoints.sendTechSupportChats(),
      body: jsonEncode({"receiver_id": "1", "message": message}),
    );
  }

  Future<http.Response> fetchChat() async {
    return await _httpClient.get(
      EndPoints.getTechSupportChats(),
    );
  }
}
