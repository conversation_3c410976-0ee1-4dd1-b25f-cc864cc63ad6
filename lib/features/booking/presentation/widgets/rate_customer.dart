import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_textfield.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/custom_divider.dart';

import '../../../../core/constants/constants.dart';
import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import 'star_rating.dart';

class RateCustomer extends StatefulWidget {
  final String id;
  final String price;

  const RateCustomer({super.key, required this.id, required this.price});

  @override
  State<RateCustomer> createState() => _RateCustomerState();
}

class _RateCustomerState extends State<RateCustomer> {
  int rating = 3;

  updateRating(int index) {
    setState(() {
      rating = index + 1;
    });
  }

  final controller = TextEditingController();

  @override
  void dispose() {
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    final acceptedTrip = context.watch<TripBloc>().requestAccept;
    var textTheme = Theme.of(context).textTheme;
    return Column(mainAxisAlignment: MainAxisAlignment.center, mainAxisSize: MainAxisSize.min, children: [
      Container(
          width: 85.w,
          padding: EdgeInsets.all(5.w),
          decoration: BoxDecoration(
            boxShadow: [
              BoxShadow(
                color: black.withOpacity(0.25),
                spreadRadius: 0,
                blurRadius: 4,
                offset: const Offset(0, 4), // changes the position of the shadow
              ),
              BoxShadow(
                color: black.withOpacity(0.25),
                spreadRadius: 0,
                blurRadius: 4,
                offset: const Offset(0, 4), // changes the position of the shadow
              ),
            ],
            color: white,
            borderRadius: BorderRadius.circular(10),
          ),
          child: Column(children: [
            Text(
              AppLocalizations.of(context)!.rate_customer,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const CustomDivider(),
            const Gap(),
            Container(
              height: 116,
              width: 355,
              padding: const EdgeInsets.symmetric(horizontal: 20),
              decoration: BoxDecoration(
                color: primaryColorDark.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CircleAvatar(
                        minRadius: 30,
                        maxRadius: 30,
                        backgroundImage: NetworkImage("${acceptedTrip.endUser?.profileImage}"),
                        // backgroundColor: grey,
                      ),
                      Gap(w: 3.w),
                      SizedBox(
                        width: 23.w,
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                  Utils.appText(
                                      context: context,
                                      text: acceptedTrip.endUser?.fullName ?? "",
                                      arabicText: acceptedTrip.endUser?.arabicFullName ?? ""),
                                  style: textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold)),
                              Gap(h: 1.h),
                              Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                                const Icon(Icons.star, color: primaryColor),
                                Gap(w: 2.w),
                                Text(rating.toString(),
                                    style: textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold))
                              ]),
                            ]),
                      )
                    ],
                  ),
                  Container(
                    width: 20.w,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: primaryColorDark,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${((double.tryParse(widget.price) ?? 0) / 100).toStringAsFixed(2)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}',
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(fontWeight: FontWeight.bold, color: white),
                    ),
                  ),
                ],
              ),
            ),
            const Gap(),
            StarRating(
              onUpdateRating: updateRating,
              rating: rating,
            ),
            const Gap(),
            Visibility(
              visible: rating <= 2,
              child: CustomTextField(
                width: 65.5.w,
                labelText: AppLocalizations.of(context)!.rate_customer,
                validator: (v) => null,
                textEditingController: controller,
                borderRadius: 4,
              ),
            ),
            const Gap(),
            const Gap(),
            BlocConsumer<TripBloc, TripState>(listener: (context, state) {
              if (state is TripRateCustomerSuccess) {
                rideRequestAccepted = false;

                context.push(path.home);
              }
            }, builder: (context, state) {
              return CustomButton(
                isLoading: state is TripRateCustomerLoading,
                height: 5.h,
                buttonText: AppLocalizations.of(context)!.rate_customer,
                borderRadius: 12,
                onPressed: () {
                  log("on pressed");
                  BlocProvider.of<TripBloc>(context)
                      .add(TripRateCustomer(message: controller.text, rating: rating.toString(), requestId: widget.id));
                  // GoRouter.of(context).pushReplacementNamed(path.home);
                  // Navigator.pushNamedAndRemoveUntil(
                  //     context, path.home, (route) => false);
                },
              );
            })
          ]))
    ]);
  }
}
