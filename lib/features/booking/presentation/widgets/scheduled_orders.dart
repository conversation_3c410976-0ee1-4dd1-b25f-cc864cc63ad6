import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import 'scheduled_order_card.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class ScheduledOrders extends StatelessWidget {
  const ScheduledOrders({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.sizeOf(context).height * 0.7,
        decoration: BoxDecoration(
            color: white, borderRadius: BorderRadius.circular(20)),
        width: MediaQuery.sizeOf(context).width,
        child: SingleChildScrollView(
          child: Column(
            children: [
              const Gap(),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.schedule),
                  Gap(w: 3.w),
                   Text(AppLocalizations.of(context)!.scheduled_orders,
                      style: const TextStyle(fontWeight: FontWeight.bold))
                ],
              ),
              const CustomDivider(),
              ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: 4,
                  itemBuilder: (context, index) {
                    return const ScheduledOrderCard();
                  })
            ],
          ),
        ));
  }
}
