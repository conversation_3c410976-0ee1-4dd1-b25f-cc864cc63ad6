import 'dart:math' as math show sin, pi, cos, sqrt;

import 'package:flutter/material.dart';

import '../../../../core/utils/colors.dart';

class Ripples extends StatefulWidget {
  const Ripples({
    Key? key,
    this.size = 80.0,
    this.color = const Color.fromARGB(255, 222, 46, 34),
    this.onPressed,
    required this.child,
    this.isCircle = false, // New isCircle parameter
  }) : super(key: key);

  final double size;
  final Color color;
  final Widget child;
  final VoidCallback? onPressed;
  final bool isCircle; // New isCircle parameter

  @override
  _RipplesState createState() => _RipplesState();
}

class _RipplesState extends State<Ripples> with TickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(milliseconds: widget.isCircle ? 2500 : 900),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Widget _button() {
    return Center(
      child: ClipRRect(
        borderRadius: BorderRadius.circular(widget.size),
        child: DecoratedBox(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              colors: <Color>[
                widget.color,
                black.withOpacity(0.3),
              ],
            ),
          ),
          child: ScaleTransition(
            scale: Tween(begin: 0.95, end: 1.0).animate(
              CurvedAnimation(
                parent: _controller,
                curve: const _PulsateCurve(),
              ),
            ),
            child: widget.child,
          ),
        ),
      ),
    );
  }

  Widget _circleButton() {
    return Center(
      child: ClipOval(
        child: DecoratedBox(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              colors: <Color>[
                widget.color,
                black.withOpacity(0.3),
              ],
            ),
          ),
          child: ScaleTransition(
            scale: Tween(begin: 0.99, end: 1.0).animate(
              CurvedAnimation(
                parent: _controller,
                curve: const _PulsateCurve(),
              ),
            ),
            child: widget.child,
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CirclePainter(
        _controller,
        color: widget.color,
        isCircle: widget.isCircle, // Pass the isCircle parameter to the painter
      ),
      child: SizedBox(
        width: widget.size * 2.125,
        height: widget.size * 2.125,
        child: widget.isCircle
            ? _circleButton()
            : _button(), // Use the appropriate button shape based on isCircle parameter
      ),
    );
  }
}

class _PulsateCurve extends Curve {
  const _PulsateCurve();

  @override
  double transform(double t) {
    if (t == 0 || t == 1) {
      return 0.01;
    }
    return math.sin(t * math.pi);
  }
}

class _CirclePainter extends CustomPainter {
  _CirclePainter(
    this._animation, {
    required this.color,
    required this.isCircle, // New isCircle parameter
  }) : super(repaint: _animation);

  final Color color;
  final Animation<double> _animation;
  final bool isCircle; // New isCircle parameter

  void shape(Canvas canvas, Rect rect, double value) {
    final double opacity = (1.0 - (value / 4.0)).clamp(0.0, 1.0);
    final Color _color = color.withOpacity(opacity);

    final Paint paint = Paint()..color = _color;

    if (isCircle) {
      final double radius = rect.width / 2 * value / 2;
      final Offset center = rect.center;

      canvas.drawCircle(center, radius, paint);
    } else {
      final double size = rect.width / 3;
      final double radius = size * value / 2;

      const double angle = 2 * math.pi / 8;
      final List<Offset> points = List.generate(8, (index) {
        final double x = rect.center.dx + radius * math.cos(index * angle);
        final double y = rect.center.dy + radius * math.sin(index * angle);
        return Offset(x, y);
      });

      final Paint paint = Paint()..color = _color;
      final Path path = Path()..addPolygon(points, true);
      canvas.drawPath(path, paint);
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    final Rect rect = Rect.fromLTRB(0.0, 0.0, size.width, size.height);

    for (int wave = 3; wave >= 0; wave--) {
      shape(canvas, rect, wave + _animation.value);
    }
  }

  @override
  bool shouldRepaint(_CirclePainter oldDelegate) => true;
}
