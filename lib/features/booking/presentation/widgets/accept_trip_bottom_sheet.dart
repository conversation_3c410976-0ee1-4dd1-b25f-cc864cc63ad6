import 'dart:async';
import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/constants/app_utils.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/chat/chats_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/ride_request/ride_request_cubit.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/reject_trip_popup.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/launch_google_maps.dart';
import '../../../../core/utils/phone_url_luncher.dart';
import '../../bloc/trip_bloc/trip_bloc.dart';
import '../../model/ride_request.dart';
import 'circle_button_with_icon.dart';
import 'get_directions.dart';
import 'swipe_button.dart';

class AcceptTripBottomSheet extends StatefulWidget {
  final ComingRideRequest rideRequest;

  const AcceptTripBottomSheet({
    super.key,
    required this.rideRequest,
  });

  @override
  State<AcceptTripBottomSheet> createState() => _AcceptTripBottomSheetState();
}

class _AcceptTripBottomSheetState extends State<AcceptTripBottomSheet> {
  bool startButtonOn = false;
  bool cancelButtonOn = true;
  late int countdown;
  Color textColor = primaryColor;
  Timer? timer;

  @override
  void initState() {
    super.initState();
    if (mounted) {
      startButtonOn =
          context.read<TripBloc>().requestAccept.trip?.status.toString() == "2";
    }
    countdown = AppUtils.waitTime.toInt();
    CustomSocket.reSubscribeChannel(
      channelName: 'private-user-send-message-channel_${globalDriverProfile.user?.id}',
    );
    startCountdown();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  void startCountdown() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown > 0) {
        if (mounted) {
          setState(() {
            countdown--;
            if (countdown <= 5) {
              textColor = Colors.red;
            }
          });
        }
      } else {
        timer.cancel();
        setState(() {
          cancelButtonOn = false;
        });
      }
    });
  }

  String formatSeconds(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;

    String minutesStr = minutes.toString().padLeft(2, '0');
    String secondsStr = remainingSeconds.toString().padLeft(2, '0');

    return '$minutesStr:$secondsStr';
  }

  @override
  Widget build(BuildContext context) {
    return tripNotStartedWidget();
  }

  Widget tripNotStartedWidget() {
    var textTheme = Theme.of(context).textTheme;
    final requestAccept = context.read<TripBloc>().requestAccept;
    return BlocListener<RideRequestCubit, RideRequestState>(
      listener: (context, state) {
        if (state is RideRequestCancelled) {
          showCustomSnackbar(
            context,
            AppLocalizations.of(context)!.trip_request_cancelled,
          );
          context.go(path.home);
        }
      },
      child: BlocListener<TripBloc, TripState>(
        listener: (context, state) {
          if (state is TripRejected) {
            timer?.cancel();
            context.push(path.home);
          }
          if (state is TripRejectError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                backgroundColor: Colors.red,
                content: Center(
                  child: Text(
                    Utils.appText(
                      context: context,
                      listen: false,
                      text: "Failed to reject trip",
                      arabicText: "فشل رفض الرحلة",
                    ),
                  ),
                ),
              ),
            );
          }
        },
        child: Container(
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(
            color: white,
            borderRadius: BorderRadius.circular(20),
          ),
          child: SingleChildScrollView(
            child: Column(
              children: [
                Gap(h: 3.w),
                if (countdown > 0)
                  Row(
                    children: [
                      Gap(w: 6.w),
                      Icon(
                        Icons.timer,
                        color: textColor,
                        size: 13,
                      ),
                      Gap(w: 1.w),
                      Text(
                        formatSeconds(countdown),
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              color: textColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                            ),
                      ),
                      Gap(w: 2.w),
                    ],
                  )
                else
                  Gap(h: 2.w),
                Gap(h: 1.w),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 3.w),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircleAvatar(
                            minRadius: 25,
                            maxRadius: 25,
                            backgroundImage: NetworkImage(
                              "${requestAccept.endUser?.profileImage}",
                            ),
                          ),
                          Gap(w: 3.w),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                Utils.appText(
                                  context: context,
                                  text: requestAccept.endUser?.fullName ?? "",
                                  arabicText:
                                      requestAccept.endUser?.fullName ?? "",
                                  // requestAccept.endUser?.arabicFullName ??"",
                                ),
                                style: textTheme.bodySmall!
                                    .copyWith(fontWeight: FontWeight.bold),
                              ),
                              Gap(h: 1.h),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  const Icon(Icons.star, color: primaryColor),
                                  Gap(w: 2.w),
                                  Text(
                                    double.parse(
                                      requestAccept.endUser?.rating ?? "1",
                                    ).toStringAsFixed(1),
                                    style: textTheme.bodySmall!
                                        .copyWith(fontWeight: FontWeight.bold),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ],
                      ),
                      Container(
                        height: 40,
                        color: black.withOpacity(0.1),
                        width: 1,
                      ),
                      Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          CircleButton(
                            icon: Icons.phone_in_talk_sharp,
                            backgroundColor: primaryColor,
                            size: 45,
                            onTap: () {
                              callPhoneNumber(
                                "${requestAccept.endUser?.mobileNumber}",
                              );
                            },
                          ),
                          Gap(w: 4.w),
                          Builder(
                            builder: (context) {
                              final unreadCount =
                                  context.watch<ChatsCubit>().unreadCount;
                              return Badge(
                                label: FittedBox(
                                  alignment: Alignment.center,
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    unreadCount > 99
                                        ? "99+"
                                        : unreadCount.toString(),
                                  ),
                                ),
                                isLabelVisible: unreadCount > 0,
                                child: CircleButton(
                                  icon: Icons.message_outlined,
                                  backgroundColor: secondaryColor,
                                  size: 45,
                                  onTap: () {
                                    context.push(path.chat);
                                  },
                                ),
                              );
                            },
                          ),
                          Gap(w: 4.w),
                          CircleButton(
                            icon: Icons.cancel_outlined,
                            backgroundColor: Colors.red,
                            size: 45,
                            onTap: () async {
                              if (!cancelButtonOn) {
                                final result = await showDialog(
                                  context: context,
                                  builder: (_) =>
                                      const CancelRideWarningModal(),
                                );
                                if (result != true) return;
                              }
                              showDialog(
                                context: context,
                                builder: (_) => const CancelRidePopUp(),
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                const Gap(),
                const CustomDivider(),
                const Gap(),
                GetDirectionsButton(
                  onPressed: () {
                    launchGoogleMapsApp(
                      widget.rideRequest.pickupLatitude,
                      widget.rideRequest.pickupLongitude,
                    );
                  },
                ),
                const Gap(h: 24),
                !startButtonOn
                    ? BlocListener<TripBloc, TripState>(
                        listener: (context, state) {
                          if (state is ArrivedAtPickup) {
                            setState(() {
                              startButtonOn = true;
                            });
                          } else if (state is ChangeTripStatusFailure) {
                            showCustomSnackbar(context, state.message);
                          }
                        },
                        child: Container(
                          margin: const EdgeInsets.symmetric(horizontal: 10) +
                              const EdgeInsets.only(bottom: 16),
                          // width: 85.5.w,
                          child: CustomSwipeButton(
                            atPickup: true,
                            onSwipe: () {
                              var id = context
                                  .read<TripBloc>()
                                  .requestAccept
                                  .trip
                                  ?.id;
                              log("trip id:$id");
                              context.read<TripBloc>().add(
                                    ChangeTripStatus(tripID: "$id", status: 2),
                                  );
                            },
                            text:
                                AppLocalizations.of(context)!.swipe_on_arrival,
                          ),
                        ),
                      )
                    : BlocConsumer<TripBloc, TripState>(
                        listener: (context, state) {
                          if (state is TripStarted) {
                            timer?.cancel();
                            cancelButtonOn = false;
                            CustomSocket.unsubscribeChannel(
                              channelName:
                                  'private-user-send-message-channel_${globalDriverProfile.user?.id}',
                            );
                            context.read<TripBloc>().requestAccept;
                            context.push(
                              path.tripStarted,
                              extra: {
                                'rideRequest': widget.rideRequest,
                                "requestAccept":
                                    context.read<TripBloc>().requestAccept,
                              },
                            );
                          }
                          if (state is ChangeTripStatusFailure) {
                            showCustomSnackbar(context, state.message);
                          }
                        },
                        builder: (context, state) {
                          return CustomButton(
                            buttonText:
                                AppLocalizations.of(context)!.start_trip,
                            isLoading: state is TripStartLoading ? true : false,
                            borderRadius: 12,
                            onPressed: () {
                              var id = context
                                  .read<TripBloc>()
                                  .requestAccept
                                  .trip
                                  ?.id;
                              context.read<TripBloc>().add(
                                    ChangeTripStatus(
                                      tripID: id.toString(),
                                      status: 3,
                                    ),
                                  );

                              // setState(() {

                              // });
                            },
                          );
                        },
                      ),
                Gap(w: 16 + MediaQuery.paddingOf(context).bottom),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
