import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/dialog_holder.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import 'rate_customer.dart';

class TripFinisedModal extends StatefulWidget {
  final String id;
  final String price;

  const TripFinisedModal({super.key, required this.id, required this.price});

  @override
  State<TripFinisedModal> createState() => _TripFinisedModalState();
}

class _TripFinisedModalState extends State<TripFinisedModal> {
  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;

    return SizedBox(
      height: MediaQuery.sizeOf(context).height * 0.45,
      child: SingleChildScrollView(
        child: Column(children: [
          const Gap(),
          Padding(
            padding: const EdgeInsets.all(10.0),
            child: Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
              SvgPicture.asset(checkedImage),
              Gap(w: 2.w),
              SizedBox(
                  width: MediaQuery.sizeOf(context).width * 0.7,
                  child: Text(AppLocalizations.of(context)!.congratulations_completed_trip,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.bold,
                          ))),
            ]),
          ),
          CustomDivider(
            indent: 10.w,
          ),
          const Gap(),
          Container(
            height: 116,
            width: 255,
            decoration: BoxDecoration(
              color: primaryColorDark.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Text(
                '${((double.tryParse(widget.price) ?? 0) / 100).toStringAsFixed(2)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}',
                style: Theme.of(context).textTheme.displayLarge!.copyWith(fontWeight: FontWeight.bold, fontSize: 30),
              ),
            ),
          ),
          const Gap(),
          const Gap(),
          CustomButton(
              height: 5.h,
              buttonText: AppLocalizations.of(context)!.done,
              borderRadius: 12,
              onPressed: () {
                showCustomDialog(
                    context,
                    RateCustomer(
                      id: widget.id,
                      price: widget.price,
                    ));
              }),
          SizedBox(height: 5.h)
        ]),
      ),
    );
  }
}
