import 'package:flutter/material.dart';
import '../../../../core/utils/colors.dart';

class Message extends StatelessWidget {
  final String message;
  final bool self;

  const Message({super.key, required this.message, required this.self});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: self ? Alignment.centerRight : Alignment.centerLeft,
      child: Column(
        crossAxisAlignment:
            self ? CrossAxisAlignment.end : CrossAxisAlignment.start,
        children: <Widget>[
          Container(
            margin: const EdgeInsets.all(10),
            padding: const EdgeInsets.all(10),
            decoration: BoxDecoration(
              color: self ? primaryColorDark : primaryColorLight,
              borderRadius: BorderRadius.only(
                  topLeft: const Radius.circular(10),
                  topRight: const Radius.circular(10),
                  bottomLeft: self ? const Radius.circular(10) : Radius.zero,
                  bottomRight: self ? Radius.zero : const Radius.circular(10)),
            ),
            child: Text(
              message,
              style: TextStyle(color: self ? Colors.white : primaryColorDark),
            ),
          ),
        ],
      ),
    );
  }
}
