import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/routes/paths.dart' as path;
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/features/authentication/model/general_info.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import 'ride_history_detail_card.dart';

class UnavailableView extends StatefulWidget {
  const UnavailableView({super.key});

  @override
  State<UnavailableView> createState() => _UnavailableViewState();
}

class _UnavailableViewState extends State<UnavailableView> {
  UserTripInfo? info;

  @override
  void initState() {
    super.initState();
    // BlocProvider.of<WalletBloc>(context).add(const WalletGetGallet());
    BlocProvider.of<AuthBloc>(context).add(const AuthGetInformation());
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(20.0),
        ),
        width: MediaQuery.sizeOf(context).width,
        child: Column(
          children: [
            const Gap(),
            const Gap(),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Container(
                  width: 23.h,
                  height: 30.w,
                  decoration: BoxDecoration(
                    color: primaryColor,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // ignore: deprecated_member_use
                          SvgPicture.asset(walletIcon, color: white),
                          Gap(w: 4.w),
                          Text(AppLocalizations.of(context)!.balance,
                              style: Theme.of(context).textTheme.displayLarge!.copyWith(color: white, fontSize: 22)),
                        ],
                      ),
                      const Divider(indent: 4, endIndent: 4, color: white),
                      Text(
                        "${double.parse(driver?.balance ?? '0').round() / 100} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                        selectionColor: white,
                        style: Theme.of(context)
                            .textTheme
                            .displayLarge!
                            .copyWith(fontSize: 28, fontWeight: FontWeight.w700, color: white),
                      ),
                    ],
                  ),
                ),
                InkWell(
                  onTap: () {
                    context.push(path.walletTopUp);
                  },
                  child: Container(
                    height: 60,
                    width: 60,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12.0),
                      border: Border.all(color: black.withOpacity(0.1)),
                    ),
                    child: const Center(
                      child: Icon(Icons.add, size: 30),
                    ),
                  ),
                )
              ],
            ),
            BlocListener<AuthBloc, AuthState>(
              listenWhen: (previous, current) => current is AuthGetInformationSuccess,
              listener: (context, state) {
                if (state is AuthGetInformationSuccess) {
                  setState(() {
                    info = state.generalInformation;
                  });
                }
              },
              child: BlocBuilder<AuthBloc, AuthState>(builder: (contex, state) {
                return Column(
                  children: [
                    const Gap(),
                    RideHistoryCard(
                      firstWidget: SvgPicture.asset(acceptedRequestIcon),
                      text: AppLocalizations.of(context)!.accetped_requests,
                      trailingWidget: state is AuthGetInformationLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation(primaryColor),
                              ))
                          : Text(info?.acceptedRequest ?? "0 %",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(fontWeight: FontWeight.bold, color: primaryColor)),
                    ),
                    RideHistoryCard(
                      firstWidget: SvgPicture.asset(pathLocationIcon),
                      text: AppLocalizations.of(context)!.number_of_trips,
                      trailingWidget: state is AuthGetInformationLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation(primaryColor),
                              ))
                          : Text(
                              info?.numberOfTrips ?? "0",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(fontWeight: FontWeight.bold, color: primaryColor),
                            ),
                    ),
                    RideHistoryCard(
                      firstWidget: SvgPicture.asset(rejectedRequestIcon),
                      text: AppLocalizations.of(context)!.rejected_requests,
                      trailingWidget: state is AuthGetInformationLoading
                          ? const SizedBox(
                              height: 20,
                              width: 20,
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation(primaryColor),
                              ))
                          : Text(info?.rejectedRequest ?? "0",
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(fontWeight: FontWeight.bold, color: primaryColor)),
                    ),
                  ],
                );
              }),
            ),
            const Gap(),
          ],
        ));
  }
}
