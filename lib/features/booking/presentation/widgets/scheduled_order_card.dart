import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';

class ScheduledOrderCard extends StatelessWidget {
  const ScheduledOrderCard({super.key});

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    var textTheme = Theme.of(context).textTheme;
    return Container(
        margin: const EdgeInsets.all(15),
        padding: const EdgeInsets.fromLTRB(15, 15, 15, 15),
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(width: 1.0, color: grey.withOpacity(0.5)),
            borderRadius: BorderRadius.circular(20)),
        child: Column(children: [
          Row(children: <Widget>[
            Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Row(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      const CircleAvatar(
                        minRadius: 25,
                        maxRadius: 25,
                        backgroundImage: NetworkImage(
                            "https://www.crushpixel.com/big-static7/preview4/car-driver-125899.jpg"),
                        backgroundColor: grey,
                      ),
                      Gap(w: 3.w),
                      Column(children: [
                        Text("Jhon doe",
                            style: textTheme.bodySmall!
                                .copyWith(fontWeight: FontWeight.bold)),
                        Text("24 July 2023",
                            style: textTheme.bodySmall!
                                .copyWith(fontSize: 12, color: grey)),
                      ])
                    ],
                  ),
                  Gap(w: 20.w),
                  Container(
                    height: 50,
                    width: 0.4, // Set the width of the vertical divider
                    color: grey, // Set the color of the vertical divider
                  ),
                  Gap(w: 8.w),
                  Text(
                      "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}15.00",
                      style: textTheme.bodyLarge)
                ],
              ),
              const Divider(),
              Container(
                  width: MediaQuery.sizeOf(context).width * 0.8,
                  height: 1,
                  color: grey.withOpacity(0.5)),
              const CustomDivider(),
              Row(
                children: [
                  Column(children: [
                    Row(
                      children: [
                        CircleAvatar(
                            minRadius: 10,
                            backgroundColor: const Color(0xFFFF6B00),
                            child: Text(AppLocalizations.of(context)!.a,
                                style: const TextStyle(color: white))),
                        Gap(w: 3.w),
                        SizedBox(
                          width: 60.w,
                          child: const Text(
                            "2-98/1, Near Panchayat, Marthahalli, Karnataka",
                            style: TextStyle(
                                fontSize: 10, fontWeight: FontWeight.bold),
                          ),
                        )
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: [
                        CircleAvatar(
                            minRadius: 10,
                            backgroundColor: primaryColor,
                            child: Text(AppLocalizations.of(context)!.b,
                                style: const TextStyle(color: white))),
                        Gap(w: 3.w),
                        SizedBox(
                          width: 60.w,
                          child: const Text(
                            "2-98/1, Near Panchayat, Marthahalli, Karnataka",
                            style: TextStyle(
                                fontSize: 10, fontWeight: FontWeight.bold),
                          ),
                        )
                      ],
                    ),
                  ]),
                  Container(
                    height: 90,
                    width: 0.4, // Set the width of the vertical divider
                    color: grey, // Set the color of the vertical divider
                  ),
                  Gap(w: 3.w),
                  const Column(children: [
                    Icon(Icons.schedule, size: 25),
                    Gap(),
                    Text("8:15 AM",
                        style: TextStyle(
                            fontSize: 10, fontWeight: FontWeight.bold))
                  ])
                ],
              )
            ]),
          ])
        ]));
  }
}
