import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class GetDirectionsButton extends StatelessWidget {
  const GetDirectionsButton({
    super.key,
    this.onPressed,
    this.isFromOverlay = false,
  });
  final void Function()? onPressed;
  final bool isFromOverlay;
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        height: isFromOverlay ? 5 : 5.h,
        width: isFromOverlay ? 50 : 50.w,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: primaryColor,
        ),
        child: Center(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.arrow_forward, color: white),
              Gap(w: isFromOverlay ? 2 : 2.w),
              Text(
                AppLocalizations.of(context)!.get_direction,
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(fontWeight: FontWeight.bold, color: white),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
