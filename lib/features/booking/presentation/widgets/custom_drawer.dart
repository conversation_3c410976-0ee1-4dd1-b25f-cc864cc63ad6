import 'dart:ui';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:circle_flags/circle_flags.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/account_type/bloc/chat/account__type_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/system_service/system_services_cubit.dart';
import 'package:yalla_gai_driver/features/connect/presentation/bloc/cubit/contactus_cubit.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/cubit/payment_cubit.dart';

import '../../../../core/routes/paths.dart' as paths;
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';

class CustomDrawer extends StatefulWidget {
  const CustomDrawer({super.key});

  @override
  State<CustomDrawer> createState() => _CustomDrawerState();
}

class _CustomDrawerState extends State<CustomDrawer> {
  SharedPreferences? preferences;

  @override
  void initState() {
    super.initState();
    BlocProvider.of<AccountTypeBloc>(context).add(const AccountTypeGet());
  }

  init() async {
    preferences = await SharedPreferences.getInstance();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    final localizations = AppLocalizations.of(context)!;

    return Container(
      width: MediaQuery.of(context).size.width - 40.w,
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(topRight: Radius.circular(4), bottomRight: Radius.circular(4)),
        boxShadow: [
          BoxShadow(
            color: black.withOpacity(0.3), // Shadow color
            spreadRadius: 3, // Spread radius
            blurRadius: 6, // Blur radius
            offset: const Offset(0, 3), // Offset from the container
          ),
        ],
      ),
      margin: const EdgeInsets.symmetric(vertical: 50),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: 2, sigmaY: 2),
        child: Drawer(
          backgroundColor: white,
          child: SingleChildScrollView(
            child: Column(
              children: [
                Gap(h: 3.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Gap(w: 6.w),
                    GestureDetector(
                        onTap: () {
                          context.pop();
                        },
                        child: const Icon(Icons.arrow_back_ios),),
                    /*Gap(w: 12.w),
                    Text(AppLocalizations.of(context)!.profile, style: Theme.of(context).textTheme.bodyLarge)*/
                  ],
                ),
                Gap(h: 3.h),
                Container(
                  width: 70, // adjust the width as per your requirement
                  height: 70, // adjust the height as per your requirement
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: black.withOpacity(0.2),
                      width: 2,
                    ),
                  ),
                  child: driver?.profileImage != null
                      ? CachedNetworkImage(
                          height: double.infinity,
                          imageUrl: "${AppEnvironment.publicBaseUrl}${driver?.profileImage}",
                          fit: BoxFit.cover,
                          imageBuilder: (context, imageProvider) => Container(
                            width: 80.0,
                            height: 80.0,
                            decoration: BoxDecoration(
                              shape: BoxShape.circle,
                              image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
                            ),
                          ),
                          placeholder: (context, url) => const CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation(primaryColor),
                          ),
                          errorWidget: (context, url, error) => const Icon(
                            Icons.person,
                            size: 50,
                          ),
                        )
                      : CircleAvatar(
                          radius: 58,
                          backgroundColor: Colors.green.withOpacity(.5),
                          child: const Icon(
                            Icons.person,
                            color: Colors.white,
                            size: 70,
                          ),
                        ),
                ),
                Gap(h: 2.h),
                CircleFlag(
                  '${driver?.country?.countryFlag}',
                  size: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Gap(w: 1.w),
                    Text(driver?.mobileNumber ?? '',
                        style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w500),),
                  ],
                ),
                Gap(h: 1.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    BlocConsumer<AccountTypeBloc, AccountTypeState>(
                      listener: (context, state) {
                        if (state is AccountTypeGetSuccess) {
                          final accountTypeId = globalDriverProfile.user?.accountTypeId;
                          final colorString = state.accountType
                              .where((element) => element.id.toString() == accountTypeId)
                              .firstOrNull
                              ?.color;
                          if (colorString != null) preferences?.setString("account_type_color", colorString);
                        }
                      },
                      listenWhen: (previous, current) => current is AccountTypeGetSuccess,
                      buildWhen: (previous, current) => current is AccountTypeGetSuccess,
                      builder: (context, state) {
                        String? colorString;
                        if (state is AccountTypeGetSuccess) {
                          final accountTypeId = globalDriverProfile.user?.accountTypeId;
                          colorString = state.accountType
                              .where((element) => element.id.toString() == accountTypeId)
                              .firstOrNull
                              ?.color;
                        }
                
                        colorString ??= preferences?.getString("account_type_color");
                        Color? color;
                        if (colorString != null) {
                          color = Color(int.tryParse(colorString) ?? int.parse("0xFFFFD700"));
                        }
                        return SvgPicture.asset(crownIcon, color: color);
                      },
                    ),
                    const Gap(
                      w: 4,
                    ),
                    Expanded(
                      child: Text(
                          Utils.appText(
                              context: context, text: driver?.fullName ?? '', arabicText: driver?.arabicFullName ?? '',), maxLines: 2,
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w600), textAlign: TextAlign.center,),
                    ),
                  ],
                ),
                Gap(h: 1.h),
                Text(preferences?.getString("phone_number") ?? "",
                    style: Theme.of(context)
                        .textTheme
                        .bodySmall!
                        .copyWith(fontWeight: FontWeight.w600, color: black.withOpacity(0.4)),),
                Gap(h: 1.h),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.grey),
                      ),
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Icon(Icons.star, size: 15, color: primaryColor),
                          const Gap(
                            w: 5,
                          ),
                          Text(
                            double.parse(driver?.rating ?? '0.0').toStringAsFixed(1),
                            style: const TextStyle(fontSize: 12),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Gap(h: 3.h),
                Center(
                  child: FractionallySizedBox(
                    widthFactor: 0.8,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.car_type,
                              style: Theme.of(context)
                                  .textTheme
                                  .displayLarge!
                                  .copyWith(fontSize: 16, fontWeight: FontWeight.w500),
                              textAlign: TextAlign.start,
                            ),
                            Gap(
                              w: 3.w,
                            ),
                            Text(
                              driver?.driver?.carModel?.model ?? '',
                              overflow: TextOverflow.clip,
                              style: Theme.of(context)
                                  .textTheme
                                  .displayLarge!
                                  .copyWith(fontSize: 14, fontWeight: FontWeight.w400, color: primaryColor),
                              textAlign: TextAlign.start,
                            ),
                          ],
                        ),
                        Gap(
                          h: 2.h,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              AppLocalizations.of(context)!.plate_no,
                              style: Theme.of(context)
                                  .textTheme
                                  .displayLarge!
                                  .copyWith(fontSize: 16, fontWeight: FontWeight.w500),
                              textAlign: TextAlign.start,
                            ),
                            Gap(
                              w: 3.w,
                            ),
                            Text(
                              driver?.driver?.plateNumber ?? '',
                              overflow: TextOverflow.clip,
                              style: Theme.of(context)
                                  .textTheme
                                  .displayLarge!
                                  .copyWith(fontSize: 14, fontWeight: FontWeight.w400),
                              textAlign: TextAlign.start,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                Gap(
                  h: 3.h,
                ),
                GestureDetector(
                  onTap: () {
                    context.read<PaymentCubit>().selectPaymentmethod(false);
                    context.push(paths.wallet);
                  },
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SvgPicture.asset(myWallet),
                      Text(AppLocalizations.of(context)!.my_wallet),
                    ],
                  ),
                ),
                Gap(h: 3.h),
                const FractionallySizedBox(widthFactor: 0.7, child: Divider()),
                const Gap(h: 10),
                Builder(
                  builder: (context) {
                    final offersEnabled = context.select<SystemServicesCubit, bool>((value) {
                      final serviceData = value.services.where((element) => element.type == "offers").firstOrNull;
                      return serviceData == null ? true : serviceData.status == "active";
                    });

                    final paymentsEnabled = context.select<SystemServicesCubit, bool>((value) {
                      final serviceData =
                          value.services.where((element) => element.type == "payments_section").firstOrNull;
                      return serviceData == null ? true : serviceData.status == "active";
                    });

                    final contactUsEnabled = context.select<SystemServicesCubit, bool>((value) {
                      final serviceData =
                          value.services.where((element) => element.type == "technical_support_contacts").firstOrNull;
                      return serviceData == null ? true : serviceData.status == "active";
                    });

                    final technicalSupportEnabled = context.select<SystemServicesCubit, bool>((value) {
                      final serviceData =
                          value.services.where((element) => element.type == "technical_support_messages").firstOrNull;
                      return serviceData == null ? true : serviceData.status == "active";
                    });

                    return Column(
                      children: [
                        _DrawerMenuItem(
                          label: localizations.notifications,
                          icon: notificationIcon,
                          onPressed: () => context.push(paths.notification),
                        ),
                        _DrawerMenuItem(
                          label: localizations.account_type,
                          icon: accountTypeIcon,
                          onPressed: () => context.push(paths.accountType),
                        ),
                        _DrawerMenuItem(
                          label: localizations.requests,
                          icon: requestsIcon,
                          onPressed: () => context.push(paths.requests),
                        ),
                        if (offersEnabled)
                          _DrawerMenuItem(
                            label: localizations.offers,
                            icon: offersIcon,
                            onPressed: () => context.push(paths.offers),
                          ),
                        if (paymentsEnabled)
                          _DrawerMenuItem(
                            label: localizations.payment,
                            icon: paymentIcon,
                            onPressed: () {
                              context.read<PaymentCubit>().selectPaymentmethod(true);
                              context.push(paths.payment);
                            },
                          ),
                        _DrawerMenuItem(
                          label: localizations.settings,
                          icon: settingIcon,
                          onPressed: () => context.push(paths.setting),
                        ),
                        if (contactUsEnabled)
                          _DrawerMenuItem(
                            label: localizations.connect_us,
                            icon: shareIconOutlined,
                            onPressed: () {
                              context.read<ContactusCubit>().contactUs();
                              context.push(paths.connectUs);
                            },
                          ),
                        _DrawerMenuItem(
                          label: localizations.suggestions_complaints,
                          icon: suggestionsIcon,
                          onPressed: () => context.push(paths.suggestionsAndComplaints),
                        ),
                        _DrawerMenuItem(
                          label: localizations.tips,
                          icon: tipsIcon,
                          onPressed: () => context.push(paths.tips),
                        ),
                        _DrawerMenuItem(
                          label: localizations.supervisor,
                          icon: supervisorIcon,
                          onPressed: () => context.push(paths.supervisor),
                        ),
                        if (technicalSupportEnabled)
                          _DrawerMenuItem(
                            label: localizations.technical_support,
                            icon: technicalSupport,
                            onPressed: () => context.push(paths.techSupport),
                          ),
                      ],
                    );
                  },
                ),
                const Gap(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class _DrawerMenuItem extends StatelessWidget {
  const _DrawerMenuItem({
    super.key,
    required this.label,
    required this.icon,
    required this.onPressed,
  });

  final String label;
  final String icon;
  final VoidCallback onPressed;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: SvgPicture.asset(
        icon,
        width: 20,
        height: 20,
      ),
      title: Text(label, style: Theme.of(context).textTheme.bodySmall),
      trailing: const Icon(Icons.arrow_forward_ios_sharp, size: 15),
      onTap: onPressed,
    );
  }
}
