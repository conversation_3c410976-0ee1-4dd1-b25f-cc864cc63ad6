import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';

class TrafficPopup extends StatelessWidget {
  const TrafficPopup({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: 55.5.h,
          padding: const EdgeInsets.all(20),
          width: MediaQuery.sizeOf(context).width * 0.8,
          decoration: BoxDecoration(
              color: white, borderRadius: BorderRadius.circular(12)),
          child: SingleChildScrollView(
            child: Column(
              children: [
                SvgPicture.asset(trafficHint),
                 Text(AppLocalizations.of(context)!.traffic_hints),
                const Gap(),
                const CustomDivider(),
                const Gap(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircleAvatar(
                        backgroundColor: Color.fromARGB(255, 200, 16, 3),
                        minRadius: 10),
                    Gap(w: 3.w),
                    Text(AppLocalizations.of(context)!.heavy_traffic,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(
                                color: const Color.fromARGB(255, 200, 16, 3)))
                  ],
                ),
                const Gap(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircleAvatar(
                        backgroundColor: Colors.orange, minRadius: 10),
                    Gap(w: 3.w),
                    Text(AppLocalizations.of(context)!.light_traffic,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: Colors.orange))
                  ],
                ),
                const Gap(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircleAvatar(
                        backgroundColor: Colors.green, minRadius: 10),
                    Gap(w: 3.w),
                    Text(AppLocalizations.of(context)!.no_traffic,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(color: Colors.green))
                  ],
                ),
                 Gap(h : 4.h),
                CustomButton(
                    width: 45.w,
                    buttonText: AppLocalizations.of(context)!.okay,
                    borderRadius: 12,
                    onPressed: () {
                      context.pop();
                    })
              ],
            ),
          ),
        ),
      ],
    );
  }
}
