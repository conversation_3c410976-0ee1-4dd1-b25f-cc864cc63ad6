import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/constants/app_utils.dart';
import 'package:yalla_gai_driver/core/routes/paths.dart' as path;
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/ride_request/ride_request_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/booking/domain/entity/ride_request.dart';
import 'package:yalla_gai_driver/features/booking/repository/trip_repository.dart';

import '../../../../core/constants/constants.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import 'pick_and_drop_points.dart';

class RequestBottomSheet extends StatefulWidget {
  final RideRequest rideRequest;
  final Function() stopRingtone;
  final int countdown;

  const RequestBottomSheet({super.key, required this.stopRingtone, required this.rideRequest, required this.countdown});

  @override
  State<RequestBottomSheet> createState() => _RequestBottomSheetState();
}

class _RequestBottomSheetState extends State<RequestBottomSheet> {
  late int countdown;
  Color textColor = primaryColor;
  bool accepted = false;
  bool accepting = false;

  @override
  void dispose() {
    timer?.cancel();
    widget.stopRingtone();
    if (!accepted) {
      CustomSocket.reSubscribeChannel(channelName: 'enduser-send-new-request-channel_${globalDriverProfile.user?.id}');
    }
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    countdown = widget.countdown;
    startCountdown();
  }

  Timer? timer;

  void startCountdown() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown > 0) {
        if (mounted) {
          setState(() {
            countdown--;
            if (countdown <= 5) {
              textColor = Colors.red;
            }
          });
        }
      } else {
        timer.cancel();
        widget.stopRingtone();
        final requestData = context.read<RideRequestCubit>().requestData;
        context.read<RideRequestCubit>().accepted(emitState: false);

        if (!accepted) {
          context.go(path.home);
          CustomSocket.unsubscribeChannel(channelName: 'cancel-ride-channel_${requestData?.userId}');
        }
      }
    });
  }

  String formatSeconds(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;

    String minutesStr = minutes.toString().padLeft(2, '0');
    String secondsStr = remainingSeconds.toString().padLeft(2, '0');

    return '$minutesStr:$secondsStr';
  }

  bool isLoading = false;

  @override
  Widget build(BuildContext context) {
    final reqData = context.read<RideRequestCubit>().requestData;

    return BlocListener<TripBloc, TripState>(
      listener: (context, state) {
        if (state is TripAcceptNewRequestSuccess) {
          widget.stopRingtone();
          rideRequestAccepted = true;
          CustomSocket.unsubscribeChannel(channelName: "accept-request-done-channel_${reqData?.id}");
          context.read<RideRequestCubit>().unsubscribe();

          CustomSocket.reSubscribeChannel(channelName: 'user-send-message-channel_${globalDriverProfile.user?.id}');
          Future.delayed(Duration.zero, () async {
            AppUtils.waitTime = await TripRepository().getWatingTime('waiting');
            context.push(path.acceptRide, extra: {
              "rideRequest": widget.rideRequest,
              "requestAccept": state.requestAccept,
            },);
          });
          setState(() {
            accepted = true;
          });
        }
      },
      child: BlocListener<RideRequestCubit, RideRequestState>(
        listener: (context, state) {
          print("RideRequestCubit => Listener => $state");
          if (state is RideRequestCancelled) {
            showCustomSnackbar(context, AppLocalizations.of(context)!.trip_request_cancelled);
            context.go(path.home);
          } else if (state is RideTakenByAnotherDriver) {
            showCustomSnackbar(context, AppLocalizations.of(context)!.ride_requested_by_another_driver);
            context.go(path.home);
          } else if (state is RideTaken) {
            if (mounted) {
              timer?.cancel();
              widget.stopRingtone();
              if (accepting) {
                if (state.requestID.toString() != reqData?.id.toString()) {
                  context.go(path.home);
                }
              }
            }
          }
        },
        child: Container(
          // height: MediaQuery.of(context).size.height * 0.36,
          width: MediaQuery.of(context).size.width,
          decoration: BoxDecoration(color: white, borderRadius: BorderRadius.circular(20)),
          child: Column(
            children: [
              Gap(w: 1.h),
              Row(
                children: [
                  Gap(w: 6.w),
                  Icon(Icons.timer, color: textColor),
                  Gap(w: 2.w),
                  Text(
                    formatSeconds(countdown),
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: textColor,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                  ),
                  Gap(w: 10.w),
                  Text(
                    AppLocalizations.of(context)!.new_request,
                    style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                ],
              ),
              const Gap(h: 2),
              const CustomDivider(),
              const Gap(h: 2),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      const Icon(Icons.access_time_filled, size: 30),
                      Gap(w: 2.w),
                      Text(
                        AppLocalizations.of(context)!.now,
                        style:
                            Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w600, fontSize: 15),
                      ),
                    ],
                  ),
                  Container(
                    height: 10,
                    width: 1,
                    color: Colors.black.withOpacity(0.1),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      const Icon(Icons.money, size: 30),
                      Gap(w: 2.w),
                      Text(
                        AppLocalizations.of(context)!.cash,
                        style:
                            Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w600, fontSize: 15),
                      ),
                    ],
                  ),
                ],
              ),
              const Gap(h: 2),
              const CustomDivider(),
              const Gap(h: 2),
              pickAndDropPoints(
                context,
                widget.rideRequest.userPickUpLocation.locationText,
                widget.rideRequest.userDropLocation?.locationText,
                13.5.w,
              ),
              Gap(h: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  CustomButton(
                    width: 34.w,
                    height: 5.h,
                    buttonText: AppLocalizations.of(context)!.reject,
                    borderRadius: 14,
                    borderOnly: true,
                    onPressed: () {
                      context.read<RideRequestCubit>().accepted(emitState: false);
                      timer?.cancel();
                      widget.stopRingtone();
                      context.go(path.home);
                    },
                  ),
                  CustomButton(
                    width: 34.w,
                    height: 5.h,
                    buttonText: AppLocalizations.of(context)!.accept,
                    isLoading: context.watch<TripBloc>().state is TripAcceptNewRequestLoading,
                    borderRadius: 14,
                    onPressed: () {
                      BlocProvider.of<TripBloc>(context).add(TripAcceptNewRequest(tripId: widget.rideRequest.id));
                      widget.stopRingtone();
                      timer?.cancel();
                    },
                  ),
                ],
              ),
              Gap(h: 16 + MediaQuery.paddingOf(context).bottom),
            ],
          ),
        ),
      ),
    );
  }
}
