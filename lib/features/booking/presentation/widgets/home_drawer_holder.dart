import 'package:flutter/material.dart';
import '../../../../core/utils/colors.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';

class HomeDrawerHolder extends StatefulWidget {
  const HomeDrawerHolder({super.key, this.margin});
  final double? margin;
  @override
  State<HomeDrawerHolder> createState() => _HomeDrawerHolderState();
}

class _HomeDrawerHolderState extends State<HomeDrawerHolder> {
  SharedPreferences? preferences;
  @override
  void initState() {
    init();
    super.initState();
  }

  init() async {
    preferences = await SharedPreferences.getInstance();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: preferences?.getString("languageCode") != "ar"
          ? Alignment.centerLeft
          : Alignment.centerRight,
      child: Icon<PERSON><PERSON>on(
        icon: const Icon(Icons.menu, weight: 5, size: 32, color: black),
        onPressed: () {
          context.read<AuthBloc>().add(const AuthGetProfile());
          Scaffold.of(context).openDrawer();
        },
      ),
    );
  }
}
