import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';


class PoliceStationDriverInfo extends StatefulWidget {
  const PoliceStationDriverInfo(
      {super.key, required this.name, required this.imageUrl});

  final String name;
  final String imageUrl;

  @override
  State<PoliceStationDriverInfo> createState() =>
      _PoliceStationDriverInfoState();
}

class _PoliceStationDriverInfoState extends State<PoliceStationDriverInfo> {
  bool liked = false;
  bool blocked = false;
  @override
  Widget build(BuildContext context) {
    return Container(
        padding: const EdgeInsets.all(15),
        margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        decoration: BoxDecoration(
          color: black.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
          CircleAvatar(
            minRadius: 25,
            maxRadius: 25,
            backgroundImage: NetworkImage(widget.imageUrl),
          ),
          Text(widget.name,
              overflow: TextOverflow.ellipsis,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(fontWeight: FontWeight.bold)),
          Row(
            children: [
              InkWell(
                onTap: () {
                  setState(() {
                    liked = !liked;
                    if (liked && blocked) {
                      blocked = false;
                    }
                  });
                },
                child: CircleAvatar(
                  minRadius: 25,
                  backgroundColor: liked ? const Color(0xFF00C2FF) : white,
                  child: Icon(
                    Icons.thumb_up,
                    color: liked ? white : black,
                  ),
                ),
              ),
              Gap(w: 2.w),
              InkWell(
                onTap: () {
                  setState(() {
                    blocked = !blocked;
                    if (liked && blocked) {
                      liked = false;
                    }
                  });
                },
                child: CircleAvatar(
                  minRadius: 25,
                  backgroundColor: blocked ? const Color(0xFFFF5757) : white,
                  child: Icon(
                    Icons.block,
                    color: blocked ? white : black,
                  ),
                ),
              ),
            ],
          ),
        ]));
  }
}
