import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/counter-cubit/counter_cubit.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import 'swipe_button.dart';
import 'wave_painter.dart';

class CounterWidget extends StatefulWidget {
  const CounterWidget({Key? key, required this.fees}) : super(key: key);

  final double fees;

  @override
  State<CounterWidget> createState() => _CounterWidgetState();
}

class _CounterWidgetState extends State<CounterWidget> {
  @override
  Widget build(BuildContext context) {
    final watchCounter = context.watch<CounterCubit>();

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          width: MediaQuery.of(context).size.width * 0.88,
          decoration: BoxDecoration(
            color: white,
            borderRadius: BorderRadius.circular(12),
          ),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: watchCounter.conunterStage != 'LastStage',
                      child: Container(
                        padding: const EdgeInsets.only(top: 20),
                        child: InkWell(
                          onTap: () => context.pop(),
                          child: Icon(
                            Icons.cancel,
                            size: 30,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                watchCounter.conunterStage == 'Start'
                    ? const OnStart()
                    : watchCounter.conunterStage == 'Agree'
                        ? AggrementStage(fees: widget.fees)
                        : const LastStage()
              ],
            ),
          ),
        ),
      ],
    );
  }

  String roundDown(double price) {
    String roundedPrice = price.toStringAsFixed(2);
    return roundedPrice;
  }
}

class OnStart extends StatelessWidget {
  const OnStart({super.key});

  @override
  Widget build(BuildContext context) {
    var textTheme = Theme.of(context).textTheme;
    return Column(
      children: [
        Transform.scale(scale: 0.65, child: Image.asset(counterIcon)),
        Text(AppLocalizations.of(context)!.counter, style: textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.bold)),
        const Gap(),
        const Divider(),
        const Gap(),
        InkWell(
          onTap: () {
            context.read<CounterCubit>().changeStages(stage: 'Agree');
          },
          child: CircleAvatar(
            minRadius: 65,
            backgroundColor: primaryColor,
            child: Text(AppLocalizations.of(context)!.start, style: textTheme.bodyLarge!.copyWith(color: white)),
          ),
        ),
        const Gap(),
      ],
    );
  }
}

class AggrementStage extends StatefulWidget {
  const AggrementStage({
    super.key,
    required this.fees,
  });

  final double fees;

  @override
  State<AggrementStage> createState() => _AggrementStageState();
}

class _AggrementStageState extends State<AggrementStage> {
  bool agreeTerm = false;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final oldCheckboxTheme = theme.checkboxTheme;
    final newCheckBoxTheme = oldCheckboxTheme.copyWith(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(100)),
    );
    return Column(
      children: [
        Transform.scale(scale: 0.65, child: Image.asset(agreement)),
        const Gap(),
        const Divider(),
        Gap(h: 6.h),
        Container(
          width: 80.w,
          alignment: Alignment.center,
          child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              children: [
                TextSpan(
                  text: AppLocalizations.of(context)!.note,
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.green,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                TextSpan(
                    text: Utils.appText(
                        context: context,
                        text:
                            "The timer is actually paid service and you will get charged ${(widget.fees / 100).toStringAsFixed(2)} ${globalDriverProfile.user?.country?.englishCurrency}",
                        arabicText:
                            "${globalDriverProfile.user?.country?.arabicCurrency} 0.2 الموقت هو في الواقع خدمة مدفوعة وسيتم محاسبتك"),
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.black,
                    )),
              ],
            ),
          ),
        ),
        Gap(h: 4.h),
        SizedBox(
            width: 80.w,
            child: Theme(
              data: theme.copyWith(checkboxTheme: newCheckBoxTheme),
              child: CheckboxListTile(
                title: Text(
                  AppLocalizations.of(context)!.agree_to_all_above_conditions,
                  style: const TextStyle(color: Colors.black, fontWeight: FontWeight.normal, fontSize: 16),
                ),
                value: agreeTerm,
                onChanged: (bool? value) {
                  setState(() {
                    agreeTerm = value!;
                  });
                },
                shape: const CircleBorder(),
                activeColor: Colors.black,
                checkColor: Colors.white,
                controlAffinity: ListTileControlAffinity.leading,
              ),
            )),
        const Gap(),
        BlocConsumer<CounterCubit, CounterState>(listener: (context, state) {
          if (state is CounterStartFaulire) {
            Navigator.pop(context);
          }
          if (state is CounterStartSuccess) {}
        }, builder: (context, state) {
          return CustomButton(
              isLoading: state is CounterStartLoading ? true : false,
              width: 45.w,
              buttonText: AppLocalizations.of(context)!.okay,
              borderRadius: 12,
              onPressed: agreeTerm
                  ? () {
                      context.read<CounterCubit>().startCounter();
                    }
                  : null);
        }),
        const Gap(),
        const Gap(),
      ],
    );
  }
}

class LastStage extends StatefulWidget {
  const LastStage({super.key});

  @override
  State<LastStage> createState() => _LastStageState();
}

class _LastStageState extends State<LastStage> {
  Timer? timer;
  int seconds = 0;

  @override
  void initState() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        seconds++;
      });
    });
    super.initState();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    final watchCounter = context.watch<CounterCubit>();
    final conunterStage = watchCounter.conunterStage;
    var textTheme = Theme.of(context).textTheme;
    return Column(
      children: [
        Transform.scale(scale: 0.65, child: Image.asset(counterIcon)),
        Text(AppLocalizations.of(context)!.counter, style: textTheme.bodyLarge!.copyWith(fontWeight: FontWeight.bold)),
        const Gap(),
        const Divider(),
        const Gap(),
        Container(
          padding: const EdgeInsets.all(10),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                spreadRadius: 5,
                blurRadius: 7,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Text(
                conunterStage == 'end'
                    ? '${AppLocalizations.of(context)!.start}  ${AppLocalizations.of(context)!.time} ${watchCounter.endCounter?.time ?? ''}'
                    : '${AppLocalizations.of(context)!.start}  ${AppLocalizations.of(context)!.time}  ${addLeadingZero(seconds ~/ 3600)}:${addLeadingZero((seconds % 3600) ~/ 60)}:${addLeadingZero(seconds % 60)}',
                style: textTheme.bodyMedium!.copyWith(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                ),
              ),
              // Text(
              //   '${AppLocalizations.of(context)!.price} : ${'d33'} ${Utils.appText(context: context, text: globalDriverProfile.user?.country?.englishCurrency ?? '', arabicText: globalDriverProfile.user?.country?.arabicCurrency ?? '')}',
              //   style: textTheme.bodySmall!
              //       .copyWith(fontSize: 13, fontWeight: FontWeight.bold),
              // )
            ],
          ),
        ),
        const Gap(),
        conunterStage == 'end'
            ? CircleAvatar(
                minRadius: 65,
                backgroundColor: primaryColor,
                child: Text(
                    "${((watchCounter.endCounter?.price ?? 0) / 100).toStringAsFixed(2)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                    style: textTheme.bodyLarge!.copyWith(color: white)))
            : Ripples(
                color: primaryColor.withOpacity(0.3),
                isCircle: true,
                child: const CircleAvatar(
                  minRadius: 65,
                  maxRadius: 65,
                  backgroundColor: primaryColor,
                  // child: Text(
                  //     "+${''} ${Utils.appText(context: context, text: globalDriverProfile.user?.country?.englishCurrency ?? '', arabicText: globalDriverProfile.user?.country?.arabicCurrency ?? '')}",
                  //     style: textTheme.displayMedium!.copyWith(color: white)),
                )),
        const Gap(),
        conunterStage == 'end'
            ? CustomButton(
                borderRadius: 12,
                // height: 5.h,
                buttonText:
                    "${AppLocalizations.of(context)!.the_price_fare_was} ${((watchCounter.endCounter?.price ?? 0) / 100).toStringAsFixed(2)}  ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                onPressed: () {})
            : BlocConsumer<CounterCubit, CounterState>(listener: (context, state) {
                if (state is CounterEndFaulire) {
                  Navigator.pop(context);
                }
              }, builder: (context, state) {
                if (state is CounterEndLoading) {
                  return const Center(
                    child: CircleAvatar(
                      radius: 32,
                      backgroundColor: Colors.white,
                      child: CircleAvatar(
                        radius: 30,
                        backgroundColor: Colors.green,
                        child: SizedBox(
                          height: 35,
                          width: 35,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                            valueColor: AlwaysStoppedAnimation(primaryColor),
                          ),
                        ),
                      ),
                    ),
                  );
                }
                return CustomSwipeButton(
                    text: AppLocalizations.of(context)!.end_counte,
                    onSwipe: () {
                      context.read<CounterCubit>().conterEnd();
                    });
              }),
        Gap(h: 6.h),
        Visibility(
          visible: conunterStage == 'end',
          child: CustomButton(
              // isLoading: state is CounterStartLoading ? true : false,
              width: 45.w,
              buttonText: AppLocalizations.of(context)!.okay,
              borderRadius: 12,
              onPressed: () {
                Navigator.pop(context);
                context.read<CounterCubit>().conunterStage = 'Start';
              }),
        ),
        const Gap()
      ],
    );
  }

  String addLeadingZero(int number) {
    return number.toString().padLeft(2, '0');
  }
}
