import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';

class ServiceCard extends StatefulWidget {
  final String text;
  final bool boolValue;
  final Function(bool) onChanged;
  final Widget leading;

  const ServiceCard({
    super.key,
    required this.text,
    required this.leading,
    this.boolValue = false,
    required this.onChanged,
  });

  @override
  ServiceCardState createState() => ServiceCardState();
}

class ServiceCardState extends State<ServiceCard> {
  bool boolValue = false;

  @override
  void initState() {
    super.initState();
    boolValue = widget.boolValue;
  }

  void _toggle() {
    setState(() {
      boolValue = !boolValue;
      widget.onChanged(boolValue);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      margin: const EdgeInsets.all(15),
      width: MediaQuery.of(context).size.width * 0.9,
      decoration: BoxDecoration(
        border: Border.all(color: black.withOpacity(0.2)),
        borderRadius: BorderRadius.circular(12),
        color: white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          widget.leading,
          Gap(w: 16),
          Text(
            widget.text,
            style: Theme.of(context).textTheme.bodyLarge,
          ),
          Gap(w: 2.w),
          Transform.scale(
            scale: 1.3,
            child: Switch(
              value: boolValue,
              trackOutlineColor: WidgetStatePropertyAll(
                boolValue ? primaryColor : Colors.black.withOpacity(0.1),
              ),
              thumbColor: const WidgetStatePropertyAll(Colors.white),
              activeColor: primaryColor,
              inactiveThumbColor: Colors.black.withOpacity(0.4),
              inactiveTrackColor: const Color.fromARGB(255, 225, 225, 225),
              onChanged: (_) => _toggle(),
            ),
          ),
        ],
      ),
    );
  }
}
