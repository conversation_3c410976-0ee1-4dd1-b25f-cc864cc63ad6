import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import 'coupon_popup.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class FloatingContainer extends StatelessWidget {
  const FloatingContainer(
      {super.key,
      required this.title,
      this.child,
      this.width,
      this.height,
      this.showCoupon = false});

  final String title;
  final double? width;
  final Widget? child;
  final double? height;
  final bool showCoupon;

  @override
  Widget build(BuildContext context) {
    return Container(
        height: height ?? 145,
        width: width ?? MediaQuery.of(context).size.width - 10.w,
        decoration: BoxDecoration(
          borderRadius:
              const BorderRadius.horizontal(left: Radius.circular(12)),
          color: white,
          boxShadow: [
            BoxShadow(
              color: black.withOpacity(0.3), // Shadow color
              spreadRadius: 3, // Spread radius
              blurRadius: 6, // Blur radius
              offset: const Offset(0, 3), // Offset from the container
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.only(left: 25, top: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(right: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context)
                          .textTheme
                          .bodySmall!
                          .copyWith(color: primaryColor),
                    ),
                    if (showCoupon)
                      GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            barrierColor: Colors.transparent,
                            builder: (BuildContext context) {
                              return Container(
                                color: Colors.transparent,
                                child: Stack(
                                  children: [
                                    BackdropFilter(
                                      filter: ImageFilter.blur(
                                          sigmaX: 5.0, sigmaY: 5.0),
                                      child: Container(
                                        color: white.withOpacity(0.2),
                                        child: const CouponPopUp(),
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          );
                        },
                        child: Container(
                          padding: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(20),
                            border: Border.all(color: grey),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SvgPicture.asset(couponIcon,
                                  width: 20, height: 20),
                              Text(AppLocalizations.of(context)!.add_coupon,
                                  style: Theme.of(context).textTheme.bodySmall),
                            ],
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              const Divider(),
              Gap(h: 1.h),
              child ?? const Gap(h: 0)
            ],
          ),
        ));
  }
}
