import 'package:flutter_svg/svg.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/shared_widgets/gap.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/features/booking/presentation/widgets/custom_divider.dart';

class AddPoliceLocationBottomSheet extends StatelessWidget {
  const AddPoliceLocationBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        height: MediaQuery.sizeOf(context).height * 0.25,
        decoration: BoxDecoration(
            color: white, borderRadius: BorderRadius.circular(20)),
        width: MediaQuery.sizeOf(context).width,
        child: SingleChildScrollView(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Gap(h: 3.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                SvgPicture.asset(policeCarLight),
                SizedBox(
                    width: MediaQuery.sizeOf(context).width * 0.7,
                    child: Text(
                        AppLocalizations.of(context)!
                            .are_you_sure_you_want_to_add_the_police_location,
                        style: Theme.of(context)
                            .textTheme
                            .bodyMedium!
                            .copyWith(fontWeight: FontWeight.bold))),
              ],
            ),
            Gap(h: 2.h),
            const CustomDivider(),
            Gap(h: 2.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                CustomButton(
                    buttonText: AppLocalizations.of(context)!.mark_on_map,
                    borderRadius: 12,
                    onPressed: () {
                      context.pop(true);
                    },
                    height: 6.h,
                    width: 40.w),
                CustomButton(
                    buttonText:
                        AppLocalizations.of(context)!.add_current_location,
                    borderRadius: 12,
                    onPressed: () {
                      context.pop(false);
                    },
                    height: 6.h,
                    width: 50.w),
              ],
            ),
            Gap(h: 3.h),
          ],
        )));
  }
}
