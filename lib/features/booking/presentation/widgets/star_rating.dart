import 'package:flutter/material.dart';

class StarRating extends StatefulWidget {
  final int rating;
  final Function(int index) onUpdateRating;

  const StarRating({super.key, required this.rating, required this.onUpdateRating});

  @override
  StarRatingState createState() => StarRatingState();
}

class StarRatingState extends State<StarRating> {
  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: List.generate(5, (index) {
        return GestureDetector(
          onTap: () => widget.onUpdateRating(index),
          child: Icon(
            size: 35,
            index < widget.rating ? Icons.star : Icons.star_border,
            color: Colors.yellow.shade700,
          ),
        );
      }),
    );
  }
}
