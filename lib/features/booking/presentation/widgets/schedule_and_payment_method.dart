import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/utils/colors.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/images.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class ScheduleAndPayment extends StatefulWidget {
  const ScheduleAndPayment({super.key});
  @override
  ScheduleAndPaymentState createState() => ScheduleAndPaymentState();
}

class ScheduleAndPaymentState extends State<ScheduleAndPayment> {
  String selectedTime = '';
  String tempTime = '';
  String selectedCash = '';
  bool walletEnabled = false;
  DateTime minDate = DateTime.now();
  String groupValue = "Cash";
  String tempGroupValue = "Cash";

  void openTimePicker(
    context,
  ) {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SizedBox(
          child: Column(
            children: [
              Padding(
                padding:
                    const EdgeInsets.symmetric(horizontal: 30, vertical: 15),
                child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      InkWell(
                        onTap: () {
                          setState(() {
                            selectedTime = "";
                          });
                          context.pop();
                        },
                        child: Text(AppLocalizations.of(context)!.order_now,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: primaryColor,
                                      fontFamily: "Plus Jakarta Sans",
                                    )),
                      ),
                      InkWell(
                        onTap: () => context.pop(),
                        child: Text(AppLocalizations.of(context)!.cancel,
                            style:
                                Theme.of(context).textTheme.bodySmall!.copyWith(
                                      fontWeight: FontWeight.bold,
                                      fontFamily: "Plus Jakarta Sans",
                                    )),
                      ),
                    ]),
              ),
              Divider(indent: 30, endIndent: 30, color: black.withOpacity(0.2)),
              SizedBox(
                height: 35.h,
                child: CupertinoDatePicker(
                  minimumDate: minDate,
                  mode: CupertinoDatePickerMode.dateAndTime,
                  onDateTimeChanged: (DateTime date) {
                    setState(() {
                      tempTime = DateFormat('MMM d, h:mm a').format(date);
                    });
                  },
                ),
              ),
              Gap(h: 3.h),
              CustomButton(
                buttonText: AppLocalizations.of(context)!.set_time,
                borderRadius: 12,
                onPressed: () {
                  if (tempTime == '') {
                    setState(() {
                      selectedTime = '';
                    });
                  } else {
                    setState(() {
                      selectedTime = tempTime;
                    });
                  }

                  context.pop();
                },
              )
            ],
          ),
        );
      },
    );
  }

  void openCashPicker() {
    showModalBottomSheet(
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(
              builder: (BuildContext context, StateSetter stateChange) {
            return Container(
                padding: const EdgeInsets.all(15),
                child: SingleChildScrollView(
                  child: Column(children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Gap(w: 3.w),
                        Text(AppLocalizations.of(context)!.payment_mode,
                            style: Theme.of(context)
                                .textTheme
                                .bodyMedium!
                                .copyWith(
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "Plus Jakarta Sans",
                                )),
                        IconButton(
                            icon: const Icon(Icons.cancel),
                            color: grey,
                            onPressed: () => context.pop())
                      ],
                    ),
                    Gap(h: 2.h),
                    const Divider(
                      indent: 20,
                      endIndent: 20,
                    ),
                    Gap(h: 2.h),
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          SvgPicture.asset(greenWalletIcon),
                          Column(
                            children: [
                              Text(AppLocalizations.of(context)!.my_wallet,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                        fontWeight: FontWeight.bold,
                                        fontFamily: "Plus Jakarta Sans",
                                      )),
                              // 
                            ],
                          ),
                          Switch(
                              trackOutlineColor: WidgetStateColor.resolveWith(
                                  (states) => walletEnabled
                                      ? primaryColor
                                      : grey.withOpacity(0.3)),
                              trackColor: WidgetStateColor.resolveWith(
                                  (states) => walletEnabled
                                      ? primaryColor
                                      : grey.withOpacity(0.3)),
                              value: walletEnabled,
                              onChanged: (bool value) {
                                if (mounted) {
                                  stateChange(() {
                                    walletEnabled = value;
                                  });
                                }
                              })
                        ]),
                    const Divider(
                      indent: 20,
                      endIndent: 20,
                    ),
                    buildOption(
                        AppLocalizations.of(context)!.cash,
                        SvgPicture.asset(
                          cashIcon,
                          fit: BoxFit.cover,
                          width: 25,
                          height: 25,
                        ),
                        (mounted) ? stateChange : setState),
                    const Divider(
                      indent: 20,
                      endIndent: 20,
                    ),
                    buildOption(
                        ".... .... .... 9332",
                        SvgPicture.asset(creditCardIcon),
                        (mounted) ? stateChange : setState),
                    const Divider(
                      indent: 20,
                      endIndent: 20,
                    ),
                    buildOption(
                        ".... .... .... 4793",
                        SvgPicture.asset(creditCardIcon),
                        (mounted) ? stateChange : setState),
                    const Divider(
                      indent: 20,
                      endIndent: 20,
                    ),
                    Gap(h: 5.h),
                    CustomButton(
                        buttonText: AppLocalizations.of(context)!.add_card,
                        borderRadius: 12,
                        onPressed: onAddCard)
                  ]),
                ));
          });
        });
  }

  onAddCard() {
    setState(() {
      groupValue = tempGroupValue;
    });
    context.pop();
    context.push(path.addCard);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.only(bottom: 10, top: 10),
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        width: MediaQuery.sizeOf(context).width - 10.w,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(30),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 2,
              blurRadius: 5,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            GestureDetector(
              onTap: () => openTimePicker(
                context,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  const Icon(Icons.access_time_filled_rounded),
                  Gap(w: 2.w),
                  Text(
                      selectedTime.isNotEmpty
                          ? selectedTime
                          : AppLocalizations.of(context)!.now,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.bold,
                            fontFamily: "Plus Jakarta Sans",
                          )),
                ],
              ),
            ),
            GestureDetector(
              onTap: () => openCashPicker(),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  SvgPicture.asset(cashIcon),
                  Gap(w: 2.w),
                  Text(groupValue,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            fontWeight: FontWeight.bold,
                            fontFamily: "Plus Jakarta Sans",
                          )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget buildOption(String title, Widget? icon, stateChange) {
    return GestureDetector(
      onTap: () {
        stateChange(
          () => tempGroupValue = title,
        );
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Row(
              children: [
                if (icon != null) icon,
                Gap(w: 8.w),
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                        fontWeight: FontWeight.w900,
                        fontFamily: "Plus Jakarta Sans",
                      ),
                ),
              ],
            ),
            SizedBox(
              width: 50,
              height: 50,
              child: Radio<String>(
                value: title,
                groupValue: tempGroupValue,

                onChanged: (value) =>
                    stateChange(() => tempGroupValue = value!),
                materialTapTargetSize: MaterialTapTargetSize
                    .shrinkWrap, // Add this line to make the circle bigger
                visualDensity: VisualDensity
                    .compact, // Add this line to adjust the size of the radio button
              ),
            ),
          ],
        ),
      ),
    );
  }
}
