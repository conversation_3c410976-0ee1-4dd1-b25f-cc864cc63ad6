import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/utils/images.dart';

import '../../../../core/shared_widgets/dialog_with_image_builder.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import 'circle_button_with_icon.dart';
import 'wave_painter.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


Future<void> emergencyAlertPage({
  required BuildContext context,
}) {
  return showGeneralDialog(
    barrierDismissible: false,
    barrierLabel: '',
    barrierColor: const Color(0x00ffffff),
    transitionDuration: const Duration(milliseconds: 500),
    pageBuilder: (ctx, anim1, anim2) => const Dialog.fullscreen(
        backgroundColor: Color(0x00ffffff), child: EmergencyDialogContent()),
    transitionBuilder: (ctx, anim1, anim2, child) => BackdropFilter(
      filter:
          ImageFilter.blur(sigmaX: 10 * anim1.value, sigmaY: 10 * anim1.value),
      child: FadeTransition(
        opacity: anim1,
        child: child,
      ),
    ),
    context: context,
  );
}

class EmergencyDialogContent extends StatefulWidget {
  const EmergencyDialogContent({super.key});

  @override
  State<EmergencyDialogContent> createState() => _EmergencyDialogContentState();
}

class _EmergencyDialogContentState extends State<EmergencyDialogContent> {
  @override
  initState() {
    super.initState();

    Timer(const Duration(seconds: 5), () async {
      dialogWithImageBuilder(
          context: context,
          image: shareEmergencyContact,
          textWidget: Column(children: [
            // Text("Dont Worry! ",
            //     style: Theme.of(context).textTheme.bodySmall!.copyWith(
            //           fontFamily: "Plus Jakarta Sans",
            //           fontWeight: FontWeight.bold,
            //         )),
            Text(AppLocalizations.of(context)!.do_not_worry,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontFamily: "Plus Jakarta Sans",
                      fontWeight: FontWeight.bold,
                    )),
          ]),
          successText: "",
          buttonText: AppLocalizations.of(context)!.okay,
          showButtonInside: true,
          onPressed: () {
            context.pop();
            context.pop();
          });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Column(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        const Gap(),
        Ripples(
          child: Transform.scale(
            scale: 1.2,
            child: CircleButton(
              icon: Icons.crisis_alert_sharp,
              backgroundColor: Colors.red,
              imageIcon: Transform.scale(
                  scale: 0.5, child: SvgPicture.asset(alertLocation)),
            ),
          ),
        ),

        // Gap(h: 5.h),
        Container(
          padding: const EdgeInsets.all(20),
          margin: const EdgeInsets.all(30),
          decoration: BoxDecoration(
            color: Colors.grey[700],
            borderRadius: BorderRadius.circular(18),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              const CircleAvatar(
                  minRadius: 20,
                  backgroundColor: white,
                  child: Icon(Icons.share_location_rounded, color: Colors.red)),
              Gap(w: 2.w),
              SizedBox(
                width: MediaQuery.sizeOf(context).width * 0.6,
                child: Text(AppLocalizations.of(context)!.sharing_location,
                    softWrap: true,
                    maxLines: 3,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          fontFamily: "Plus Jakarta Sans",
                          color: white,
                          fontWeight: FontWeight.bold,
                        )),
              ),
            ],
          ),
        ),
        GestureDetector(
          onTap: () {
            context.pop();
          },
          child: CircleAvatar(
              minRadius: 30,
              backgroundColor: grey[600],
              child: SvgPicture.asset(cancelIcon)),
        )
      ],
    ));
  }
}
