import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/images.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class StatsCard extends StatelessWidget {
  final num rating;
  final num trips;
  final num years;

  const StatsCard({
    Key? key,
    required this.rating,
    this.trips = 0,
    this.years = 0,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    var mediumStyle = Theme.of(context)
        .textTheme
        .bodySmall!
        .copyWith(fontFamily: "Plus Jakarta Sans", fontWeight: FontWeight.bold);
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: <Widget>[
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        SvgPicture.asset(sharpStarIcon),
                        const Gap(w: 20),
                        Text( AppLocalizations.of(context)!.rating, style: mediumStyle),
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: <Widget>[
                        SvgPicture.asset(carIcon),
                        const Gap(w: 20),
                        Text( AppLocalizations.of(context)!.trips,  style: mediumStyle),
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: <Widget>[
                        SvgPicture.asset(timeIcon),
                        const Gap(w: 20),
                        Text( AppLocalizations.of(context)!.years,  style: mediumStyle),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const VerticalDivider(color: Colors.grey),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: <Widget>[
                    Row(
                      children: [
                        Text(
                          '$rating ${ AppLocalizations.of(context)!.rating}',
                          textAlign: TextAlign.start,
                           style: mediumStyle
                        )
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: [
                        Text('$trips ${ AppLocalizations.of(context)!.trips}', textAlign: TextAlign.start, style: mediumStyle),
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: [
                        Text('$years ${ AppLocalizations.of(context)!.years}', textAlign: TextAlign.start, style: mediumStyle),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
