import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/routes/paths.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_button.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';

class AccountBlockInfo extends StatefulWidget {
  const AccountBlockInfo({
    super.key,
  });

  @override
  State<AccountBlockInfo> createState() => _AccountBlockInfoState();
}

class _AccountBlockInfoState extends State<AccountBlockInfo> {
  bool _isBlocked = false;
  String? _banTill;

  @override
  void initState() {
    super.initState();
    final user = context.read<AuthBloc>().user;
    _isBlocked = user.user?.activationStatus?.toLowerCase() == 'block';
    _banTill = user.user?.banTill;
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    return BlocListener<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is AuthGetProfileSuccess) {
          final blocked = state.driverProfile.user?.activationStatus?.toLowerCase() == 'block';
          final banTill = state.driverProfile.user?.banTill;

          if (blocked != _isBlocked || banTill != _banTill) {
            setState(() {
              _isBlocked = blocked;
              _banTill = banTill;
            });
          }
        }
      },
      child: Visibility(
        visible: _isBlocked,
        child: SizedBox(
          height: size.height,
          width: size.width,
          child: BackdropFilter(
            filter: ImageFilter.blur(sigmaY: 5, sigmaX: 5),
            child: Column(
              children: [
                Expanded(
                  child: AbsorbPointer(absorbing: true, child: Container()),
                ),
                Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(size.width * 0.02),
                      topRight: Radius.circular(size.width * 0.02),
                    ),
                  ),
                  child: Column(
                    children: [
                      Container(
                        height: size.height * 0.15,
                        width: size.width,
                        decoration: BoxDecoration(
                          color: Colors.red,
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(size.width * 0.02),
                            topRight: Radius.circular(size.width * 0.02),
                          ),
                        ),
                        child: Container(
                          padding: EdgeInsets.all(size.width * 0.02),
                          child: Image.asset("assets/images/account/block.png"),
                        ),
                      ),
                      Gap(h: 2.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 3.w),
                        child: Builder(builder: (context) {
                          if (_banTill != null) {
                            final ban = DateTime.parse(_banTill!);
                            final difference = ban.difference(DateTime.now());
      
                            String formatted = "";
                            if (difference.inHours > 1) {
                              formatted += "${difference.inHours} ${Utils.appText(context: context, text: "Hour", arabicText: "ساعه")} ";
                            }
                            formatted += "${difference.inMinutes % 60} ${Utils.appText(context: context, text: "minutes", arabicText: "دقيقه")}";

                            return Column(
                              children: [
                                Text(
                                  Utils.appText(
                                    context: context,
                                    text: "Your account has been banned for",
                                    arabicText: "لقد تم حظر حسابك لمدة ",
                                  ),
                                  textAlign: TextAlign.center,
                                  style: Theme.of(context).textTheme.displayMedium!.copyWith(
                                        color: black,
                                        fontWeight: FontWeight.bold,
                                        fontFamily: "Plus Jakarta Sans",
                                        fontSize: 18.sp,
                                      ),
                                ),
                                Text(
                                  formatted,
                                  style: Theme.of(context).textTheme.displayMedium!.copyWith(
                                        color: black,
                                        fontWeight: FontWeight.bold,
                                        fontFamily: "Plus Jakarta Sans",
                                        fontSize: 18.sp,
                                      ),
                                ),
                              ],
                            );
                          }
      
                          return Text(
                              Utils.appText(
                                  context: context,
                                  text: "Your account has been banned ...",
                                  arabicText: "لقد تم حظر حسابك لمدة ..."),
                              textAlign: TextAlign.center,
                              style: Theme.of(context).textTheme.displayMedium!.copyWith(
                                  color: black,
                                  fontWeight: FontWeight.bold,
                                  fontFamily: "Plus Jakarta Sans",
                                  fontSize: 18.sp));
                        }),
                      ),
                      Gap(h: 2.h),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 3.w),
                        child: Text(
                            Utils.appText(
                                context: context,
                                text: "You need to contact moderator to unban",
                                arabicText: "تحتاج إلى الاتصال بالمشرف لإلغاء الحظر"),
                            textAlign: TextAlign.center,
                            style: Theme.of(context)
                                .textTheme
                                .displayMedium!
                                .copyWith(color: black, fontWeight: FontWeight.bold, fontFamily: "Plus Jakarta Sans")),
                      ),
                      Gap(h: 2.h),
                      CustomButton(
                        buttonText: Utils.appText(context: context, text: "Moderator", arabicText: "مشرف"),
                        borderRadius: size.width * 0.03,
                        height: size.height * 0.07,
                        color: primaryColorDark,
                        onPressed: () {
                          context.push(supervisor);
                        },
                      ),
                      Gap(h: 2.h),
                      CustomButton(
                        buttonText: Utils.appText(context: context, text: "Contact Us", arabicText: "اتصل بنا"),
                        borderRadius: size.width * 0.03,
                        height: size.height * 0.07,
                        color: primaryColorDark,
                        onPressed: () {
                          context.push(connectUs);
                        },
                      ),
                      Gap(h: 2.h),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
