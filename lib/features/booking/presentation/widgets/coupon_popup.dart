import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/utils/colors.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/images.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class CouponPopUp extends StatefulWidget {
  const CouponPopUp({super.key});

  @override
  CouponPopUpState createState() => CouponPopUpState();
}

class CouponPopUpState extends State<CouponPopUp> {
  bool isCodeCorrect = true;
  final  textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      // Wrap the AlertDialog with a GestureDetector
      onTap: () {
        context.pop();
      }, // Empty onTap function to prevent dismiss when outside area is touched
      child: Container(
        color: Colors.transparent,
        height: MediaQuery.of(context).size.height,
        width: MediaQuery.of(context).size.height,
        child: GestureDetector(
          onTap: () {},
          child: AlertDialog(
            backgroundColor: Colors.transparent,
            contentPadding: EdgeInsets.zero,
            content: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                  color: white, borderRadius: BorderRadius.circular(14)),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    children: [
                      SvgPicture.asset(
                        couponIcon,
                        width: 40,
                        height: 40,
                      ),
                      Gap(w: 4.w),
                      Text(
                        AppLocalizations.of(context)!.coupon,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                  const Divider(indent: 16, endIndent: 16),
                  Gap(h: 2.h),
                  SizedBox(
                    child: TextField(
                      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                          color: isCodeCorrect ? primaryColor : Colors.red,
                          fontWeight: FontWeight.bold),
                      controller: textEditingController,
                      decoration: InputDecoration(
                        filled: true,
                        suffix: !isCodeCorrect
                            ? Text(
                                AppLocalizations.of(context)!.wrong_code,
                                style: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(color: Colors.red),
                              )
                            : null,
                        fillColor: isCodeCorrect
                            ? primaryColor.withOpacity(0.1)
                            : Colors.red.withOpacity(0.1),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(10),
                          borderSide: BorderSide.none,
                        ),
                      ),
                    ),
                  ),
                  Gap(h: 3.h),
                  CustomButton(
                    buttonText: AppLocalizations.of(context)!.apply,
                    borderRadius: 12,
                    onPressed: () {
                      setState(() {
                        isCodeCorrect = false;
                      });
                    },
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
