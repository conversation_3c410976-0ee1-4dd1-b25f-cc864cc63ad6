import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:action_slider/action_slider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';

class CustomSwipeButton extends StatefulWidget {
  final void Function()? onSwipe;
  final bool atPickup;
  String text;
  CustomSwipeButton({
    Key? key,
    required this.onSwipe,
    required this.text,
    this.atPickup = false,
  }) : super(key: key);

  @override
  State<CustomSwipeButton> createState() => _CustomSwipeButtonState();
}

class _CustomSwipeButtonState extends State<CustomSwipeButton> {
  SharedPreferences? preferences;
  init() async {
    preferences = await SharedPreferences.getInstance();
    setState(() {});
  }

  @override
  void initState() {
    init();
    super.initState();
  }

  final controller = ActionSliderController();
  @override
  Widget build(BuildContext context) {
    return BlocListener<TripBloc, TripState>(
      listener: (context, state) {
        if (state is ArrivedAtPickup && widget.atPickup) {
          controller.success(); //starts success animation
        }
        if (state is TripEnd) {
          controller.success(); //starts success animation
        }
      },
      child: ActionSlider.standard(
        width: double.infinity,
        controller: controller,
        actionThresholdType: ThresholdType.release,
        onTap: (controller, pos) {},
        action: (controller) async {
          log("sliding");
          widget.onSwipe!();
          controller.loading();

          //starts loading animation
        },
        toggleColor: Colors.green,
        sliderBehavior: SliderBehavior.stretch,
        rolling: false,
        icon: Icon(
          preferences?.getString("languageCode") != "ar"
              ? Icons.double_arrow_rounded
              : Icons.keyboard_double_arrow_left_sharp,
          color: Colors.white,
        ),
        direction: preferences?.getString("languageCode") != "ar"
            ? TextDirection.ltr
            : TextDirection.rtl,
        child: Text(
          widget.text,
          style: Theme.of(context).textTheme.bodySmall!.copyWith(color: grey),
        ),
      ),
    );
    // return SwipeButton.expand(
    //   thumb: const Icon(
    //     Icons.double_arrow_rounded,
    //     color: white,
    //   ),
    //   borderRadius: BorderRadius.circular(12),
    //   activeThumbColor: primaryColor,
    //   activeTrackColor: Colors.grey.shade300,
    //   onSwipe: widget.onSwipe,
    // child: Text(
    //   widget.text,
    //   style: Theme.of(context).textTheme.bodySmall!.copyWith(color: grey),
    // ),
    // );

    // return SwipableButton(
    //   onPressed: widget.onSwipe!,
    //   text: widget.text,
    // );
  }
}

// import 'package:flutter/material.dart';

class SwipableButton extends StatelessWidget {
  final String text;
  final Function onPressed;

  const SwipableButton(
      {super.key, required this.text, required this.onPressed});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 50.w,
      child: Dismissible(
        // resizeDuration: Duration(milliseconds: 0),
        key: UniqueKey(),
        // direction: TextDirection.rtl, // Set the dismiss direction to RTL for Arabic
        onDismissed: (_) {
          // Handle button swipe
          onPressed();
        },
        background: Container(
          alignment: Alignment.centerRight,
          width: 90.w,
          height: 55,
          color: Colors.green,
          child: const Padding(
            padding: EdgeInsets.only(right: 16.0),
            child: Icon(
              Icons.double_arrow_rounded,
              color: white,
            ),
          ),
        ),
        child: Container(
          height: 55,
          // width: 100,
          alignment: Alignment.centerLeft,
          color: Colors.grey,
          child: Padding(
            padding: const EdgeInsets.only(left: 16.0),
            child: Text(
              text,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16.0,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
