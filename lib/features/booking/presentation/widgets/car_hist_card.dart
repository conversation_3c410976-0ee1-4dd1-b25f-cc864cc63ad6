import 'package:flutter/material.dart';

import '../../../../core/shared_widgets/gap.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class CarHistory extends StatelessWidget {
  final String memberSince;
  final String carModel;
  final String plateNumber;

  const CarHistory({
    Key? key,
    required this.memberSince,
    required this.carModel,
    required this.plateNumber,
  }) : super(key: key);
  @override
  Widget build(BuildContext context) {
    var mediumStyle = Theme.of(context)
        .textTheme
        .bodySmall!
        .copyWith(fontFamily: "Plus Jakarta Sans", fontWeight: FontWeight.bold);
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey),
        borderRadius: BorderRadius.circular(10.0),
      ),
      child: IntrinsicHeight(
        child: Row(
          children: <Widget>[
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        Text(AppLocalizations.of(context)!.member_since,
                            style: mediumStyle),
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: <Widget>[
                        Text(AppLocalizations.of(context)!.car_brand,
                            style: mediumStyle),
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: <Widget>[
                        Text(AppLocalizations.of(context)!.plate_number,
                            style: mediumStyle),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            const VerticalDivider(color: Colors.grey),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  children: <Widget>[
                    Row(
                      children: [
                        Text(memberSince,
                            textAlign: TextAlign.start, style: mediumStyle)
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: [
                        Text(carModel,
                            textAlign: TextAlign.start, style: mediumStyle),
                      ],
                    ),
                    const Gap(),
                    Row(
                      children: [
                        Text(plateNumber,
                            textAlign: TextAlign.start, style: mediumStyle),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
