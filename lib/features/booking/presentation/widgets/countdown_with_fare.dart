import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';

class CountDownWithFare extends StatefulWidget {
  final ValueNotifier<Duration> elapsedTimeNotifier;
  final ValueNotifier<double> priceChangeNotifier;
  final Timer timer;
  final Timer pricer;

  const CountDownWithFare(
      {super.key,
      required this.elapsedTimeNotifier,
      required this.priceChangeNotifier,
      required this.timer,
      required this.pricer});

  @override
  CountDownWithFareState createState() => CountDownWithFareState();
}

class CountDownWithFareState extends State<CountDownWithFare> {
  late DateTime startTime;

  @override
  void initState() {
    super.initState();
    startTime = DateTime.now();
  }

  @override
  void dispose() {
    super.dispose();
    widget.timer.cancel();
    widget.pricer.cancel();
  }

  void _updateElapsedTime(Timer t) {
    final newElapsedTime = DateTime.now().difference(startTime);
    widget.elapsedTimeNotifier.value = newElapsedTime;
  }

  void _updatePriceChangeNotifier(Timer t) {
    final newElapsedTime = DateTime.now().difference(startTime);

    final newPrice = newElapsedTime.inSeconds * 0.01;

    widget.priceChangeNotifier.value = newPrice;
  }

  String formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');

    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));

    return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: MediaQuery.sizeOf(context).width * 0.9,
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 5,
            blurRadius: 7,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          ValueListenableBuilder<Duration>(
            valueListenable: widget.elapsedTimeNotifier,
            builder: (context, value, child) {
              return Text(
                '${value.inHours}:${value.inMinutes.remainder(60).toString().padLeft(2, '0')}:${value.inSeconds.remainder(60).toString().padLeft(2, '0')}',
                style: textTheme.bodyMedium!.copyWith(
                  fontSize: 13,
                  fontWeight: FontWeight.bold,
                ),
              );
            },
          ),
          // ValueListenableBuilder<double>(
          //   valueListenable: widget.priceChangeNotifier,
          //   builder: (context, value, child) {
          //     return Text(
          //       '${AppLocalizations.of(context)!.price} ${value.toStringAsFixed(1)} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}',
          //       style: textTheme.bodyMedium!.copyWith(
          //         fontSize: 13,
          //         fontWeight: FontWeight.bold,
          //       ),
          //     );
          //   },
          // ),
        ],
      ),
    );
  }
}
