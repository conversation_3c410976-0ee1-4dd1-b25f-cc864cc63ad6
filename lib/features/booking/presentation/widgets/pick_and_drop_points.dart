import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import 'vertical_dotted_line.dart';

pickAndDropPoints(
  context,
  String startLocation,
  String? destinationLocation,
  double? hPadding,
) {
  return Column(
    children: [
      SizedBox(
        width: MediaQuery.sizeOf(context).width * 0.8,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Container(
              width: 30,
              height: 30,
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(
                color: primaryColor,
                border: Border.all(
                  color: primaryColor,
                ),
                borderRadius: BorderRadius.circular(57),
              ),
              child: Center(
                child: Text(
                  AppLocalizations.of(context)!.a,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(color: white),
                ),
              ),
            ),
            Gap(w: 1.w),
            Expanded(
              child: Text(
                startLocation,
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      fontSize: 12,
                      color: primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
              ),
            ),
          ],
        ),
      ),
      Visibility(
        visible: destinationLocation != "",
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: hPadding ?? 14.w, vertical: 10),
          child: const Align(
            alignment: AlignmentDirectional.centerStart,
            child: VerticalDottedLine(height: 20),
          ),
        ),
      ),
      Visibility(
        visible: destinationLocation != "",
        child: SizedBox(
          width: MediaQuery.sizeOf(context).width * 0.8,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 30,
                height: 30,
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: primaryColor,
                  border: Border.all(
                    color: primaryColor,
                  ),
                  borderRadius: BorderRadius.circular(57),
                ),
                child: Center(
                  child: Text(
                    AppLocalizations.of(context)!.b,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(
                          color: white,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                ),
              ),
              Gap(w: 1.w),
              Expanded(
                child: Text(
                  destinationLocation ?? "",
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontSize: 12,
                        color: primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
        ),
      ),
    ],
  );
}
