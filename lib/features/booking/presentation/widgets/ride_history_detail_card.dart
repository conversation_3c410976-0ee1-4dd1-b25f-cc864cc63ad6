import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';


class RideHistoryCard extends StatelessWidget {
  final Widget firstWidget;
  final String text;
  final Widget trailingWidget;

  const RideHistoryCard(
      {Key? key,
      required this.firstWidget,
      required this.text,
      required this.trailingWidget})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: black.withOpacity(0.1)),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          firstWidget,
          Text(text,
              style: Theme.of(context)
                  .textTheme
                  .bodySmall!
                  .copyWith(fontWeight: FontWeight.bold)),
          Gap(w: 2.w),
          Container(
            height: 35,
            width: 1,
            color: black.withOpacity(0.1),
          ),
          trailingWidget,
        ],
      ),
    );
  }
}
