import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import '../../../../core/utils/colors.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../../core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/routes/paths.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/features/booking/model/reject_reason.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';

class CancelRideWarningModal extends StatelessWidget {
  const CancelRideWarningModal({super.key});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            height: 64,
            child: Center(
              child: Text(
                AppLocalizations.of(context)!.cancel_trip,
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: "Plus Jakarta Sans",
                      fontSize: 17.sp,
                      fontWeight: FontWeight.bold,
                    ),
              ),
            ),
          ),
          Divider(indent: 20, endIndent: 20, color: black.withOpacity(0.2)),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              AppLocalizations.of(context)!.cancel_trip_warning,
              textAlign: TextAlign.center,
            ),
          ),
          const Gap(w: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Flexible(
                child: CustomButton(
                  buttonText: AppLocalizations.of(context)!.no,
                  onPressed: () => Navigator.of(context).pop(false),
                  width: 35.w,
                  height: 56,
                  borderOnly: true,
                  borderRadius: 10,
                ),
              ),
              const Gap(w: 16),
              Flexible(
                child: CustomButton(
                  color: Colors.red,
                  buttonText: AppLocalizations.of(context)!.yes,
                  borderRadius: 10,
                  height: 56,
                  width: 35.w,
                  onPressed: () => Navigator.of(context).pop(true),
                ),
              ),
            ],
          ),
          const Gap(h: 16),
        ],
      ),
    );
  }
}

class CancelRidePopUp extends StatefulWidget {
  const CancelRidePopUp({super.key});

  @override
  State<CancelRidePopUp> createState() => _CancelRidePopUpState();
}

class _CancelRidePopUpState extends State<CancelRidePopUp> {
  TripCancelReason? selectedReason;
  @override
  void initState() {
    context.read<TripBloc>().add(const RejectTripReason());
    super.initState();
  }

  final controller = TextEditingController();
  @override
  Widget build(BuildContext context) {
    final cancellReasons = context.watch<TripBloc>().reasons;
    return Center(
      child: Material(
        color: Colors.transparent,
        child: StatefulBuilder(
          builder: (context, stateSetter) {
            var radioTextStyle =
                Theme.of(context).textTheme.bodyMedium!.copyWith(
                      fontFamily: "Plus Jakarta Sans",
                      fontWeight: FontWeight.w600,
                      fontSize: 16.sp,
                    );
            return BlocConsumer<TripBloc, TripState>(
              listener: (context, state) {
                if (state is TripRejected) {
                  // CustomSocket.connectAndLidddsten(context: context);
                  CustomSocket.subscribeNewReqChannel(
                    globalDriverProfile.user?.id ?? '',
                  );

                  Navigator.pop(context);
                  context.go(home);
                }
                if (state is RejectReasonLoaded) {
                  if (state.reasons.isNotEmpty) {
                    selectedReason = state.reasons.first;
                  }
                }
                if (state is TripRejectError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      backgroundColor: Colors.red,
                      content: Text(
                        "Failed",
                        textAlign: TextAlign.center,
                      ),
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is RejectReasonLoading) {
                  return const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation(primaryColor),
                    ),
                  );
                }

                return Container(
                  decoration: const BoxDecoration(
                    color: white,
                    borderRadius: BorderRadius.all(Radius.circular(12)),
                  ),
                  margin: const EdgeInsets.symmetric(horizontal: 20),
                  padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom,
                  ),
                  child: SingleChildScrollView(
                    reverse: true,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        SizedBox(
                          height: 64,
                          child: Row(
                            children: [
                              Gap(w: 1.w),
                              IconButton(
                                icon: const Icon(
                                  Icons.arrow_back_ios_new_outlined,
                                ),
                                onPressed: () {
                                  context.pop();
                                },
                              ),
                              Gap(w: 5.w),
                              Text(
                                AppLocalizations.of(context)!
                                    .cancellation_reason,
                                style: radioTextStyle.copyWith(
                                  fontSize: 17.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Divider(
                          indent: 20,
                          endIndent: 20,
                          color: black.withOpacity(0.2),
                        ),
                        Column(
                          children: List.generate(
                            cancellReasons.length + 1,
                            (index) => index == cancellReasons.length
                                ? RadioListTile(
                                    title: Text(
                                      AppLocalizations.of(context)!.other,
                                      style: radioTextStyle,
                                    ),
                                    value: null,
                                    groupValue: selectedReason,
                                    onChanged: (value) {
                                      setState(() {
                                        selectedReason = value;
                                      });
                                    },
                                  )
                                : RadioListTile(
                                    title: Text(
                                      Utils.appText(
                                        context: context,
                                        text:
                                            cancellReasons[index].reason ?? '',
                                        arabicText:
                                            cancellReasons[index].arabicReason,
                                      ),
                                      style: radioTextStyle,
                                    ),
                                    value: cancellReasons[index],
                                    groupValue: selectedReason,
                                    onChanged: (value) {
                                      setState(() {
                                        selectedReason = cancellReasons[index];
                                      });
                                    },
                                  ),
                          ),
                        ),
                        Visibility(
                          visible: selectedReason == null,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: TextField(
                              controller: controller,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium!
                                  .copyWith(fontFamily: 'Plus Jakarta Sans'),
                              textAlignVertical: TextAlignVertical.top,
                              maxLines: 4,
                              minLines: 1,
                              onChanged: (value) {},
                              decoration: InputDecoration(
                                hintText: AppLocalizations.of(context)!
                                    .enter_other_reason,
                                labelText: AppLocalizations.of(context)!
                                    .enter_other_reason,
                                labelStyle:
                                    const TextStyle(color: blackTextColor),
                                hintStyle: Theme.of(context)
                                    .textTheme
                                    .bodySmall!
                                    .copyWith(fontFamily: 'Plus Jakarta Sans'),
                              ),
                            ),
                          ),
                        ),
                        Gap(h: 2.h),
                        CustomButton(
                          buttonText: Utils.appText(
                            context: context,
                            text: "Cancel trip",
                            arabicText: "الغاء الرحله ",
                          ),
                          width: 45.w,
                          borderRadius: 12,
                          isLoading: state is TripRejecting,
                          onPressed: () {
                            if (selectedReason == null) {
                              if (controller.text.trim() == "") {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    backgroundColor: Colors.red,
                                    content: Text(
                                      AppLocalizations.of(context)!
                                          .enter_other_reason,
                                      textAlign: TextAlign.center,
                                    ),
                                  ),
                                );
                                return;
                              }
                            }
                            final tripID =
                                context.read<TripBloc>().requestAccept.trip?.id;
                            context.read<TripBloc>().add(
                                  RejectTrip(
                                    tripID: tripID,
                                    cancelReasonID: selectedReason?.id ?? "",
                                    reason: selectedReason?.reason ??
                                        controller.text,
                                  ),
                                );
                          },
                        ),
                        Gap(h: 5.h),
                      ],
                    ),
                  ),
                );
              },
            );
          },
        ),
      ),
    );
  }
}
