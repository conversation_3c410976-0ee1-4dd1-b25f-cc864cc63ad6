/*
import 'package:flutter/material.dart';
import 'package:geocoding/geocoding.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';

import '../../../../core/shared_widgets/custom_divider.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import 'police_station_driver_info.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class PoliceStationBottomSheet extends StatelessWidget {
  final LatLng location;
  const PoliceStationBottomSheet({super.key, required this.location});

  Future<String> getLocationName() async {
    final placemarks =
        await placemarkFromCoordinates(location.latitude, location.longitude);
    final locality = "${placemarks[0].locality}, ";
    final subLocality = "${placemarks[0].subLocality}, ";
    final street = placemarks[0].street ?? " ";
    final placeName = locality + subLocality + street;
    return placeName;
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        height: MediaQuery.sizeOf(context).height * 0.6,
        decoration: BoxDecoration(
            color: white, borderRadius: BorderRadius.circular(20)),
        width: MediaQuery.sizeOf(context).width,
        child: SingleChildScrollView(
            child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Gap(h: 2.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Gap(w: 5.w),
                IconButton(
                  icon: Icon(Icons.cancel,
                      size: 35, color: black.withOpacity(0.2)),
                  onPressed: () {
                    context.pop();
                  },
                ),
                Gap(w: 12.w),
                Row(
                  children: [
                    Transform.scale(
                        scale: 1.2,
                        child: Image.asset(
                          trafficStationMarker,
                          width: 30,
                          height: 30,
                        )),
                    Gap(w: 4.w),
                    Text(
                      AppLocalizations.of(context)!.police_location,
                      style: Theme.of(context).textTheme.displayMedium,
                    ),
                  ],
                ),
              ],
            ),
            const CustomDivider(),
            Container(
              padding: const EdgeInsets.all(15),
              margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 15),
              decoration: BoxDecoration(
                color: primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  const CircleAvatar(
                    minRadius: 20,
                    backgroundColor: white,
                    child: Icon(Icons.location_on, color: black),
                  ),
                  Gap(w: 4.w),
                  Expanded(
                    child: FutureBuilder(
                        future: getLocationName(),
                        builder: (context, snap) {
                          return Text(
                              snap.data ??
                                  AppLocalizations.of(context)!
                                      .fetching_place_information,
                              overflow: TextOverflow.ellipsis,
                              style: Theme.of(context)
                                  .textTheme
                                  .bodySmall!
                                  .copyWith(fontWeight: FontWeight.bold));
                        }),
                  ),
                ],
              ),
            ),
            const PoliceStationDriverInfo(
                name: "Jhon Doe",
                imageUrl:
                    "https://th.bing.com/th/id/R.********************************?rik=MRBhdWokGCpQzA&pid=ImgRaw&r=0"),
            const PoliceStationDriverInfo(
                name: "Natalia Jhon",
                imageUrl:
                    "https://th.bing.com/th/id/R.********************************?rik=MRBhdWokGCpQzA&pid=ImgRaw&r=0"),
            const PoliceStationDriverInfo(
                name: "Abebe Duresa",
                imageUrl:
                    "https://th.bing.com/th/id/R.********************************?rik=MRBhdWokGCpQzA&pid=ImgRaw&r=0"),
          ],
        )));
  }
}
*/
