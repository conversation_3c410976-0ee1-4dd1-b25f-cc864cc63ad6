import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import '../../../../core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class GenderSelectionWidget extends StatefulWidget {
  final String selectedGender;
  final Function(String) onGenderSelected;

  const GenderSelectionWidget(
      {super.key,
      required this.selectedGender,
      required this.onGenderSelected});

  @override
  GenderSelectionWidgetState createState() => GenderSelectionWidgetState();
}

class GenderSelectionWidgetState extends State<GenderSelectionWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30.h,
      width: 200,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.5),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        children: [
           Text(AppLocalizations.of(context)!.choose_gender,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Gap(),
          const Divider(),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () {
                  widget.onGenderSelected('Male');
                },
                child: Row(
                  // mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Icon(
                    //   Icons.male,
                    //   size: 40,
                    //   color: widget.selectedGender == 'Male'
                    //       ? primaryColor
                    //       : Colors.grey,
                    // ),
                    SvgPicture.asset(
                        widget.selectedGender == "Male"
                            ? maleIconSelected
                            : maleIconUnSelected,
                        width: 40,
                        height: 40),
                    Gap(w: 3.w),
                    Text(
                      AppLocalizations.of(context)!.male,
                      style: TextStyle(
                        color: widget.selectedGender == 'Male'
                            ? primaryColor
                            : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(),
              GestureDetector(
                onTap: () {
                  widget.onGenderSelected('Female');
                },
                child: Row(
                  // mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                        widget.selectedGender == "Female"
                            ? femaleIconSelected
                            : femaleIconUnSelected,
                        width: 40,
                        height: 40),
                    Gap(w: 3.w),
                    Text(AppLocalizations.of(context)!.female,
                      style: TextStyle(
                        color: widget.selectedGender == 'Female'
                            ? primaryColor
                            : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(),
              GestureDetector(
                onTap: () {
                  widget.onGenderSelected('both');
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.both,
                      style: TextStyle(
                        color: widget.selectedGender == 'both'
                            ? primaryColor
                            : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
