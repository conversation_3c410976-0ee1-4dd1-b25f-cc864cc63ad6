import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class LocationWarning extends StatelessWidget {
  const LocationWarning(
      {super.key, this.onCancelPressed, this.onEnablePressed});
  final void Function()? onCancelPressed;
  final void Function()? onEnablePressed;

  @override
  Widget build(BuildContext context) {
    var textStyle = Theme.of(context).textTheme.labelLarge!.copyWith(
          fontSize: 17.sp,
          fontWeight: FontWeight.w600,
          fontFamily: "Plus Jakarta Sans",
        );
    return AlertDialog(
      backgroundColor: Colors.transparent,
      contentPadding: EdgeInsets.zero,
      shadowColor: white,
      content: Container(
          padding: const EdgeInsets.all(15),
          decoration: BoxDecoration(
              color: white, borderRadius: BorderRadius.circular(14)),
          height: 38.h,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              SvgPicture.asset(warningSticker),
              Text(AppLocalizations.of(context)!.enable_location_permission,
                  textAlign: TextAlign.center, style: textStyle),
              Gap(h: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  CustomButton(
                      buttonText: AppLocalizations.of(context)!.enable,
                      borderRadius: 7,
                      width: 30.w,
                      borderOnly: true,
                      onPressed: onEnablePressed),
                  CustomButton(
                      buttonText:AppLocalizations.of(context)!.not_now,
                      borderRadius: 7,
                      width: 30.w,
                      borderOnly: true,
                      onPressed: onCancelPressed),
                ],
              ),
              Gap(h: 2.h),
            ],
          )),
    );
  }
}
