import 'package:flutter/material.dart';
// import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';

import '../../../../core/utils/colors.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class ToggleButton extends StatefulWidget {
  final ValueChanged<bool> onToggleChanged;
  bool value;

  ToggleButton({
    super.key,
    required this.onToggleChanged,
    this.value = false,
  });

  @override
  State<ToggleButton> createState() => _ToggleButtonState();
}

class _ToggleButtonState extends State<ToggleButton> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      width: double.infinity,
      color:
          widget.value ? primaryColor.withOpacity(0.5) : black.withOpacity(0.5),
      child: Center(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            Text(
              AppLocalizations.of(context)!.not_available,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: widget.value ? white.withOpacity(0.5) : white,
                  ),
            ),
            Transform.scale(
              scale: 1.1,
              child: Switch(
                value: widget.value,
                trackOutlineColor: WidgetStatePropertyAll(
                  widget.value ? primaryColor : black.withOpacity(0.5),
                ),
                thumbColor: const WidgetStatePropertyAll(Colors.white),
                activeColor: primaryColor,
                inactiveTrackColor: const Color.fromARGB(255, 225, 225, 225),
                onChanged: (value) => widget.onToggleChanged(widget.value),
                
              ),
            ),
            Text(
              AppLocalizations.of(context)!.available,
              style: Theme.of(context).textTheme.bodyMedium!.copyWith(
                    fontWeight: FontWeight.w600,
                    color: widget.value ? white : white.withOpacity(0.5),
                  ),
            ),
          ],
        ),
      ),
    );
  }
}
