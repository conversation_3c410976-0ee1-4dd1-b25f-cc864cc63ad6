import 'dart:async';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/model/accept_request.dart';
import 'package:yalla_gai_driver/features/booking/model/ride_request.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/get_directions.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/home_drawer_holder.dart';

import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/utils/launch_google_maps.dart';
import '../../bloc/trip_bloc/trip_bloc.dart';
import '../widgets/countdown_with_fare.dart';
import '../widgets/custom_drawer.dart';
import '../widgets/finished_trip_modal.dart';
import '../widgets/swipe_button.dart';

class TripStarted extends StatelessWidget {
  final ComingRideRequest rideRequest;
  final RequestAccept requestAccept;

  const TripStarted({
    required this.rideRequest,
    required this.requestAccept,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: white,
        drawerScrimColor: Colors.transparent,
        drawer: const CustomDrawer(),
        appBar: AppBar(
          leading: const HomeDrawerHolder(),
          toolbarHeight: 64,
        ),
        body: ScaffoldBody(
          request: rideRequest,
          requestAccept: requestAccept,
        ),
      ),
    );
  }
}

class ScaffoldBody extends StatefulWidget {
  final ComingRideRequest request;
  final RequestAccept requestAccept;

  const ScaffoldBody({
    super.key,
    required this.request,
    required this.requestAccept,
  });

  @override
  State<ScaffoldBody> createState() => _ScaffoldBodyState();
}

class _ScaffoldBodyState extends State<ScaffoldBody> with LocationMixin {
  final Completer<GoogleMapController> _controller = Completer();

  BitmapDescriptor sourceIcon = BitmapDescriptor.defaultMarker;
  BitmapDescriptor destinationIcon = BitmapDescriptor.defaultMarker;

  late DateTime startTime;
  late DateTime endTime;
  late Duration elapsedTime;
  late Timer timer;
  late Timer pricer;

  late ValueNotifier<Duration> elapsedTimeNotifier;
  late ValueNotifier<double> priceChangeNotifier;

  GoogleMapController? _googleMapController;

  @override
  initState() {
    setSourceAndDestinationIcons();

    startTime = DateTime.now();
    elapsedTime = Duration.zero;
    elapsedTimeNotifier = ValueNotifier<Duration>(elapsedTime);
    timer = Timer.periodic(const Duration(seconds: 1), _updateElapsedTime);
    pricer =
        Timer.periodic(const Duration(seconds: 1), _updatePriceChangeNotifier);
    priceChangeNotifier = ValueNotifier<double>(0);

    super.initState();
    WakelockPlus.enable();
  }

  @override
  void dispose() {
    WakelockPlus.disable();
    super.dispose();
  }

  void _updateElapsedTime(Timer t) {
    final newElapsedTime = DateTime.now().difference(startTime);
    elapsedTimeNotifier.value = newElapsedTime;
  }

  void _updatePriceChangeNotifier(Timer t) {
    final newElapsedTime = DateTime.now().difference(startTime);

    final newPrice = newElapsedTime.inSeconds * 0.001;

    priceChangeNotifier.value = newPrice;
  }

  void setSourceAndDestinationIcons() async {
    await Future.wait([
      rootBundle.load(carMapMarker).then((value) async {
        Codec codec = await instantiateImageCodec(
          value.buffer.asUint8List(),
          targetHeight: 72,
        );
        FrameInfo fi = await codec.getNextFrame();
        final byteImage =
            (await fi.image.toByteData(format: ImageByteFormat.png))
                ?.buffer
                .asUint8List();
        sourceIcon = BitmapDescriptor.bytes(Uint8List.view(byteImage!.buffer));
      }),
      rootBundle.load(userMapMarker).then((value) async {
        Codec codec = await instantiateImageCodec(
          value.buffer.asUint8List(),
          targetHeight: 60,
        );
        FrameInfo fi = await codec.getNextFrame();
        final byteImage =
            (await fi.image.toByteData(format: ImageByteFormat.png))
                ?.buffer
                .asUint8List();
        destinationIcon =
            BitmapDescriptor.bytes(Uint8List.view(byteImage!.buffer));
      }),
    ]);
    setState(() {
      setState(() {
        sourceMarker = Marker(
          markerId: const MarkerId("driver_source"),
          icon: sourceIcon,
          anchor: const Offset(0.5, 0.5),
          position: LatLng(
            currentPosition?.latitude ?? widget.request.pickupLatitude,
            currentPosition?.longitude ?? widget.request.pickupLongitude,
          ),
        );
      });
    });
  }

  late Marker sourceMarker = Marker(
    markerId: const MarkerId("driver_source"),
    icon: sourceIcon,
    anchor: const Offset(0.5, 0.5),
    position: LatLng(
      currentPosition?.latitude ?? widget.request.pickupLatitude,
      currentPosition?.longitude ?? widget.request.pickupLongitude,
    ),
  );

  double? _lastDirection;

  @override
  void onLocationUpdated(Position position) {
    super.onLocationUpdated(position);

    final latLng = LatLng(position.latitude, position.longitude);
    if (position.heading > 0) {
      _lastDirection = position.heading;
    }

    setState(() {
      sourceMarker = Marker(
        markerId: const MarkerId("driver_source"),
        icon: sourceIcon,
        anchor: const Offset(0.5, 0.5),
        // rotation: _lastDirection ?? position.heading,
        position: latLng,
      );
    });

    BlocProvider.of<TripBloc>(context).add(
      TripUpdateLocation(
        lat: latLng.latitude.toString(),
        lng: latLng.longitude.toString(),
        direction: (_lastDirection ?? position.heading).toString(),
        bygoogle: false,
      ),
    );
    CustomSocket().updateLocation(position.latitude, position.longitude);

    /*getPolylineBetweenTwoPoints(
          LatLng(newLoc.latitude, newLoc.longitude),
          LatLng(widget.request.destinationLatitude, widget.request.destinationLongitude),
          setState,
          routePoints,
          polylinePoints);*/

    _googleMapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: latLng,
          bearing: position.heading,
          zoom: 19,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        GoogleMap(
          myLocationButtonEnabled: false,
          initialCameraPosition: CameraPosition(
            target: LatLng(
              widget.request.pickupLatitude,
              widget.request.pickupLongitude,
            ),
            zoom: 14.5,
          ),
          onMapCreated: (controller) {
            _controller.complete(controller);
            _googleMapController = controller;
          },
          markers: {
            sourceMarker,
            if (widget.request.destinationLatitude != null &&
                widget.request.destinationLongitude != null)
              Marker(
                markerId: const MarkerId("driver_destination"),
                icon: destinationIcon,
                position: LatLng(
                  widget.request.destinationLatitude!,
                  widget.request.destinationLongitude!,
                ),
              ),
          },
        ),
        Positioned(
          top: 12,
          child: CountDownWithFare(
            elapsedTimeNotifier: elapsedTimeNotifier,
            priceChangeNotifier: priceChangeNotifier,
            timer: timer,
            pricer: pricer,
          ),
        ),
        Positioned(
          bottom: 0,
          child: Container(
            height: 130,
            width: MediaQuery.sizeOf(context).width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(22),
              color: white,
            ),
            child: Center(
              child: Container(
                margin: const EdgeInsets.symmetric(horizontal: 10),
                // width: 85.5.w,
                child: BlocConsumer<TripBloc, TripState>(
                  listener: (context, state) {
                    if (state is TripEnd) {
                      setState(() {
                        stopTimer();
                      });
                      CustomSocket.subscribeNewReqChannel(
                        globalDriverProfile.user?.id ?? '',
                      );

                      showBottomSheet(
                        context: context,
                        enableDrag: false,
                        builder: (context) {
                          return TripFinisedModal(
                            id: widget.requestAccept.trip?.id.toString() ?? "",
                            // id: widget.request.id.toString(),
                            price: state.tripStatus.cost.toString(),
                          );
                        },
                      );
                    }
                    if (state is ChangeTripStatusFailure) {
                      showCustomSnackbar(context, state.message);
                    }
                  },
                  builder: (context, state) {
                    return CustomSwipeButton(
                      onSwipe: () {
                        BlocProvider.of<TripBloc>(context).add(
                          ChangeTripStatus(
                            tripID:
                                widget.requestAccept.trip?.id.toString() ?? "",
                            status: 4,
                          ),
                        );
                      },
                      text: AppLocalizations.of(context)!.end_trip,
                    );
                  },
                ),
              ),
            ),
          ),
        ),
        if (widget.request.destinationLatitude != null &&
            widget.request.destinationLongitude != null)
          Positioned(
            bottom: 110,
            right: 20,
            child: GetDirectionsButton(
              onPressed: () => launchGoogleMapsApp(
                widget.request.destinationLatitude!,
                widget.request.destinationLongitude!,
              ),
            ),
          ),
      ],
    );
  }

  stopTimer() {
    timer.cancel();
    pricer.cancel();
  }
}
