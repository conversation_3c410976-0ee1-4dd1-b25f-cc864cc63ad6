import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/features/booking/bloc/tech_support/tech_supports_cubit.dart';

import '../../../../core/shared_widgets/back_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/images.dart';
import '../widgets/message.dart';

class TechSupport extends StatefulWidget {
  const TechSupport({super.key});

  @override
  State<TechSupport> createState() => _TechSupportState();
}

class _TechSupportState extends State<TechSupport> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    context.read<TechSupportsCubit>().fetchChat();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TechSupportsCubit, TechSupportsState>(
      listenWhen: (previous, current) => current is TechSupportsLoadedState,
      listener: (context, state) {
        // Auto-scroll to bottom when new messages arrive
        Timer(
          Durations.medium4,
          () {
            if (_scrollController.hasClients) {
              _scrollController.animateTo(
                _scrollController.position.maxScrollExtent,
                duration: Durations.short2,
                curve: Curves.ease,
              );
            }
          },
        );
      },
      builder: (context, state) {
        // Use watch to ensure rebuilds when cubit data changes
        final techSupportsCubit = context.watch<TechSupportsCubit>();

        // Debug: Print current chat count when UI rebuilds
        print("🔄 Tech Support UI rebuilding - Chat count: ${techSupportsCubit.chats.length}");

        return AppScaffold(
          isLoading: state is TechSupportsLoadingState,
          body: Column(
            children: [
              Stack(
                children: [
                  SvgPicture.asset(
                    width: MediaQuery.sizeOf(context).width,
                    appBarShape,
                  ),
                  Positioned(
                      top: 6.h,
                      left: 3.w,
                      child: SvgBackButton(
                        svgImage: SvgPicture.asset(
                          width: 36,
                          height: 36,
                          backButton,
                        ),
                      ),),
                  Align(
                      alignment: Alignment.center,
                      child: Column(
                        children: [
                          Gap(
                            h: 7.h,
                          ),
                          SvgBackButton(
                            svgImage: SvgPicture.asset(
                              height: 96,
                              serviceHeader,
                            ),
                          ),
                          const Gap(
                            h: 8,
                          ),
                          Text(
                            'Tech Support',
                            style: Theme.of(context)
                                .textTheme
                                .titleLarge!
                                .copyWith(color: Colors.black, fontSize: 26, fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),),
                ],
              ),
              Expanded(
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16),
                  itemCount: techSupportsCubit.chats.length,
                  itemBuilder: (context, index) {
                    final chat = techSupportsCubit.chats[index];
                    return SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        child: Message(
                            message: chat.message ?? "",
                            self: LocalStorage.instance.userId == chat.senderId,),);
                  },
                ),
              ),
              Container(
                color: const Color(0XFFc8c7cc).withOpacity(0.4),
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: TextField(
                        controller: techSupportsCubit.chatController,
                        decoration: InputDecoration(
                          hintText: AppLocalizations.of(context)!.type_a_message,
                          hintStyle: Theme.of(context)
                              .textTheme
                              .labelMedium!
                              .copyWith(color: Colors.black.withOpacity(0.2), fontWeight: FontWeight.w300),
                          border: UnderlineInputBorder(
                              borderSide: BorderSide(width: 1, color: Colors.black.withOpacity(0.2)),),
                        ),
                      ),
                    ),
                    Gap(w: 1.w),
                    InkWell(
                      child: SvgPicture.asset(send),
                      onTap: () {
                        context.read<TechSupportsCubit>().sendChat();
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
