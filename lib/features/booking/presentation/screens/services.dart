import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/images.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/cubit/carmodel_cubit.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_snack_bar.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/socket/custom_socket.dart';
import '../../../../core/utils/colors.dart';
import '../../bloc/service_bloc/service_bloc.dart';
import '../widgets/service_card.dart';

class ServicesPage extends StatefulWidget {
  const ServicesPage({super.key});

  @override
  ServicesPageState createState() => ServicesPageState();
}

class ServicesPageState extends State<ServicesPage> {
  @override
  void initState() {
    context.read<CarmodelCubit>().getCarCategory();

    super.initState();
  }

  bool driverActive = false;
  List<String> selectedCarIds = [];

  @override
  Widget build(BuildContext context) {
    final watchCarCubit = context.watch<CarmodelCubit>();
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Gap(w: 5.w),
                  IconButton(
                    icon: Icon(
                      Icons.cancel,
                      size: 40,
                      color: black.withOpacity(0.2),
                    ),
                    onPressed: () {
                      context.pop(false);
                    },
                  ),
                  Gap(w: 22.w),
                  Text(
                    AppLocalizations.of(context)!.services,
                    style: Theme.of(context).textTheme.displayMedium,
                  ),
                ],
              ),
              const Gap(),
              ListTile(
                title: Text(
                  AppLocalizations.of(context)!.activate_cars_to_receive_orders,
                ),
              ),
              const Gap(),
              Column(
                children: List.generate(
                  watchCarCubit.filteredcarCategory.length,
                  (index) => ServiceCard(
                    text: Utils.appText(
                      context: context,
                      text: watchCarCubit.filteredcarCategory[index].name ?? '',
                      arabicText:
                          watchCarCubit.filteredcarCategory[index].arabicName,
                    ),
                    leading: ClipOval(
                      // borderRadius: BorderRadius.circular(4),
                      child: CachedNetworkImage(
                        imageUrl:
                            "${AppEnvironment.publicBaseUrl}${watchCarCubit.filteredcarCategory[index].image}",
                        fit: BoxFit.contain,
                        width: 52,
                        height: 52,
                        errorWidget: (context, url, error) => Image.asset(
                          priorityCar,
                          width: 52,
                          height: 52,
                        ),
                      ),
                    ),
                    onChanged: (value) {
                      setState(() {
                        if (value == true) {
                          if (!selectedCarIds.contains(
                            watchCarCubit.filteredcarCategory[index].id
                                .toString(),
                          )) {
                            selectedCarIds.add(
                              watchCarCubit.filteredcarCategory[index].id
                                  .toString(),
                            );
                          }
                        } else {
                          if (selectedCarIds.contains(
                            watchCarCubit.filteredcarCategory[index].id
                                .toString(),
                          )) {
                            selectedCarIds.remove(
                              watchCarCubit.filteredcarCategory[index].id
                                  .toString(),
                            );
                          }
                        }
                      });

                      // log('${index}${value}');
                    },
                  ),
                ),
              ),
              const Gap(),
              BlocListener<ServiceBloc, ServiceState>(
                listener: (context, state) {
                  if (state is ServiceChangeStatusSuccess) {
                    CustomSocket.subscribeNewReqChannel(
                      globalDriverProfile.user?.id ?? '',
                    );

                    showCustomSnackbar(
                      context,
                      'Driver availability status has been updated successfully',
                    );
                    driverActive = true;

                    context.pop();
                  }
                  if (state is ServiceChangeStatusFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                  if (state is ServiceChangeStatusFaulire) {
                    showCustomSnackbar(context, state.message);
                  }
                },
                child: CustomButton(
                  buttonText: AppLocalizations.of(context)!.startup,
                  borderRadius: 12,
                  onPressed: selectedCarIds.isEmpty
                      ? null
                      : () {
                          BlocProvider.of<ServiceBloc>(context).add(
                            ServiceChangeStatus(
                              id: selectedCarIds.join(',').toString(),
                              status: 'on',
                            ),
                          );
                        },
                  width: 50.w,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ActivateCarModel {
  String? id, status;

  ActivateCarModel({required this.id, required this.status});
}

//  if (selectedCarIds
//                             .map((e) => e.id.toString())
//                             .toList()
//                             .contains(watchCarCubit
//                                 .filteredcarCategory[index].id
//                                 .toString())) {
//                           ActivateCarModel? itemToUpdate =
//                               selectedCarIds.firstWhere((element) =>
//                                   element.id.toString() ==
//                                   watchCarCubit.filteredcarCategory[index].id
//                                       .toString());
//                           itemToUpdate.status = value == false ? "OFF" : "ON";
//                         } else {
//                           selectedCarIds.add(ActivateCarModel(
//                               id: watchCarCubit.filteredcarCategory[index].id
//                                   .toString(),
//                               status: value == false ? "OFF" : "ON"));
//                         }
