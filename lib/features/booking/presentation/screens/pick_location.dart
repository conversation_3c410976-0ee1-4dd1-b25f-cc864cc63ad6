/*
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_webservice/places.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import '../../../../core/shared_widgets/custom_textfield.dart';
import 'pick_on_map.dart';
import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/places.dart';
import '../../domain/entity/location_entity.dart';
import '../widgets/circle_icon_button.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class PickLocation extends StatefulWidget {
  @override
  PickLocationState createState() => PickLocationState();
  final bool dropOff;
  final LocationEntity? source;
  final LocationEntity? destination;

  const PickLocation(
      {super.key,
      this.dropOff = false,
      required this.source,
      required this.destination});
}

class PickLocationState extends State<PickLocation> {
  final homeScaffoldKey = GlobalKey<ScaffoldState>();
  final searchScaffoldKey = GlobalKey<ScaffoldState>();
  final locationTextEditingController = TextEditingController();
  LocationEntity? pickedLocation;
  String selectedSuggestion = '';
  List<LocationEntity> suggestions = [];
  final List<LocationEntity> locations = [
    LocationEntity(
        locationText: "Petra",
        streetName: "Main Street",
        latitude: 31.9497,
        longitude: 35.8320),
    LocationEntity(
        locationText: "Wadi Rum",
        streetName: "Desert Road",
        latitude: 31.9447,
        longitude: 35.9220),
    LocationEntity(
        locationText: "Amman",
        streetName: "Rainbow Street",
        latitude: 31.9417,
        longitude: 35.9320),
    LocationEntity(
        locationText: "Dead Sea",
        streetName: "Salt Road",
        latitude: 31.947,
        longitude: 35.912320),
    LocationEntity(
        locationText: "Jerash",
        streetName: "Roman Street",
        latitude: 31.9447,
        longitude: 35.9120),
  ];

  Future<Position> getCurrentLocation() async {
    Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high);
    setState(() {
      locationTextEditingController.text =
          AppLocalizations.of(context)!.current_location;
      suggestions = [];
      pickedLocation = LocationEntity(
          locationText: AppLocalizations.of(context)!.current_location,
          latitude: position.latitude,
          longitude: position.longitude);
    });
    return position;
  }

  Future<void> searchPlaces(String query) async {
    if (query == "") {
      setState(() {
        suggestions = [];
        return;
      });
    }

    final result = await places.autocomplete(
      query,
      language: "en",
      types: ['geocode'],
      components: [Component(Component.country, 'jor')],
    );
    if (result.status == "OK") {
      final predictions = result.predictions;
      List<LocationEntity> updatedSuggestions = [];

      for (var prediction in predictions) {
        final placeDetails =
            await places.getDetailsByPlaceId(prediction.placeId ?? "");
        final location = placeDetails.result.geometry?.location;
        final latitude = location?.lat;
        final longitude = location?.lng;

        final suggestion = LocationEntity(
          locationText:
              // ignore: use_build_context_synchronously
              prediction.description ?? AppLocalizations.of(context)!.unknown,
          latitude: latitude ?? 0,
          longitude: longitude ?? 0,
        );

        updatedSuggestions.add(suggestion);
      }
      setState(() {
        suggestions = updatedSuggestions;
      });
    } else {}
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: homeScaffoldKey,
      appBar: AppBar(
          centerTitle: true,
          title: Text(
              "${AppLocalizations.of(context)!.set_your} ${!widget.dropOff ? AppLocalizations.of(context)!.pick_up : AppLocalizations.of(context)!.drop_off} ${AppLocalizations.of(context)!.point}",
              style: Theme.of(context)
                  .textTheme
                  .bodyLarge!
                  .copyWith(fontFamily: "Plus Jakarta Sans"))),
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              CustomTextField(
                  icon: Icons.search,
                  width: 90.w,
                  borderRadius: 4,
                  labelText:
                      "${AppLocalizations.of(context)!.set_your} ${!widget.dropOff ? AppLocalizations.of(context)!.pick_up : AppLocalizations.of(context)!.drop_off} ${AppLocalizations.of(context)!.point}",
                  textEditingController: locationTextEditingController,
                  validator: (value) => null,
                  onChanged: (value) {
                    searchPlaces(value);
                  }),
              suggestions != []
                  ? Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Card(
                        child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: suggestions.length,
                            itemBuilder: (context, index) {
                              return ListTile(
                                  onTap: () {
                                    setState(() {
                                      pickedLocation = suggestions[index];
                                      locationTextEditingController.text =
                                          suggestions[index].locationText;
                                      suggestions = [];
                                    });
                                  },
                                  title: Text(suggestions[index].locationText,
                                      style: Theme.of(context)
                                          .textTheme
                                          .bodyMedium));
                            }),
                      ),
                    )
                  : const Gap(h: 0),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            const CircleIconButton(
                                child: Icon(Icons.location_on,
                                    color: primaryColor, size: 35)),
                            Gap(w: 5.w),
                            GestureDetector(
                              onTap: () async {
                                final LocationEntity result =
                                    await Navigator.push(
                                  context,
                                  MaterialPageRoute(builder: (context) {
                                    return MapPicker(
                                      locationNameController:
                                          locationTextEditingController,
                                      // initialLocation:
                                      //     const LatLng(31.9497, 35.9320),
                                    );
                                  }),
                                );
                                setState(() {
                                  pickedLocation = LocationEntity(
                                      locationText: result.locationText,
                                      latitude: result.latitude,
                                      longitude: result.longitude);
                                });
                              },
                              child: Text(
                                  AppLocalizations.of(context)!.select_on_map,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontFamily: "Plus Jakarta Sans")),
                            )
                          ],
                        ),
                      ),
                      Gap(h: 2.h),
                      if (!widget.dropOff)
                        InkWell(
                          onTap: () {
                            getCurrentLocation();
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              const CircleIconButton(
                                  child: Icon(Icons.gps_fixed,
                                      color: primaryColor, size: 35)),
                              Gap(w: 5.w),
                              Text(
                                  AppLocalizations.of(context)!
                                      .add_current_location,
                                  style: Theme.of(context)
                                      .textTheme
                                      .bodyMedium!
                                      .copyWith(
                                          fontWeight: FontWeight.bold,
                                          fontFamily: "Plus Jakarta Sans"))
                            ],
                          ),
                        )
                    ],
                  ),
                ],
              ),
              Gap(
                h: 2.h,
              ),
              Divider(
                indent: 4.w,
                endIndent: 4.w,
                color: black.withOpacity(0.1),
              ),
              Gap(
                h: 1.h,
              ),
              Padding(
                padding: EdgeInsets.only(left: 4.w),
                child: Align(
                    alignment: Alignment.centerLeft,
                    child: Text(AppLocalizations.of(context)!.recent_rides,
                        style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                            fontSize: 20.sp, fontFamily: "Plus Jakarta Sans"))),
              ),
              ListView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: locations.length,
                  itemBuilder: (BuildContext context, int index) {
                    return Container(
                      color: white,
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: InkWell(
                          onTap: () {
                            setState(() {
                              pickedLocation = locations[index];
                              locationTextEditingController.text =
                                  pickedLocation!.locationText;
                            });
                          },
                          child: Row(
                            children: <Widget>[
                              CircleAvatar(
                                backgroundColor: primaryColor.withOpacity(0.8),
                                radius: 10.0,
                              ),
                              const SizedBox(width: 20.0),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Text(
                                    locations[index].locationText,
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodyMedium!
                                        .copyWith(
                                            fontWeight: FontWeight.bold,
                                            fontFamily: "Plus Jakarta Sans"),
                                  ),
                                  Text(
                                    locations[index].streetName ?? "",
                                    style: Theme.of(context)
                                        .textTheme
                                        .bodySmall!
                                        .copyWith(
                                            fontFamily: "Plus Jakarta Sans"),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  }),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 10),
        child: CustomButton(
            buttonText: AppLocalizations.of(context)!.continue_,
            borderRadius: 12,
            onPressed: () {
              if (pickedLocation != null) {
                if (widget.dropOff) {
                  context.push(path.pickUpAdded, extra: {
                    "source": widget.source,
                    "destination": pickedLocation,
                  });
                } else {
                  context.push(path.pickUpAdded, extra: {
                    "source": pickedLocation,
                    "destination": widget.destination,
                  });
                }
              }
            }),
      ),
    );
  }
}
*/
