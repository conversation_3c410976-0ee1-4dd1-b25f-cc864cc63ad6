import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/custom_snack_bar.dart';
import 'package:yalla_gai_driver/features/booking/domain/custom_formatter.dart';
import 'package:yalla_gai_driver/features/booking/domain/validations/card_validations.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/cubit/payment_cubit.dart';

import '../../../../core/shared_widgets/back_button.dart';
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/custom_textfield.dart';
import '../../../../core/shared_widgets/dialog_with_image_builder.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../widgets/custom_drawer.dart';

class AddCard extends StatefulWidget {
  const AddCard({super.key});

  @override
  State<AddCard> createState() => _AddCardState();
}

class _AddCardState extends State<AddCard> {
  final formKey = GlobalKey<FormState>();
  final cardNameTextEditingController = TextEditingController();
  final cardNumberTextEditingController = TextEditingController();
  final cardExpiryTextEditingController = TextEditingController();
  final cardCVVTextEditingController = TextEditingController();
  Map<String, dynamic> card = {"cardName": "", "cardNumber": "", "cardExpiry": DateTime, "cardCVV": ""};

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      isLoading: context.watch<PaymentCubit>().state is Paymentloading,
      backgroundColor: white,
      drawerScrimColor: Colors.transparent,
      drawer: const CustomDrawer(),
      body: BlocListener<PaymentCubit, PaymentState>(
        listener: (context, state) {
          if (state is CardAddedState) {
            cardCVVTextEditingController.clear();
            cardExpiryTextEditingController.clear();
            cardNameTextEditingController.clear();
            cardNumberTextEditingController.clear();

            dialogWithImageBuilder(
              context: context,
              image: cardAdded,
              successText: AppLocalizations.of(context)!.congrats_youve_successfully_added_your_card,
              buttonText: AppLocalizations.of(context)!.continue_,
              popBackLevel: 1,
            );
            Timer.run(() => formKey.currentState!.reset());
          }
        },
        child: Container(
          padding: const EdgeInsets.fromLTRB(0, 20, 0, 0),
          height: double.infinity,
          width: double.infinity,
          child: SingleChildScrollView(
            child: SafeArea(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: EdgeInsets.symmetric(vertical: 1.h, horizontal: 3.w),
                    child: Stack(
                      children: [
                        InkWell(
                          child: Align(
                            alignment: Alignment.centerLeft,
                            child: SvgBackButton(
                              onTap: () => context.pop(),
                              svgImage: SvgPicture.asset(
                                width: 36,
                                height: 36,
                                backButton,
                              ),
                            ),
                          ),
                        ),
                        Align(
                          alignment: Alignment.center,
                          child: Text(
                            AppLocalizations.of(context)!.add_new_card,
                            style: Theme.of(context)
                                .textTheme
                                .displayLarge!
                                .copyWith(color: black, fontFamily: "Plus Jakarta Sans"),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 30.0),
                  Form(
                    key: formKey,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        SvgPicture.asset(width: 90.w, cardExample),
                        const Gap(),
                        Divider(
                          height: 5,
                          thickness: 1,
                          indent: 5.w,
                          endIndent: 5.w,
                          color: black.withOpacity(0.3),
                        ),
                        const Gap(),
                        CustomTextField(
                          width: 80.w,
                          borderRadius: 4,
                          labelText: AppLocalizations.of(context)!.card_name,
                          labelColor: primaryColor,
                          textEditingController: cardNameTextEditingController,
                          validator: CardValidations.of(context).cardName,
                        ),
                        const Gap(),
                        CustomTextField(
                          width: 80.w,
                          keyboardType: TextInputType.number,
                          borderRadius: 4,
                          labelText: AppLocalizations.of(context)!.card_number,
                          labelColor: primaryColor,
                          textEditingController: cardNumberTextEditingController,
                          validator: CardValidations.of(context).cardNumber,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly,
                            const CustomInputFormatter(forEvery: 4, seperator: "  "),
                            LengthLimitingTextInputFormatter(22),
                          ],
                        ),
                        const Gap(),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            CustomTextField(
                              width: 40.w,
                              keyboardType: TextInputType.number,
                              borderRadius: 4,
                              labelText: AppLocalizations.of(context)!.expiry_date,
                              labelColor: primaryColor,
                              textEditingController: cardExpiryTextEditingController,
                              validator: CardValidations.of(context).expDate,
                              inputFormatters: [
                                const CustomInputFormatter(),
                                LengthLimitingTextInputFormatter(5),
                              ],
                            ),
                            Gap(w: 10.w),
                            CustomTextField(
                              width: 30.w,
                              keyboardType: TextInputType.number,
                              borderRadius: 4,
                              labelText: AppLocalizations.of(context)!.cvv,
                              labelColor: primaryColor,
                              textEditingController: cardCVVTextEditingController,
                              validator: CardValidations.of(context).cvv,
                              inputFormatters: [
                                FilteringTextInputFormatter.digitsOnly,
                                LengthLimitingTextInputFormatter(4),
                              ],
                            ),
                          ],
                        ),
                        Gap(h: 4.h),
                        Align(
                          alignment: Alignment.center,
                          child: BlocConsumer<PaymentCubit, PaymentState>(
                            listener: (context, state) {
                              if (state is CardAddedError) {
                                showCustomSnackbar(context, state.message);
                              }
                            },
                            builder: (context, state) {
                              return CustomButton(
                                isLoading: state is Paymentloading,
                                buttonText: AppLocalizations.of(context)!.add_card,
                                borderRadius: 10,
                                height: 6.h,
                                width: 45.w,
                                onPressed: () {
                                  if (formKey.currentState!.validate()) {
                                    context.read<PaymentCubit>().addCards(
                                          expYear: cardExpiryTextEditingController.text.split('/')[1],
                                          cvv: cardCVVTextEditingController.text,
                                          expMonth: cardExpiryTextEditingController.text.split('/')[0],
                                          cardNumber: cardNumberTextEditingController.text,
                                          cardName: cardNameTextEditingController.text,
                                        );
                                  }
                                },
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

class CardExpirationDateFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    final newText = newValue.text;
    if (newText.length > 2 && !newText.contains('/')) {
      return TextEditingValue(
        text: '${newText.substring(0, 2)}/',
        selection: const TextSelection.collapsed(offset: 3),
      );
    }
    return newValue;
  }
}
