/*
import "package:flutter/material.dart";
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';

import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/utils/uti.dart';
import '../widgets/vertical_dotted_line.dart';

class CarDetail extends StatefulWidget {
  const CarDetail({super.key});

  @override
  CarDetailState createState() => CarDetailState();
}

class CarDetailState extends State<CarDetail> {
  bool showDetail = false;

  Widget details() {
    final driver = context.watch<AuthBloc>().user.user;
    return Container(
        padding: const EdgeInsets.fromLTRB(5, 20, 5, 20),
        decoration: BoxDecoration(
            color: Colors.white,
            border: Border.all(
              width: 1.0,
              color: grey,
            ),
            borderRadius: BorderRadius.circular(20)),
        child: Row(
          children: [
            Expanded(
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      AppLocalizations.of(context)!.price,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                    ),
                    gap(40),
                    Text(
                      AppLocalizations.of(context)!.seats,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                    ),
                    gap(40),
                    Text(
                      AppLocalizations.of(context)!.accepted,
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                    ),
                  ],
                ),
              ),
            ),
            Container(
              height: 125,
              width: 0.4, // Set the width of the vertical divider
              color: grey, // Set the color of the vertical divider
            ),
            Expanded(
              child: Center(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')} 50,00",
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                    ),
                    gap(40),
                    Text(
                      "5 ${AppLocalizations.of(context)!.seats}",
                      style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                    ),
                    gap(40),
                    const Icon(Icons.payment_rounded)
                  ],
                ),
              ),
            ),
          ],
        ));
  }

  Widget tarrif_detail({driver}) {
    return Container(
        padding: const EdgeInsets.fromLTRB(5, 20, 5, 20),
        decoration: const BoxDecoration(
            color: Colors.white,
            border: Border(
              top: BorderSide(width: 1.0, color: grey),
              left: BorderSide(width: 1.0, color: grey),
              right: BorderSide(width: 1.0, color: grey),
              bottom: BorderSide(width: 1.0, color: grey),
            ),
            borderRadius: BorderRadius.all(Radius.circular(20))),
        child: Column(children: [
          title(AppLocalizations.of(context)!.yalla_auto),
          gap(25),
          Row(
            children: [
              Expanded(
                child: Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context)!.minimum_fare,
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                      ),
                      gap(40),
                      Text(
                        AppLocalizations.of(context)!.maximum_fare,
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                      ),
                      gap(40),
                      Text(
                        AppLocalizations.of(context)!.flag_down_fee,
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                height: 150,
                width: 0.4, // Set the width of the vertical divider
                color: grey, // Set the color of the vertical divider
              ),
              Expanded(
                child: Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}10.00",
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                      ),
                      gap(40),
                      Text(
                        "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}50.00",
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                      ),
                      gap(40),
                      Text(
                        "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}5.00",
                        style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          gap(40),
          Text(
              "${AppLocalizations.of(context)!.avg_fdf}${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}5/${AppLocalizations.of(context)!.min} ${AppLocalizations.of(context)!.or} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}10/${AppLocalizations.of(context)!.km}",
              style: TextStyle(fontSize: 18, color: grey[600])),
          gap(30),
          Row(
            children: [
              Expanded(
                  child: Center(
                child:
                    Text(AppLocalizations.of(context)!.including_tax, style: TextStyle(fontSize: 18, color: grey[600])),
              )),
              Container(
                height: 30,
                width: 0.4, // Set the width of the vertical divider
                color: grey, // Set the color of the vertical divider
              ),
              Expanded(
                  child: Center(
                child: Text("${AppLocalizations.of(context)!.booking_fee} 9.66%", style: const TextStyle(fontSize: 18)),
              )),
            ],
          )
        ]));
  }

  final weekDays = ["Tuesday", "Wednesday", "Thursday", "Friday", "Saturday", "Sunday", "Monday"];

  Widget schedule(String timeOfDay, context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Stack(
      children: [
        Container(
            margin: const EdgeInsets.only(bottom: 35),
            padding: const EdgeInsets.fromLTRB(15, 15, 15, 15),
            decoration: BoxDecoration(
                color: Colors.white,
                border: Border.all(width: 1.0, color: grey),
                borderRadius: BorderRadius.circular(20)),
            child: Column(children: [
              Row(
                children: <Widget>[
                  Expanded(
                    flex: 5,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(timeOfDay, style: const TextStyle(fontSize: 22, fontWeight: FontWeight.w500)),
                        gap(3),
                        const Divider(
                          color: grey,
                          thickness: 0.4,
                        ),
                        gap(3),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(AppLocalizations.of(context)!.thursday_friday,
                                style: TextStyle(fontSize: 16, color: grey[600])),
                            Text(AppLocalizations.of(context)!.saturday_monday,
                                style: TextStyle(fontSize: 16, color: grey[600])),
                          ],
                        ),
                        gap(10),
                      ],
                    ),
                  ),
                  const SizedBox(
                    width: 8,
                  ),
                  // const Expanded(
                  //   flex: 2,
                  //   child: Text(
                  //     "From",
                  //     style:
                  //         TextStyle(fontSize: 20, fontWeight: FontWeight.w500),
                  //   ),
                  // ),
                  Container(
                    height: 120,
                    width: 0.4, // Set the width of the vertical divider
                    color: grey, // Set the color of the vertical divider
                  ),
                  const SizedBox(
                    width: 15,
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Row(
                          children: [
                            const Icon(Icons.timer),
                            const SizedBox(
                              width: 4,
                            ),
                            Text("7:30", style: Theme.of(context).textTheme.bodyMedium),
                          ],
                        ),
                        const VerticalDottedLine(height: 30),
                        Row(
                          children: [
                            const Icon(Icons.timer),
                            const SizedBox(width: 4),
                            Text("10:30", style: Theme.of(context).textTheme.bodyMedium)
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              showDetail
                  ? Column(children: [
                      const Divider(
                        color: grey,
                        thickness: 0.4,
                      ),
                      gap(20),
                      Row(
                        children: [
                          Expanded(
                            child: Center(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)!.minimum_fare,
                                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                                  ),
                                  gap(30),
                                  Text(
                                    AppLocalizations.of(context)!.flag_down_fee,
                                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Container(
                            height: 80,
                            width: 0.4, // Set the width of the vertical divider
                            color: Colors.grey, // Set the color of the vertical divider
                          ),
                          Expanded(
                            child: Center(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}10.00",
                                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                                  ),
                                  gap(30),
                                  Text(
                                    "${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver.country?.arabicCurrency ?? '')}50.00",
                                    style: const TextStyle(fontSize: 18, fontWeight: FontWeight.w400),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                      gap(30),
                      Text(
                          "${AppLocalizations.of(context)!.avg_fdf}${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}5/${AppLocalizations.of(context)!.min} ${AppLocalizations.of(context)!.or} ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}10/${AppLocalizations.of(context)!.km}",
                          style: TextStyle(fontSize: 18, color: grey[600])),
                      gap(40)
                    ])
                  : Container(),
            ])),
        Positioned(
            bottom: 10,
            left: 125,
            child: ElevatedButton(
              onPressed: () {
                setState(() {
                  showDetail = !showDetail;
                });
              },
              style: TextButton.styleFrom(
                  backgroundColor: Colors.white,
                  foregroundColor: Colors.black,
                  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
                  side: const BorderSide(color: Colors.green, width: 2),
                  padding: const EdgeInsets.fromLTRB(30, 10, 30, 10)),
              child: Text(showDetail ? AppLocalizations.of(context)!.less : AppLocalizations.of(context)!.more),
            )),
      ],
    );
  }

  Widget gap(double h) {
    return SizedBox(height: h);
  }

  Widget title(String t) {
    return Text(
      t,
      style: const TextStyle(
        fontWeight: FontWeight.w700,
        fontFamily: "Plus Jakarta Sans",
        fontSize: 28,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: title(AppLocalizations.of(context)!.auto),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.fromLTRB(20, 0, 20, 20),
        child: SingleChildScrollView(
          child: Column(
            children: <Widget>[
              Image.asset(
                priorityBigCar,
              ),
              gap(40),
              title(AppLocalizations.of(context)!.auto),
              gap(20),
              const Divider(
                color: grey,
                thickness: 0.4,
              ),
              gap(20),
              details(),
              gap(40),
              Text(
                AppLocalizations.of(context)!.tarrifs,
                style: const TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 28,
                ),
              ),
              Container(
                height: 1,
                width: 80,
                color: primaryColor,
              ),
              gap(40),
              tarrif_detail(),
              gap(40),
              schedule(AppLocalizations.of(context)!.morning, context),
            ],
          ),
        ),
      ),
    );
  }
}
*/
