import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:geocoding/geocoding.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:yalla_gai_driver/core/routes/router_config.dart';
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/core/utils/get_polyline_points.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/utils/images.dart';
import '../../domain/entity/location_entity.dart';

class MapPicker extends StatefulWidget {
  final TextEditingController locationNameController;

  const MapPicker({super.key, required this.locationNameController});

  @override
  MapPickerState createState() => MapPickerState();
}

class MapPickerState extends State<MapPicker> {
  final Set<Marker> _markers = {};
  BitmapDescriptor? icon;
  LatLng? currentLocation;
  final Completer<GoogleMapController> _controller = Completer();

  @override
  initState() {
    super.initState();
    setMarkerImage();
  }

  setMarkerImage() async {
    await getBitmapDescriptor(locationPinMarker).then((value) => icon = value);
    setState(() {});
  }

  LatLng? _pickedLocation;

  void setCurrentLocation() async {
    Position position = await context.read<LocationService>().getCurrentLocation();
    if (_controller.isCompleted) {
      final GoogleMapController controller = await _controller.future;
      controller.animateCamera(CameraUpdate.newLatLng(
        LatLng(position.latitude, position.longitude),
      ));
    }
  }

  void _selectLocation(LatLng position) {
    setState(() {
      _pickedLocation = position;
      _markers.clear();
      _markers.add(
        Marker(
          markerId: const MarkerId('m1'),
          icon: icon ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
          position: position,
        ),
      );
    });
  }

  void _submitLocation(context) async {
    if (_pickedLocation == null) {
      return;
    }
    try {
      List<Placemark> placemarks =
          await placemarkFromCoordinates(_pickedLocation!.latitude, _pickedLocation!.longitude);
      setState(() {
        widget.locationNameController.text = placemarks[0].street!;
      });
      Navigator.of(context).pop(LocationEntity(
          locationText: placemarks[0].street!,
          latitude: _pickedLocation!.latitude,
          longitude: _pickedLocation!.longitude));
    } catch (e) {
      widget.locationNameController.text = AppLocalizations.of(context)!.unknown_street;
      Navigator.of(context).pop(LocationEntity(
          locationText: AppLocalizations.of(context)!.unknown_street,
          latitude: _pickedLocation!.latitude,
          longitude: _pickedLocation!.longitude));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: GoogleMap(
          myLocationButtonEnabled: true,
          myLocationEnabled: true,
          onMapCreated: (controller) {
            _controller.complete(controller);
            setCurrentLocation();
          },
          initialCameraPosition: const CameraPosition(
            target: LatLng(31.9497, 35.9320),
            zoom: 16,
          ),
          onTap: _selectLocation,
          markers: _markers,
        ),
      ),
      floatingActionButton: Align(
        alignment: Alignment.bottomCenter,
        child: Padding(
          padding: const EdgeInsets.all(50),
          child: CustomButton(
              width: MediaQuery.of(context).size.width - MediaQuery.of(context).size.width / 2.5,
              borderRadius: 4,
              buttonText: AppLocalizations.of(context)!.set_drop_point,
              onPressed: () => _submitLocation(context)),
        ),
      ),
    );
  }
}
