/*
import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/get_polyline_points.dart';
import 'package:yalla_gai_driver/environment.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import '../../domain/entity/location_entity.dart';
import '../widgets/custom_drawer.dart';

class ConfirmBooking extends StatefulWidget {
  const ConfirmBooking({super.key, required this.source, required this.destination});

  final LocationEntity source;
  final LocationEntity destination;

  @override
  State<ConfirmBooking> createState() => _ConfirmBookingState();
}

class _ConfirmBookingState extends State<ConfirmBooking> {
  @override
  initState() {
    super.initState();
    getPolylineBetweenTwoPoints(LatLng(widget.source.latitude, widget.source.longitude),
        LatLng(widget.destination.latitude, widget.destination.longitude));
    setMarkerImage();
  }

  double ridePrice = 50.0;
  List<LatLng> routePoints = [];
  PolylinePoints polylinePoints = PolylinePoints();
  BitmapDescriptor? pointAMarker;
  BitmapDescriptor? pointBMarker;

  Future<void> getPolylineBetweenTwoPoints(LatLng source, LatLng destination) async {
    PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(AppEnvironment.googleMapsApiKey,
        PointLatLng(source.latitude, source.longitude), PointLatLng(destination.latitude, destination.longitude));
    if (result.points.isNotEmpty) {
      for (var point in result.points) {
        setState(() {
          routePoints.add(LatLng(point.latitude, point.longitude));
        });
      }
    }
  }

  void setMarkerImage() async {
    await Future.wait([
      getBitmapDescriptor(locationAMarker).then((value) => pointAMarker = value),
      getBitmapDescriptor(locationBMarker).then((value) => pointBMarker = value),
    ]);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    return Scaffold(
        backgroundColor: white,
        drawerScrimColor: Colors.transparent,
        drawer: const CustomDrawer(),
        body: Stack(
          children: [
            GoogleMap(
              initialCameraPosition: CameraPosition(
                target: LatLng(widget.source.latitude, widget.source.longitude),
                zoom: 16.5,
              ),
              markers: {
                Marker(
                  markerId: const MarkerId("Point A confirm"),
                  position: LatLng(widget.source.latitude, widget.source.longitude),
                  icon: pointAMarker ??
                      BitmapDescriptor.defaultMarkerWithHue(
                        BitmapDescriptor.hueGreen,
                      ),
                ),
                Marker(
                  markerId: const MarkerId("Point B confirm"),
                  position: LatLng(widget.destination.latitude, widget.destination.longitude),
                  icon: pointBMarker ??
                      BitmapDescriptor.defaultMarkerWithHue(
                        BitmapDescriptor.hueGreen,
                      ),
                ),
              },
              polylines: {
                Polyline(
                    polylineId: const PolylineId('route confirm'), color: Colors.black, width: 5, points: routePoints),
              },
            ),
            Align(
              alignment: Alignment.topCenter,
              child: Padding(
                padding: const EdgeInsets.only(top: 50),
                child: Column(
                  children: [
                    Text(widget.source.locationText,
                        style: Theme.of(context)
                            .textTheme
                            .displayMedium!
                            .copyWith(color: black, fontWeight: FontWeight.bold, fontFamily: "Plus Jakarta Sans")),
                    Gap(h: 2.h),
                    Text(widget.destination.locationText,
                        style: Theme.of(context)
                            .textTheme
                            .displayMedium!
                            .copyWith(color: black, fontWeight: FontWeight.bold, fontFamily: "Plus Jakarta Sans"))
                  ],
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 30, top: 30),
              child: Align(
                  alignment: Alignment.bottomCenter,
                  child: CustomButton(
                      buttonText:
                          "${AppLocalizations.of(context)!.confirm_order} $ridePrice ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                      borderRadius: 12,
                      height: 7.h,
                      onPressed: () {
                        context.push(
                          path.findDriver,
                          extra: {"destination": widget.destination, "source": widget.source},
                        );
                      })),
            )
          ],
        ));
  }
}
*/
