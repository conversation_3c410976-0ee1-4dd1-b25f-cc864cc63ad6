import 'dart:async';
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/booking/bloc/ride_request/ride_request_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';

import '../../../../core/utils/colors.dart';
import '../../../../core/utils/get_polyline_points.dart';
import '../../../../core/utils/images.dart';
import '../../model/ride_request.dart';
import '../widgets/accept_trip_bottom_sheet.dart';
import '../widgets/custom_drawer.dart';
import '../widgets/home_drawer_holder.dart';

class AcceptRide extends StatelessWidget {
  // final RideRequest rideRequest;
  // final RequestAccept requestAccept;

  // AcceptRide({super.key, required this.request});
  const AcceptRide({
    // required this.rideRequest,
    // required this.requestAccept,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final watchRide = context.watch<RideRequestCubit>();
    if (watchRide.requestData == null) {
      return const SizedBox.shrink();
    }
    return PopScope(
      canPop: false,
      child: AppScaffold(
        isLoading: context.watch<TripBloc>().state is ChangeTripStatusLoading ||
            context.watch<TripBloc>().state is TripRejecting,
        backgroundColor: white,
        drawerScrimColor: Colors.transparent,
        drawer: const CustomDrawer(),
        appBar: AppBar(
          toolbarHeight: 64,
          leading: const HomeDrawerHolder(),
        ),
        body: AcceptRideScaffoldBody(
          request: watchRide.requestData!,
        ),
      ),
    );
  }
}

class AcceptRideScaffoldBody extends StatefulWidget {
  final ComingRideRequest request;

  const AcceptRideScaffoldBody({super.key, required this.request});

  @override
  State<AcceptRideScaffoldBody> createState() => _AcceptRideScaffoldBodyState();
}

class _AcceptRideScaffoldBodyState extends State<AcceptRideScaffoldBody> with LocationMixin {
  List<LatLng> routePoints = [];
  /*PolylinePoints polylinePoints = PolylinePoints();*/
  BitmapDescriptor sourceIcon = BitmapDescriptor.defaultMarker;
  BitmapDescriptor pickUpIcon = BitmapDescriptor.defaultMarker;
  BitmapDescriptor destinationIcon = BitmapDescriptor.defaultMarker;
  GoogleMapController? _mapController;
  final double _bearingAngle = 0;

  @override
  initState() {
    super.initState();
    setSourceAndDestinationIcons();
    debugPrint("AcceptRideScaffoldBody initState ==== ${widget.request.toJson()}");
    /*getPolyline();*/
    WakelockPlus.enable();
  }

  @override
  void didChangeDependencies() {
    _highlightDriverPosition();
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    WakelockPlus.disable();

    super.dispose();
  }

  double? _lastDirection;

  @override
  void onLocationUpdated(Position position) {
    super.onLocationUpdated(position);

    if (position.heading > 0) {
      _lastDirection = position.heading;
    }

    BlocProvider.of<TripBloc>(context).add(
      TripUpdateLocation(
        lat: position.latitude.toString(),
        lng: position.longitude.toString(),
        direction: (_lastDirection ?? position.heading).toString(),
        bygoogle: false,
      ),
    );
    CustomSocket().updateLocation(position.latitude, position.longitude);
    _onDriverLocationChanged(LatLng(position.latitude, position.longitude));
  }

  /*getPolyline() async {
    final currentPosition = this.currentPosition ?? await context.read<LocationService>().getCurrentLocation();
    await _updatePolyline(LatLng(currentPosition.latitude, currentPosition.longitude));
    _highlightDriverPosition();
  }

  Future<void> _updatePolyline(LatLng currentPosition) async {
    final points = await getPolylineBetweenPoints(
      currentPosition,
      LatLng(widget.request.pickupLatitude, widget.request.pickupLongitude),
    );
    setState(() {
      routePoints = points;
      _highlightDriverPosition();
    });
  }*/

  /*_simulateNavigationTesting() {
    if (routePoints.length == 1) {
      return;
    }
    final nextLatLng = routePoints[1];
    Timer(
      const Duration(seconds: 2),
      () async {
        await _onDriverLocationChanged(nextLatLng);
        _simulateNavigationTesting();
      },
    );
  }*/

  Future<void> _onDriverLocationChanged(LatLng latLng) async {
    _highlightDriverPosition();

    /*setState(() {
      routePoints[0] = latLng;
    });

    if (routePoints.length > 1) {
      final currentPoint = Point(latLng.latitude, latLng.longitude);
      final nextPoint = Point(routePoints[1].latitude, routePoints[1].longitude);
      double distance = SphericalUtils.computeDistanceBetween(currentPoint, nextPoint).toDouble();

      switch (distance) {
        case <= 5:
          routePoints.removeAt(0);
          _highlightDriverPosition();
          break;
        case >= 100:
          _updatePolyline(latLng);
          break;
        default:
          _highlightDriverPosition();
      }
    }*/
  }

  Future<void> _highlightDriverPosition() async {
    assert(currentPosition != null);

    final latlng = LatLng(currentPosition!.latitude, currentPosition!.longitude);
    // if (routePoints.length > 1) {
    //   double angle = calculateBearing(latlng, routePoints[1]);
    //   _bearingAngle = angle;
    // }

    await _mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: latlng,
          bearing: currentPosition!.heading,
          zoom: 19,
        ),
      ),
    );
  }

  /*void _centerView(GoogleMapController controller) async {
    driverPosition ??= await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high);
    LatLngBounds bounds;
    Geolocator.getPositionStream().listen((newPosition) {
      driverPosition = newPosition;
      controller.animateCamera(CameraUpdate.newLatLng(LatLng(newPosition.latitude, newPosition.longitude)));
      if (driverPosition!.latitude > widget.request.pickupLatitude) {
        bounds = LatLngBounds(
            southwest: LatLng(widget.request.pickupLatitude, widget.request.pickupLongitude),
            northeast: LatLng(driverPosition!.latitude, driverPosition!.longitude));
      } else {
        bounds = LatLngBounds(
          southwest: LatLng(driverPosition!.latitude, driverPosition!.longitude),
          northeast: LatLng(widget.request.pickupLatitude, widget.request.pickupLongitude),
        );
      }

      CameraUpdate cameraUpdate = CameraUpdate.newLatLngBounds(bounds, 100);
      controller.animateCamera(cameraUpdate);
      getPolyline();
      setState(() {});
    });
    if (driverPosition!.latitude > widget.request.pickupLatitude) {
      bounds = LatLngBounds(
          southwest: LatLng(widget.request.pickupLatitude, widget.request.pickupLongitude),
          northeast: LatLng(driverPosition!.latitude, driverPosition!.longitude));
    } else {
      bounds = LatLngBounds(
        southwest: LatLng(driverPosition!.latitude, driverPosition!.longitude),
        northeast: LatLng(widget.request.pickupLatitude, widget.request.pickupLongitude),
      );
    }

    CameraUpdate cameraUpdate = CameraUpdate.newLatLngBounds(bounds, 100);
    controller.animateCamera(cameraUpdate);
  }*/

  Future<Uint8List?> getAssetBytes(String img) async {
    ByteData data = await rootBundle.load(img);
    Codec codec = await instantiateImageCodec(data.buffer.asUint8List(), targetHeight: 100);
    FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ImageByteFormat.png))?.buffer.asUint8List();
  }

  void setSourceAndDestinationIcons() async {
    await Future.wait([
      getBitmapDescriptor(carMapMarker).then((value) => sourceIcon = value),
      getBitmapDescriptor(userMapMarker).then((value) => pickUpIcon = value),
      getBitmapDescriptor(destMapMarker).then((value) => destinationIcon = value),
    ]);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final padding = MediaQuery.paddingOf(context);

    LatLng pickupLocation = LatLng(widget.request.pickupLatitude, widget.request.pickupLongitude);
    LatLng? destinationLocation;
    if (widget.request.destinationLatitude != null && widget.request.destinationLongitude != null) {
      destinationLocation = LatLng(widget.request.destinationLatitude!, widget.request.destinationLongitude!);
    }

    return Column(
      children: [
        Expanded(
          child: Stack(
            children: [
              GoogleMap(
                myLocationButtonEnabled: false,
                padding: EdgeInsets.fromLTRB(16, padding.top + 5.w, 16, padding.bottom + 16),
                initialCameraPosition: CameraPosition(
                  target: destinationLocation != null
                      ? getCenterPoint(pickupLocation, destinationLocation)
                      : pickupLocation,
                  zoom: 20,
                ),
                onMapCreated: (controller) {
                  _mapController = controller;
                  centerMap(pickupLocation: pickupLocation, dropoffLocation: destinationLocation);
                },
                markers: {
                  if (currentPosition != null)
                    Marker(
                      markerId: const MarkerId("driver_source"),
                      icon: sourceIcon,
                      rotation: currentPosition!.heading,
                      position: LatLng(currentPosition!.latitude, currentPosition!.longitude),
                    ),
                  Marker(
                    markerId: const MarkerId("user_pickup"),
                    icon: pickUpIcon,
                    position: pickupLocation,
                  ),
                  if(destinationLocation!= null)
                  Marker(
                    markerId: const MarkerId("user_destination"),
                    icon: destinationIcon,
                    position: destinationLocation,
                  ),
                },
                polylines: {
                  Polyline(polylineId: const PolylineId('route'), color: Colors.black, width: 5, points: routePoints),
                },
              ),
            ],
          ),
        ),
        AcceptTripBottomSheet(
          rideRequest: widget.request,
        ),
      ],
    );
  }

  void centerMap({required LatLng pickupLocation, LatLng? dropoffLocation}) {
    // LatLng centerPoint = getCenterPoint(pickupLocation, dropoffLocation);
    if (dropoffLocation == null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(pickupLocation, 14),
      );
    } else {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(computeBounds([pickupLocation, dropoffLocation]), 20),
      );
    }
  }
}
