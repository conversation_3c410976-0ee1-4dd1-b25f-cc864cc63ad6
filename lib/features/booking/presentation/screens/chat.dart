import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_svg/svg.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/features/booking/bloc/chat/chats_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';

import '../../../../core/shared_widgets/back_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../widgets/message.dart';

class Chat extends StatefulWidget {
  const Chat({super.key});

  @override
  State<Chat> createState() => _ChatState();
}

class _ChatState extends State<Chat> {
  SharedPreferences? preferences;

  @override
  void initState() {
    init();
    super.initState();
  }

  init() async {
    preferences = await SharedPreferences.getInstance();
    context.read<ChatsCubit>().clearUnreadCount();
    setState(() {});
  }

  final TextEditingController controller = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final watchChats = context.watch<ChatsCubit>();
    final messages = context.watch<ChatsCubit>().chats;
    final acceptedRide = context.watch<TripBloc>().requestAccept;
    final size = MediaQuery.sizeOf(context);
    return AppScaffold(
        body: Column(
          children: [
            Stack(
              children: [
                SvgPicture.asset(
                  width: MediaQuery.sizeOf(context).width,
                  appBarShape,
                ),
                Positioned(
                    top: 6.h,
                    left: 3.w,
                    child: SvgBackButton(
                      svgImage: SvgPicture.asset(
                        width: 36,
                        height: 36,
                        backButton,
                      ),
                    )),
                Align(
                  alignment: Alignment.center,
                  child: Column(
                    children: [
                      Gap(
                        h: 7.h,
                      ),
                      Container(
                          width: size.width * 0.25,
                          height: size.width * 0.25,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: black.withOpacity(0.2),
                              width: 2,
                            ),
                          ),
                          child: acceptedRide.endUser?.profileImage != null
                              ? CachedNetworkImage(
                                  width: size.width * 0.25,
                                  height: size.width * 0.25,
                                  imageUrl: acceptedRide.endUser?.profileImage ?? "",
                                  fit: BoxFit.fill,
                                  imageBuilder: (context, imageProvider) => Container(
                                    width: size.width * 0.25,
                                    height: size.width * 0.25,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
                                    ),
                                  ),
                                  placeholder: (context, url) => const CircularProgressIndicator(
                                    valueColor: AlwaysStoppedAnimation(primaryColor),
                                  ),
                                  errorWidget: (context, url, error) => const Icon(
                                    Icons.person,
                                    size: 50,
                                  ),
                                )
                              : CircleAvatar(
                                  radius: 58,
                                  backgroundColor: Colors.green.withOpacity(.5),
                                  child: const Icon(
                                    Icons.person,
                                    color: Colors.white,
                                    size: 70,
                                  ))),
                      const Gap(),
                      Text(
                          Utils.appText(
                              context: context,
                              text: acceptedRide.endUser?.fullName ?? "",
                              arabicText: acceptedRide.endUser?.arabicFullName ?? ""),
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(fontWeight: FontWeight.w600)),
                    ],
                  ),
                )
              ],
            ),
            Expanded(
              child: BlocBuilder<ChatsCubit, ChatsState>(builder: (context, state) {
                if (state is ChatsLoadingState) {
                  return const Center(
                    child: CircularProgressIndicator(valueColor: AlwaysStoppedAnimation(primaryColor)),
                  );
                }
                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: messages.length,
                  reverse: true,
                  controller: watchChats.scrollController,
                  itemBuilder: (context, index) {
                    int msgIndex = messages.length - (index + 1);
                    return SizedBox(
                        width: MediaQuery.of(context).size.width * 0.7,
                        child: Message(
                            message: messages[msgIndex].message ?? "",
                            self: preferences?.getString("id") == messages[msgIndex].senderId));
                  },
                );
              }),
            ),
            Container(
              color: const Color(0xffc8c7cc).withOpacity(0.4),
              padding: const EdgeInsets.all(16.0),
              child: Row(
                children: <Widget>[
                  // InkWell(
                  //   child: SvgPicture.asset(
                  //     paperclip,
                  //     height: 50,
                  //   ),
                  //   onTap: () {},
                  // ),
                  Expanded(
                    child: TextFormField(
                      textInputAction: TextInputAction.send,
                      controller: context.watch<ChatsCubit>().chatController,
                      onFieldSubmitted: (value) {
                        final userId = context.read<TripBloc>().requestAccept.endUser?.id;
                        context.read<ChatsCubit>().sendChat(driverID: userId.toString());
                      },
                      decoration: InputDecoration(
                        hintText: AppLocalizations.of(context)!.type_a_message,
                        hintStyle: Theme.of(context)
                            .textTheme
                            .labelMedium!
                            .copyWith(color: Colors.black.withOpacity(0.2), fontWeight: FontWeight.w300),
                        border: UnderlineInputBorder(
                            borderSide: BorderSide(width: 1, color: Colors.black.withOpacity(0.2))),
                      ),
                    ),
                  ),
                  Gap(
                    w: 1.w,
                  ),
                  InkWell(
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: SvgPicture.asset(send),
                    ),
                    onTap: () {
                      final userId = context.read<TripBloc>().requestAccept.endUser?.id;
                      context.read<ChatsCubit>().sendChat(driverID: userId.toString());

                      print("--------------------------------------");
                    },
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}
