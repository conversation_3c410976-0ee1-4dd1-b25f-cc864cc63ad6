import 'dart:async';
import 'dart:developer' as logger;

import 'package:audioplayers/audioplayers.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
/*import 'package:flutter_polyline_points/flutter_polyline_points.dart' hide TravelMode;*/
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:google_maps_webservice/directions.dart' hide Polyline;
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/app_locale.dart';
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/request_bottom_sheet.dart';

import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/get_polyline_points.dart';
import '../../../../core/utils/images.dart';
import '../../domain/entity/ride_request.dart';
import '../widgets/custom_drawer.dart';

class NewRideRequest extends StatefulWidget {
  final RideRequest request;
  final int countdown;

  const NewRideRequest({super.key, required this.request, required this.countdown});

  @override
  State<NewRideRequest> createState() => _NewRideRequestState();
}

class _NewRideRequestState extends State<NewRideRequest> {
  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        backgroundColor: white,
        drawerScrimColor: Colors.transparent,
        drawer: const CustomDrawer(),
        body: ScaffoldBody(
          request: widget.request,
          countdown: widget.countdown,
        ),
      ),
    );
  }
}

class RingtonePlayer {}

class ScaffoldBody extends StatefulWidget {
  final RideRequest request;
  final int countdown;

  const ScaffoldBody({super.key, required this.request, required this.countdown});

  @override
  State<ScaffoldBody> createState() => _ScaffoldBodyState();
}

class _ScaffoldBodyState extends State<ScaffoldBody> with LocationMixin {
  late AudioPlayer audioPlayer;

  void playRingtone() async {
    audioPlayer = AudioPlayer();
    audioPlayer.setReleaseMode(ReleaseMode.loop);
    audioPlayer.play(
      AssetSource('sounds/ride_alert.mp3'),
      mode: PlayerMode.mediaPlayer,
    );
  }

  void stopRingtone() {
    audioPlayer.stop();
  }

  final Completer<GoogleMapController> _controller = Completer();
  List<LatLng> routePoints = [];
  /*PolylinePoints polylinePoints = PolylinePoints();*/
  GoogleMapController? _mapController;
  BitmapDescriptor sourceIcon = BitmapDescriptor.defaultMarker;
  BitmapDescriptor pickUpIcon = BitmapDescriptor.defaultMarker;
  BitmapDescriptor destinationIcon = BitmapDescriptor.defaultMarker;

  @override
  void dispose() {
    stopRingtone();
    super.dispose();
  }

  @override
  initState() {
    playRingtone();
    getCurrentLocation();
    setSourceAndDestinationIcons();
    super.initState();
  }

  /*void _centerView(GoogleMapController controller) async {
    driverPosition ??= await Geolocator.getCurrentPosition();
    LatLngBounds bounds;

    if (driverPosition!.latitude > widget.request.userPickUpLocation.latitude) {
      bounds = LatLngBounds(
          southwest: LatLng(widget.request.userPickUpLocation.latitude, widget.request.userPickUpLocation.longitude),
          northeast: LatLng(driverPosition!.latitude, driverPosition!.longitude));
    } else {
      bounds = LatLngBounds(
        southwest: LatLng(driverPosition!.latitude, driverPosition!.longitude),
        northeast: LatLng(widget.request.userPickUpLocation.latitude, widget.request.userPickUpLocation.longitude),
      );
    }

    CameraUpdate cameraUpdate = CameraUpdate.newLatLngBounds(bounds, 50);
    controller.animateCamera(cameraUpdate);
  }*/

  String distance = "...";
  String duration = "...";

  calculateDistance(LatLng latlng) async {
    try {
      final direction = await GoogleMapsDirections(apiKey: AppEnvironment.googleMapsApiKey).directions(
        Location(lat: latlng.latitude, lng: latlng.longitude),
        Location(lat: widget.request.userPickUpLocation.latitude, lng: widget.request.userPickUpLocation.longitude),
        travelMode: TravelMode.driving,
        language: context.read<AppLocale>().languageCode,
      );

      final route = direction.routes.firstOrNull;
      if (route != null) {
        final leg = route.legs.firstOrNull;
        if (leg != null) {
          distance = leg.distance.text;
          duration = leg.duration.text;
        }

        final points = decodeEncodedPolyline(route.overviewPolyline.points);
        setState(() {
          routePoints = points;
        });
      }

      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(
          computeBounds([
            latlng,
            LatLng(widget.request.userPickUpLocation.latitude, widget.request.userPickUpLocation.longitude),
          ]),
          48,
        ),
      );
    } catch (e) {
      logger.log("distance  werror$e");
    }
  }

  void setSourceAndDestinationIcons() async {
    await Future.wait([
      getBitmapDescriptor(carMapMarker).then((value) => sourceIcon = value),
      getBitmapDescriptor(userMapMarker).then((value) => pickUpIcon = value),
      getBitmapDescriptor(destMapMarker).then((value) => destinationIcon = value),
    ]);
    setState(() {});
  }

  Future getCurrentLocation() async {
    final currentPosition = await context.read<LocationService>().getCurrentLocation();
    final latlng = LatLng(currentPosition.latitude, currentPosition.longitude);

    calculateDistance(latlng);
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.sizeOf(context);

    LatLng pickupLocation =
        LatLng(widget.request.userPickUpLocation.latitude, widget.request.userPickUpLocation.longitude);
    LatLng? destinationLocation;
    if (widget.request.userDropLocation != null) {
      destinationLocation =
          LatLng(widget.request.userDropLocation!.latitude, widget.request.userDropLocation!.longitude);
    }

    return SizedBox(
      height: size.height,
      width: size.width,
      child: Column(
        children: [
          // const HomeDrawerHolder(),
          Container(
            width: MediaQuery.sizeOf(context).width,
            padding: EdgeInsets.fromLTRB(20, size.height * 0.06, 20, 20),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                bottomLeft: Radius.circular(22),
                bottomRight: Radius.circular(22),
              ),
            ),
            child: Column(
              children: [
                const Gap(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Flexible(
                      child: CustomButton.flexible(
                        width: 40.w,
                        height: 5.h,
                        buttonText: distance,
                        borderRadius: 14,
                        borderOnly: true,
                        onPressed: () {},
                      ),
                    ),
                    Container(height: 40, color: black.withOpacity(0.1), width: 1),
                    Flexible(
                      child: CustomButton(
                        width: 40.w,
                        height: 5.h,
                        buttonText: duration,
                        borderRadius: 14,
                        onPressed: () {},
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: GoogleMap(
              myLocationButtonEnabled: false,
              initialCameraPosition: CameraPosition(
                target:
                    destinationLocation != null ? getCenterPoint(pickupLocation, destinationLocation) : pickupLocation,
                zoom: 20,
              ),
              // cameraTargetBounds: CameraTargetBounds(_computeBounds([
              //   LatLng(pickUpLocation.latitude, pickUpLocation.longitude),
              //   LatLng(widget.request.userDropLocation.latitude, widget.request.userDropLocation.longitude),
              // ])),
              onMapCreated: (controller) {
                _mapController = controller;
                centerMap(pickupLocation: pickupLocation, dropoffLocation: destinationLocation);
              },
              markers: {
                if (currentPosition != null)
                  Marker(
                    zIndex: 1.0,
                    markerId: const MarkerId("driver_source"),
                    icon: sourceIcon,
                    position: LatLng(currentPosition!.latitude, currentPosition!.longitude),
                  ),
                Marker(
                  markerId: const MarkerId("user_pickup"),
                  icon: pickUpIcon,
                  position: pickupLocation,
                ),
                if (destinationLocation != null)
                  Marker(
                    markerId: const MarkerId("user_destination"),
                    icon: destinationIcon,
                    position: destinationLocation,
                  ),
              },
              polylines: {
                Polyline(polylineId: const PolylineId('route'), color: Colors.black, width: 5, points: routePoints),
              },
            ),
          ),
          RequestBottomSheet(
            stopRingtone: stopRingtone,
            rideRequest: widget.request,
            countdown: widget.countdown,
          ),
        ],
      ),
    );
  }

  void centerMap({required LatLng pickupLocation, LatLng? dropoffLocation}) {
    // LatLng centerPoint = getCenterPoint(pickupLocation, dropoffLocation);
    if (dropoffLocation == null) {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(pickupLocation, 14),
      );
    } else {
      _mapController!.animateCamera(
        CameraUpdate.newLatLngBounds(computeBounds([pickupLocation, dropoffLocation]), 20),
      );
    }
  }
}
