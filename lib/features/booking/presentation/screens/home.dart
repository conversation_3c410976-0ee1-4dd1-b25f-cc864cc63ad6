import 'dart:async';
import 'dart:convert' as decode;
import 'dart:developer';
import 'dart:io';
import 'dart:isolate';
import 'dart:ui';

import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
// import 'package:flutter_overlay_window/flutter_overlay_window.dart';
// import 'package:flutter_compass/flutter_compass.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:geolocator/geolocator.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher_string.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:yalla_gai_driver/app_locale.dart';
import 'package:yalla_gai_driver/core/constants/app_utils.dart';
import 'package:yalla_gai_driver/core/notification/custom_local_notification.dart';
import 'package:yalla_gai_driver/core/routes/paths.dart' as path;
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/core/shared_widgets/app_scafold.dart';
import 'package:yalla_gai_driver/core/shared_widgets/dialog_with_image_builder.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/language/language_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/links/links_cubit.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/chat/chats_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/connectivity/connectivity_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/nearby_users/nearby_users_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/ride_request/ride_request_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/service_bloc/service_bloc.dart';
import 'package:yalla_gai_driver/features/booking/bloc/system_service/system_services_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/account_block.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/available_section.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/star_rating.dart';
import 'package:yalla_gai_driver/features/booking/repository/counter_repository.dart';
import 'package:yalla_gai_driver/features/booking/repository/trip_repository.dart';
import 'package:yalla_gai_driver/features/setting/bloc/app_version/app_version_cubit.dart';
import 'package:yalla_gai_driver/features/setting/presentation/screens/under_maintenance_screen.dart';
import 'package:yalla_gai_driver/features/setting/presentation/screens/update_app.dart';

import '../../../../core/constants/constants.dart';
import '../../../../core/shared_widgets/dialog_holder.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/socket/custom_socket.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/images.dart';
import '../../../../main.dart';
import '../../../../notifications/firebase_push_notifications_helper.dart';
import '../../../background/location_sending_task.dart';
import '../../bloc/counter-cubit/counter_cubit.dart';
import '../../domain/entity/location_entity.dart';
import '../../domain/entity/ride_request.dart';
import '../../model/ride_request.dart';
import '../widgets/circle_button_with_icon.dart';
import '../widgets/counter_widget.dart';
import '../widgets/custom_drawer.dart';
import '../widgets/gender_selection_widget.dart';
import '../widgets/home_drawer_holder.dart';
import '../widgets/radius_selector.dart';
import '../widgets/scheduled_orders.dart';
import '../widgets/traffic_popup.dart';
import '../widgets/unavailable_view.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class DriverHome extends StatefulWidget {
  const DriverHome({super.key});

  @override
  State<DriverHome> createState() => _DriverHomeState();
}

class _DriverHomeState extends State<DriverHome> with WidgetsBindingObserver {
  late WebSocketChannel channel;
  String receivedData = '';

  void _subscribeToChannel(String channelName) {
    final subscribeMessage = {
      "event": "pusher:subscribe",
      "data": {
        "channel": channelName,
      },
    };
    channel.sink.add(decode.jsonEncode(subscribeMessage));
    log("home screen Subscribed to $channelName");
  }

  final platform = MethodChannel('com.example.app/channel');
  ReceivePort? _receivePort;

  Future<dynamic> _handleMethod(MethodCall call) async {
    if (call.method == 'launchOverlayIsolate') {
      Isolate.spawn(overlayEntry, _receivePort!.sendPort);
      return true;
    }
    return false;
  }

  @override
  void initState() {
    super.initState();

    // FlutterOverlayWindow.overlayListener.listen((event) {
    //   log("Overlay event: $event");
    // });

    _receivePort = ReceivePort();
    _receivePort?.listen((msg) {
      setState(() {
        log("Received from isolate: $msg");
      });
    });
    platform.setMethodCallHandler(_handleMethod);

    // Replace with your actual socket URL
    // final uri = Uri.parse(
    //   'wss://fasrili.online/app/lokay_production_app_key?protocol=7&client=js&version=7.2.0&flash=false',
    // );

    // channel = WebSocketChannel.connect(uri);

    // Listen to incoming messages
    // channel.stream.listen(
    //   (message) {
    //     final parsed = decode.jsonDecode(message);
    //     log("Home screen Received: $parsed");
    //     if (parsed["event"] == "pusher:connection_established") {
    //       _subscribeToChannel("ban-user_${globalDriverProfile.user?.id}");
    //     }
    //     setState(() {
    //       receivedData = message;
    //     });
    //   },
    //   onError: (error) {
    //     log("Home screen WebSocket Error: $error");
    //   },
    // );

    context.read<AppVersionCubit>().getAppVersion();

    WidgetsBinding.instance.addObserver(this);
    BlocProvider.of<AuthBloc>(context).add(const AuthGetProfile());
    PushNotificationHelper.updateFCMToken();
    _shouldAskForReview();
    context.read<LinksCubit>().fetchLinks();
    context.read<AuthBloc>().add(const AuthGetProfile());
    context.read<SystemServicesCubit>().fetchServicesList();
    final locale = context.read<AppLocale>().locale;
    BlocProvider.of<LanaguageBloc>(context).add(
      LanguageUpdate(languageId: locale.languageCode == "ar" ? "2" : "1"),
    );

    /// for getting permission for overlay popups
    // WidgetsBinding.instance.addPostFrameCallback(
    //   (timeStamp) async {
    //     if (!(await FlutterOverlayWindow.isPermissionGranted())) {
    //       await FlutterOverlayWindow.requestPermission();
    //     }
    //   },
    // );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    if (state == AppLifecycleState.detached) {
      CustomSocket.unsubscribeChannel(
        channelName:
            'enduser-send-new-request-channel_${globalDriverProfile.user?.id}',
      );

      BlocProvider.of<ServiceBloc>(context)
          .add(const ServiceChangeStatus(id: null, status: 'off'));
    }
    if (state == AppLifecycleState.paused) {
      // App moved to background
      if (Platform.isAndroid) {
        // _startForegroundService();
        if (Platform.isAndroid) {
          _startForegroundService();
        }
      }
    }

    if (state == AppLifecycleState.resumed) {
      _stopForegroundService();
    }
  }

  void _initService() {
    FlutterForegroundTask.init(
      androidNotificationOptions: AndroidNotificationOptions(
        channelId: 'foreground_service',
        channelName: 'Foreground Service Notification',
        channelDescription:
            'This notification appears when the foreground service is running.',
        onlyAlertOnce: true,
      ),
      iosNotificationOptions: const IOSNotificationOptions(
        showNotification: false,
        playSound: false,
      ),
      foregroundTaskOptions: ForegroundTaskOptions(
        eventAction: ForegroundTaskEventAction.repeat(5000),
        autoRunOnBoot: false,
        allowWakeLock: true,
      ),
    );
  }

  void _startForegroundService() async {
    _initService();
    await FlutterForegroundTask.startService(
      notificationTitle: 'Tracking Location',
      notificationText: 'We are updating your location...',
      callback: startCallback,
    );
  }

  // void startCallback() {
  //   print("Starting Location");
  //   FlutterForegroundTask.setTaskHandler(LocationTaskHandler());
  // }
  void _stopForegroundService() async {
    await FlutterForegroundTask.stopService();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void _shouldAskForReview() async {
    SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
    bool neverAskReview =
        sharedPreferences.getBool("never_ask_review") ?? false;
    if (!neverAskReview) {
      bool shouldAskReview = sharedPreferences.getBool("ask_review") ?? false;
      if (shouldAskReview && globalDriverProfile.user?.rateAt == null) {
        showCustomDialog(context, const _RatingModal());
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<RideRequestCubit, RideRequestState>(
      listener: (context, state) {
        if (state is RideNewRequest) {
          context.push(path.acceptRide);
        }
        if (state is RideRequestLoaded) {
          context.push(path.acceptRide);
        }
      },
      child: Stack(
        children: [
          Scaffold(
            backgroundColor: white,
            drawerScrimColor: Colors.transparent,
            drawer: const CustomDrawer(),
            appBar: AppBar(
              toolbarHeight: 64,
              leading: const HomeDrawerHolder(),
              bottom: PreferredSize(
                preferredSize: const Size.fromHeight(48),
                child: BlocConsumer<ServiceBloc, ServiceState>(
                  listener: (context, state) {
                    if (state is ServiceChangeStatusSuccess) {
                      if (state.status == "on") {
                        CustomSocket.subscribeNewReqChannel(
                          globalDriverProfile.user?.id ?? '',
                        );

                        setState(
                          () {
                            globalDriverProfile.changeAvailabilityStatus(true);
                          },
                        );
                      } else {
                        CustomSocket.unsubscribeChannel(
                          channelName:
                              'enduser-send-new-request-channel_${globalDriverProfile.user?.id}',
                        );
                        setState(
                          () {
                            globalDriverProfile.changeAvailabilityStatus(false);
                          },
                        );
                      }
                    }
                  },
                  builder: (context, state) {
                    if (state is ServiceChangeStatusLoading) {
                      return Container(
                        color: Colors.white.withOpacity(0.5),
                        child: const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation(primaryColor),
                          ),
                        ),
                      );
                    }
                    return ToggleButton(
                      value: globalDriverProfile.user?.avalabilityStatus
                              .toString()
                              .toLowerCase() ==
                          "on",
                      onToggleChanged: (isToggled) async {
                        if (globalDriverProfile.user?.avalabilityStatus
                                .toString()
                                .toLowerCase() ==
                            "on") {
                          BlocProvider.of<ServiceBloc>(context).add(
                            const ServiceChangeStatus(
                              id: null,
                              status: 'off',
                            ),
                          );
                        } else {
                          await context.push(path.services);
                        }
                      },
                    );
                  },
                ),
              ),
            ),
            body: const ScaffoldBody(),
          ),
          const UpdateAppScreen(),
          const UnderMaintenanceScreen(),
        ],
      ),
    );
  }
}

class ScaffoldBody extends StatefulWidget {
  const ScaffoldBody({super.key});

  @override
  State<ScaffoldBody> createState() => _ScaffoldBodyState();
}

class _ScaffoldBodyState extends State<ScaffoldBody> with LocationMixin {
  bool trafficEnabled = false;
  double? radius;

  bool genderWidget = false;
  bool radiusWidget = false;
  bool radiusPopup = false;
  bool showCrowed = false;
  bool showPoliceStations = false;
  String gender = "Male";
  double zoomLevel = 14;
  final Completer<GoogleMapController> _controller = Completer();

  // Set<Marker> _polliceMarkers = {};
  final Set<Marker> _requestMarkers = {};

  // Set<Circle> polliceCircles = {};
  // Set<Circle> requestCircles = {};
  Set<Circle> _usersCircles = {};

  // var currentLocation = const LatLng(8.90042, 38.72334);
  // var currentLocation = const LatLng(31.0461, 35.0946);

  // BitmapDescriptor? policeStationBitmapDescripter;
  // BitmapDescriptor? requestBitmapDescripter;
  BitmapDescriptor? carIcon;
  Set<Marker> markers = {};

  @override
  void initState() {
    super.initState();

    if (mounted) {
      context.read<CounterCubit>().fetchCounter().then((counter) {
        if (counter != null) {
          context.read<CounterCubit>().changeStages(stage: 'LastStage');
          onCounterPressed();
        }
      });
    }
    print("AppEnvironment.pusherApiKey:${AppEnvironment.pusherApiKey}");
    print("AppEnvironment.pusherCluster:${AppEnvironment.pusherCluster}");
    BlocProvider.of<AuthBloc>(context).add(const AuthGetProfile());
    CustomSocket.connectAndListener(context: context);
    initializeMarkerImages();
    createMarkers();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      TripRepository().getWatingTime('waiting');
      CounterRepository()
          .getCounterFees()
          .then((value) => AppUtils.counterFees = value);
    });

    SharedPreferences.getInstance().then((value) {
      radius = value.getDouble("driver_radius") ?? 1500;
    });
  }

  DateTime _lastLocationUpdateTime = DateTime.now();
  Position? _lastUpdatedLocation;
  double? _lastDirection;

  @override
  Future<void> onLocationPermissionGranted() {
    CustomLocalNotification.requestPermission();
    return super.onLocationPermissionGranted();
  }

  @override
  void onLocationUpdated(Position position) {
    super.onLocationUpdated(position);

    if (position.heading > 0) {
      _lastDirection = position.heading;
    }

    bool shouldUpdateLocation = canUpdateLocationWithGoogle;

    if (globalDriverProfile.user?.avalabilityStatus == "on") {
      shouldUpdateLocation = true;
    }

    if (DateTime.now().difference(_lastLocationUpdateTime) >
        Duration(seconds: 30)) {
      shouldUpdateLocation = true;
    }

    if (_lastUpdatedLocation != null) {
      final distance = Geolocator.distanceBetween(
        position.latitude,
        position.longitude,
        _lastUpdatedLocation!.latitude,
        _lastUpdatedLocation!.longitude,
      );
      if (distance > 10) {
        shouldUpdateLocation = true;
      }
    }

    if (shouldUpdateLocation) {
      _lastLocationUpdateTime = DateTime.now();
      _lastUpdatedLocation = position;
      updateLocation(
        lat: position.latitude,
        lng: position.longitude,
        direction: _lastDirection ?? position.heading,
      );
      if (canUpdateLocationWithGoogle) {
        setState(() {
          canUpdateLocationWithGoogle = false;
        });
      }
    }

    // setState(() {
    // Timer.periodic(const Duration(minutes: 2), (Timer t) async {
    //   final position = await Location().getLocation();
    //   updateLocation(
    //       lat: position.latitude,
    //       lng: position.longitude,
    //       direction: position.heading);
    //   setState(() {
    //     canUpdateLocationWithGoogle = false;
    //     log('working');
    //   });
    // });
    // });

    /*setState(() {
      _requestMarkers.add(Marker(
        markerId: const MarkerId('newRequest'),
        // icon:
        //     BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        icon: policeStationBitmapDescripter ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        visible: true,
        position: currentLocation,
        rotation: _direction!,
        // rotation: newLoc.heading,
        onTap: () {
          // onNewRequest();
        },
      ));
      requestCircles.add(Circle(
          circleId: const CircleId('requestCircleId'),
          radius: radius?.toDouble() ?? 1500,
          center: LatLng(position.latitude, position.longitude),
          fillColor: primaryColor.withOpacity(0.25),
          // purple.withOpacity(0.35)
          strokeColor: primaryColor.withOpacity(0.15),
          // purple.withOpacity(0.5)
          onTap: () {},
          strokeWidth: 20,
          zIndex: 2));
    });*/
    if (!showCrowed) {
      _googleMapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            zoom: zoomLevel,
            bearing: position.heading,
            target: LatLng(position.latitude, position.longitude),
          ),
        ),
      );
    }
    drawCircles(position);
  }

  createMarkers() async {
    ByteData data = await rootBundle.load("assets/images/e.png");
    Codec codec = await instantiateImageCodec(
      data.buffer.asUint8List(),
      targetHeight: 60,
    );
    FrameInfo fi = await codec.getNextFrame();
    final byteImage = (await fi.image.toByteData(format: ImageByteFormat.png))
        ?.buffer
        .asUint8List();
    carIcon = BitmapDescriptor.bytes(byteImage!);
    // policeStationBitmapDescripter = carIcon;
    // requestBitmapDescripter = carIcon;
    setState(() {});
  }

  updateLocation({lat, lng, direction}) {
    // developer.log('direction: ${newLoc.heading}');

    /*if(!canUpdateLocationWithGoogle)*/
    BlocProvider.of<TripBloc>(context).add(
      TripUpdateLocation(
        lat: lat.toString(),
        lng: lng.toString(),
        direction: direction.toString(),
        bygoogle: canUpdateLocationWithGoogle,
      ),
    );
    CustomSocket().updateLocation(lat, lng);
  }

  BitmapDescriptor? bitmapDescriptor;

  void initializeMarkerImages() async {
    // await Future.wait([
    //   getBitmapDescriptor(trafficStationMarker).then((value) => policeStationBitmapDescripter = value),
    //   getBitmapDescriptor(trafficStationMarker).then((value) => requestBitmapDescripter = value),
    // ]);
    // setState(() {});
  }

  GoogleMapController? _googleMapController;

  void setCurrentLocation() async {
    // BitmapDescriptor k = await getSvgIcon();

    /*setState(() {
      _requestMarkers.add(Marker(
        rotation: _direction ?? 0.0,
        markerId: const MarkerId('newRequest'),
        icon: policeStationBitmapDescripter ?? BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueBlue),
        // icon: k,

        visible: true,
        position: currentLocation,
        onTap: () {
          // onNewRequest();
        },
      ));

      requestCircles.add(Circle(
          circleId: const CircleId('requestCircleId'),
          radius: radius?.toDouble() ?? 1500,
          center: LatLng(
            currentLocation.latitude,
            currentLocation.longitude,
          ),
          fillColor: primaryColor.withOpacity(0.25),
          // purple.withOpacity(0.35)
          strokeColor: primaryColor.withOpacity(0.15),
          // purple.withOpacity(0.5)
          onTap: () {},
          strokeWidth: 20,
          zIndex: 2));
    });*/

    if (_controller.isCompleted && currentPosition != null) {
      final GoogleMapController controller = await _controller.future;
      controller.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(
            bearing: currentPosition?.heading ?? 0,
            target:
                LatLng(currentPosition!.latitude, currentPosition!.longitude),
            zoom: 17,
          ),
        ),
      );
      // drawCircles(currentLocation);
      // setPoliceStations();
    }
  }

  void setPoliceStations() {
    /*List<LatLng> centerCircle = [
      LatLng(currentLocation.latitude, currentLocation.longitude),
      LatLng(currentLocation.latitude - 0.003, currentLocation.longitude + 0.0022),
      LatLng(currentLocation.latitude + 0.010, currentLocation.longitude - 0.0112),
      LatLng(currentLocation.latitude + 0.0102, currentLocation.longitude - 0.0012),
    ];

    _polliceMarkers = Set.from(centerCircle.map((pos) {
      return Marker(
          rotation: _direction ?? 0.0,
          markerId: MarkerId(pos.toString()),
          icon: policeStationBitmapDescripter ?? BitmapDescriptor.defaultMarker,
          position: pos,
          onTap: () async {
            showModalBottomSheet(
                context: context,
                builder: (BuildContext context) {
                  return PoliceStationBottomSheet(location: pos);
                });
          });
    }));*/
  }

  void drawCircles(currentLocation) {
    /*List<LatLng> centerCircle = [
      LatLng(currentLocation.latitude, currentLocation.longitude),
      LatLng(currentLocation.latitude - 0.012, currentLocation.longitude + 0.004),
      LatLng(currentLocation.latitude + 0.030, currentLocation.longitude - 0.032),
      LatLng(currentLocation.latitude + 0.032, currentLocation.longitude - 0.022),
      LatLng(currentLocation.latitude, currentLocation.longitude)
    ];

    polliceCircles = Set.from(
      centerCircle
          .asMap()
          .map((index, LatLng latLng) {
            return MapEntry(index, LatLng(latLng.latitude, latLng.longitude));
          })
          .entries
          .toList()
          .map(
            (center) => Circle(
              circleId: CircleId(center.value.toString()),
              radius: center.key == 0 ? 200 : 2000,
              center: center.value,
              fillColor: center.key == 0 ? primaryColor.withOpacity(0.1) : primaryColor.withOpacity(0.35),
              // purple.withOpacity(0.35)
              strokeColor: Colors.transparent,
              // purple.withOpacity(0.5)
              onTap: () {},
            ),
          ),
    );
    if (mounted) {
      setState(() {});
    }*/
  }

  @override
  Widget build(BuildContext context) {
    return MultiBlocListener(
      listeners: [
        BlocListener<ConnectivityCubit, ConnectivityState>(
          listener: (context, state) {
            if (state is ConnectivityDisconnected) {
              dialogWithImageBuilder(
                context: context,
                image: "assets/images/no_connection.png",
                textWidget: const NoConnection(),
                // borderOnly: true,
                successText: "",
                onPressed: () async {
                  Navigator.pop(context);
                  AppSettings.openAppSettings(
                    asAnotherTask: true,
                    type: AppSettingsType.wifi,
                  );
                },
                buttonText: Utils.appText(
                  listen: false,
                  context: context,
                  text: "Try Again",
                  arabicText: "حاول ثانية",
                ),
                showButtonInside: true,
              );
            }
          },
        ),
        BlocListener<NearbyUserCubit, NearbyUserState>(
          listener: (context, state) {
            if (state is NearbyUserStateLoaded) {
              if (showCrowed) _updatedUsersOnMap();
            }
          },
        ),
        BlocListener<ServiceBloc, ServiceState>(
          listener: (context, state) {
            if (state is ServiceChangeStatusSuccess) {
              setState(() {});
            }
          },
        ),
      ],
      child: Stack(
        children: [
          BlocConsumer<RideRequestCubit, RideRequestState>(
            builder: (contex, state) => Container(),
            listener: (context, state) {
              if (state is RideNewRequest) {
                onNewRequest(state.newRequest);
              }
            },
          ),
          GoogleMap(
            myLocationButtonEnabled: false,
            onCameraMove: (position) {
              zoomLevel = position.zoom;
            },
            // myLocationEnabled: true,
            tiltGesturesEnabled: true,
            scrollGesturesEnabled: true,
            zoomGesturesEnabled: true,
            compassEnabled: true,
            rotateGesturesEnabled: true,
            // myLocationEnabled: true,
            trafficEnabled: trafficEnabled,
            initialCameraPosition: CameraPosition(
              target: LatLng(
                currentPosition?.latitude ?? 0,
                currentPosition?.longitude ?? 0,
              ),
              zoom: zoomLevel,
            ),
            onMapCreated: (controller) async {
              _controller.complete(controller);
              setCurrentLocation();
              setState(() {
                _googleMapController = controller;
              });

              bool serviceEnabled =
                  await context.read<LocationService>().locationServiceEnabled;
              if (!serviceEnabled) {
                await Geolocator.openLocationSettings();
              }
            },
            circles: {
              if (showCrowed) ..._usersCircles,
              if (radiusWidget && currentPosition != null) ...{
                Circle(
                  circleId: const CircleId('requestCircleId'),
                  radius: radius?.toDouble() ?? 1500,
                  center: LatLng(
                    currentPosition!.latitude,
                    currentPosition!.longitude,
                  ),
                  fillColor: primaryColor.withOpacity(0.25),
                  strokeColor: primaryColor.withOpacity(0.15),
                  strokeWidth: 20,
                  zIndex: 2,
                ),
              },
            },
            markers: {
              if (currentPosition != null)
                Marker(
                  anchor: const Offset(0.5, 0.5),
                  markerId: const MarkerId('direction'),
                  icon: carIcon ?? BitmapDescriptor.defaultMarker,
                  position: LatLng(
                    currentPosition!.latitude,
                    currentPosition!.longitude,
                  ),
                ),
            },
          ),
          leftSideActions(context),
          rightSideActions(context),
          if (genderWidget)
            Positioned(
              bottom: 200,
              left: 100,
              child: GenderSelectionWidget(
                selectedGender: gender,
                onGenderSelected: (currentGender) {
                  log(currentGender);
                  context.read<CounterCubit>().updateClientGender(
                        gender: currentGender.toLowerCase(),
                      );
                  setState(() {
                    gender = currentGender;
                    genderWidget = !genderWidget;
                  });
                },
              ),
            ),
          if (radiusPopup)
            Positioned(
              bottom: 200,
              right: 100,
              child: RadiusSelectionWidget(
                selectedRadius: radius ?? 1500,
                onRadiusSelected: (currentRadius) {
                  setState(() {
                    radius = currentRadius;
                    /*requestCircles.clear(); // Clear existing circles
                    requestCircles.add(
                      Circle(
                        circleId: const CircleId('requestCircleId'),
                        radius: radius?.toDouble() ?? 1500,
                        center: LatLng(currentLocation.latitude, currentLocation.longitude),
                        fillColor: primaryColor.withOpacity(0.25),
                        strokeColor: primaryColor.withOpacity(0.15),
                        onTap: () {},
                        strokeWidth: 20,
                        zIndex: 2,
                      ),
                    );
                    drawCircles(currentLocation); // Add this line to draw the circles on the map*/
                    // log((radius).toInt().toString());
                    BlocProvider.of<TripBloc>(context).add(
                      TripStoreRadius(radius: radius!.toInt().toString()),
                    );
                  });
                },
                onSet: () {
                  setState(() {
                    // BlocProvider.of<AuthBloc>(context).add(const AuthGetProfile());
                    radiusPopup = false;
                    // drawCircles(currentLocation); // Add this line to draw the circles on the map
                    SharedPreferences.getInstance().then(
                      (value) =>
                          value.setDouble("driver_radius", radius ?? 1500),
                    );
                  });
                },
              ),
            ),
          if (globalDriverProfile.user?.avalabilityStatus
                  .toString()
                  .toLowerCase() !=
              "on")
            const Positioned(
              bottom: 0,
              child: UnavailableView(),
            ),
          context.watch<AuthBloc>().state is AuthChangeAvaliablityLoading
              ? Container(
                  color: Colors.white.withOpacity(0.5),
                  child: const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation(primaryColor),
                    ),
                  ),
                )
              : const SizedBox.shrink(),
          const AccountBlockInfo(),
        ],
      ),
    );
  }

  leftSideActions(BuildContext context) {
    final genderButtonEnabled =
        context.select<SystemServicesCubit, bool>((value) {
      final serviceData = value.services
          .where((element) => element.type == "select_gender")
          .firstOrNull;
      return serviceData == null ? true : serviceData.status == "active";
    });

    final counterEnabled = context.select<SystemServicesCubit, bool>((value) {
      final serviceData = value.services
          .where((element) => element.type == "counter")
          .firstOrNull;
      return serviceData == null ? true : serviceData.status == "active";
    });

    return Positioned(
      bottom: 150,
      left: 20,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CircleButton(
            shadow: true,
            imageIcon: Transform.scale(
              scale: 0.5,
              child: SvgPicture.asset(
                threePeopleIcon,
                color: showCrowed ? primaryColor : black,
              ),
            ),
            onTap: onShowCrowedPressed,
            backgroundColor: showCrowed
                ? Color.alphaBlend(primaryColor.withOpacity(0.2), white)
                : white,
          ),
          const Gap(),
          if (genderButtonEnabled) ...[
            BlocBuilder<AuthBloc, AuthState>(
              builder: (context, state) {
                if (state is AuthGetProfileSuccess) {
                  return state.driverProfile.user?.gender
                              .toString()
                              .toLowerCase() ==
                          "male"
                      ? Container()
                      : CircleButton(
                          shadow: true,
                          imageIcon: Transform.scale(
                            scale: 0.5,
                            child: Icon(
                              Icons.male_rounded,
                              size: 60,
                              color: genderWidget ? white : black,
                            ),
                          ),
                          backgroundColor: genderWidget ? primaryColor : white,
                          onTap: onGenderButtonPressed,
                        );
                }
                return Container();
              },
            ),
            const Gap(),
          ],
          if (counterEnabled) ...[
            CircleButton(
              shadow: true,
              onTap: onCounterPressed,
              imageIcon: Transform.scale(
                scale: 0.5,
                child: Transform.scale(
                  scale: 1.1,
                  child: Image.asset(counterIcon),
                ),
              ),
              backgroundColor: white,
            ),
            const Gap(),
          ],

          // CircleButton(
          //   shadow: true,
          //   backgroundColor: showPoliceStations ? primaryColor : white,
          //   onTap: onPoliceStationPressed,
          //   imageIcon: Transform.scale(
          //     scale: 0.5,
          //     child: SvgPicture.asset(trafficIcon,
          //         color: showPoliceStations ? white : black),
          //   ),
          // ),
        ],
      ),
    );
  }

  rightSideActions(BuildContext context) {
    return Positioned(
      bottom: 150,
      right: 20,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          CircleButton(
            shadow: true,
            imageIcon: Transform.scale(
              scale: 0.5,
              child: Icon(
                Icons.radar_outlined,
                size: 60,
                color: radiusWidget ? primaryColor : black,
              ),
            ),
            backgroundColor: radiusWidget
                ? Color.alphaBlend(primaryColor.withOpacity(0.2), white)
                : white,
            onTap: onRadiusButtonPressed,
          ),
          // const Gap(),
          // CircleButton(
          //   shadow: true,
          //   imageIcon: Transform.scale(
          //     scale: 0.5,
          //     child: const Icon(Icons.schedule, size: 60),
          //   ),
          //   onTap: onScheduledRides,
          //   backgroundColor: white,
          // ),
          const Gap(),
          CircleButton(
            shadow: true,
            imageIcon: Transform.scale(
              scale: 0.5,
              child: Icon(
                Icons.traffic,
                size: 60,
                color: trafficEnabled ? white : black,
              ),
            ),
            onTap: onTrafficPressed,
            backgroundColor: trafficEnabled
                ? Color.alphaBlend(primaryColor.withOpacity(0.2), white)
                : white,
          ),
          const Gap(),
          CircleButton(
            shadow: true,
            imageIcon: Transform.scale(
              scale: 0.5,
              child: Padding(
                padding: const EdgeInsets.only(right: 5, top: 5),
                child: SvgPicture.asset(
                  currentLocationPointer,
                ),
              ),
            ),
            onTap: () {
              setCurrentLocation();
            },
            backgroundColor: white,
          ),
        ],
      ),
    );
  }

  onNewRequest(ComingRideRequest ride) {
    context.read<ChatsCubit>().clearMessages();
    CustomSocket.reSubscribeChannel(
      channelName: "accept-request-done-channel_${ride.id}",
    );
    CustomSocket.reSubscribeChannel(
      channelName: "private-accept-request-done-channel_${ride.id}",
    );
    LocationEntity pickUpLocation = LocationEntity(
      locationText: ride.pickupLocation ?? "",
      latitude: ride.pickupLatitude,
      longitude: ride.pickupLongitude,
      streetName: null,
    );

    LocationEntity? dropLocation =
        ride.destinationLatitude != null && ride.destinationLongitude != null
            ? LocationEntity(
                locationText: ride.destinationLocation ?? "",
                latitude: ride.destinationLatitude!,
                longitude: ride.destinationLongitude!,
                streetName: null,
              )
            : null;

    RideRequest rideRequest = RideRequest(
      fare: ride.fare ?? "",
      id: ride.id ?? "",
      name: "John Doe",
      profilePicture: "https://example.com/profile_picture.jpg",
      userPickUpLocation: pickUpLocation,
      userDropLocation: dropLocation,
    );

    Future.delayed(Duration.zero, () async {
      SharedPreferences preferences = await SharedPreferences.getInstance();
      double waitTime = preferences.getDouble("waitTime") ?? 30;
      // ignore: use_build_context_synchronously
      context.push(
        path.newRideRequest,
        extra: {
          "rideRequest": rideRequest,
          "countDown": waitTime.toInt(),
        },
      );
    });
  }

  onCounterPressed() async {
    SharedPreferences preferences = await SharedPreferences.getInstance();
    final counterFees =
        preferences.getDouble("counterFees") ?? AppUtils.counterFees;
    showCustomDialog(context, CounterWidget(fees: counterFees));
  }

  onShowCrowedPressed() {
    setState(() {
      showCrowed = !showCrowed;
      if (showCrowed) {
        context.read<NearbyUserCubit>().fetchNearbyUser();
      } else {
        _usersCircles = {};
      }
    });
    if (showCrowed) {
      _updatedUsersOnMap();
    }
  }

  void _updatedUsersOnMap() {
    final users = context.read<NearbyUserCubit>().users;
    setState(() {
      _usersCircles = {
        // Testing coordinates
        /*...[
          const LatLng(21.23, 72.84),
          const LatLng(21.23, 72.845),
          const LatLng(21.235, 72.845),
          const LatLng(21.24, 72.85),
          const LatLng(21.24, 72.845),
        ].map((point) {
          return Circle(
            circleId: CircleId(point.hashCode.toString()),
            radius: 150,
            fillColor: Colors.green.withOpacity(0.30),
            strokeColor: Colors.green.withOpacity(0.30),
            strokeWidth: 0,
            center: point,
          );
        }),*/
        ...users.map((user) {
          return Circle(
            circleId: CircleId(user.id.toString()),
            radius: 150,
            fillColor: Colors.green.withOpacity(0.30),
            strokeColor: Colors.green.withOpacity(0.30),
            strokeWidth: 0,
            center: LatLng(
              double.parse(user.latitude),
              double.parse(user.longitude),
            ),
          );
        }),
      };
    });
  }

  onScheduledRides() {
    showModalBottomSheet(
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return const ScheduledOrders();
      },
    );
  }

  onGenderButtonPressed() {
    setState(() {
      radiusWidget = false;
      genderWidget = !genderWidget;
    });
  }

  onRadiusButtonPressed() {
    if (radiusWidget) {
      setState(() {
        radiusWidget = false;
        radiusPopup = false;
        genderWidget = false;
      });
    } else {
      setState(() {
        radiusWidget = true;
        radiusPopup = true;
        genderWidget = false;
      });
    }
  }

  onTrafficPressed() {
    setState(() {
      trafficEnabled = !trafficEnabled;
    });
    if (trafficEnabled) showCustomDialog(context, const TrafficPopup());
  }

/*onPoliceStationPressed() async {
    if (showPoliceStations) {
      setState(() => showPoliceStations = false);
      return;
    }
    final bool? action = await showModalBottomSheet<bool?>(
        context: context,
        builder: (BuildContext context) {
          return const AddPoliceLocationBottomSheet();
        });
    if (!(action ?? false)) {
      showModalBottomSheet(
          context: context,
          builder: (BuildContext context) {
            return AddCurPositionBottomSheet(location: currentLocation);
          });
    } else {
      setState(() {
        if (action ?? false) showPoliceStations = !showPoliceStations;
      });
    }
  }*/
}

class _RatingModal extends StatelessWidget {
  const _RatingModal();

  @override
  Widget build(BuildContext context) {
    return Dialog(
      elevation: 0,
      backgroundColor: Colors.white.withOpacity(0.9),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              "Rate Us!",
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.w600),
            ),
            const Gap(h: 16),
            const Text(
              "How do you love this app",
              style: TextStyle(fontSize: 14, color: Colors.black54),
            ),
            const Gap(h: 16),
            StarRating(
              onUpdateRating: (index) async {
                AuthRepository().updateRateApp();

                final platform = Platform.operatingSystem;
                final link = context
                    .read<LinksCubit>()
                    .links
                    .where((element) => element.type == platform)
                    .firstOrNull;
                if (link != null) launchUrlString(link.link);

                SharedPreferences sharedPreferences =
                    await SharedPreferences.getInstance();
                sharedPreferences.setBool("never_ask_review", true);
                Navigator.of(context).pop();
              },
              rating: 5,
            ),
            const Gap(h: 16),
            TextButton(
              style: TextButton.styleFrom(foregroundColor: Colors.black54),
              onPressed: () async {
                AuthRepository().updateRateApp();

                SharedPreferences sharedPreferences =
                    await SharedPreferences.getInstance();
                sharedPreferences.setBool("never_ask_review", true);
                Navigator.of(context).pop();
              },
              child: const Text(
                "No Thanks",
                style: TextStyle(fontWeight: FontWeight.w600),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
