import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_app_bar.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/phone_url_luncher.dart';
import '../widgets/car_hist_card.dart';
import '../widgets/circle_button_with_icon.dart';
import '../widgets/driver_stats_card.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class DriverDetails extends StatefulWidget {
  const DriverDetails({
    Key? key,
  }) : super(key: key);

  @override
  State<DriverDetails> createState() => _DriverDetailsState();
}

class _DriverDetailsState extends State<DriverDetails> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: CustomAppBar(
        title: AppLocalizations.of(context)!.driver_etails,
        bottomBorder: false,
      ),
      body: SingleChildScrollView(
        child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: [
          const Gap(),
          CircleAvatar(
            radius: 6.h,
            backgroundImage: const NetworkImage(
                'https://u4v9n6k3.rocketcdn.me/content/uploads/2021/02/Austin_Daniel.png'),
          ),
          const Gap(),
          Text(
            'Daniel Austin',
            style: Theme.of(context).textTheme.titleLarge!.copyWith(
                color: Colors.black, fontSize: 32, fontWeight: FontWeight.w500),
          ),
          Gap(
            h: 5.h,
          ),
          const Padding(
            padding: EdgeInsets.only(left: 16.0, right: 16),
            child: Divider(),
          ),
          const Gap(),
          const Padding(
            padding: EdgeInsets.all(16),
            child: StatsCard(
              rating: 4.6,
              years: 5,
              trips: 279,
            ),
          ),
          const Gap(),
          const Padding(
            padding: EdgeInsets.all(16),
            child: CarHistory(
              memberSince: 'July 2 2023',
              carModel: 'Mercedes Benz',
              plateNumber: 'HSQ 4736 XK',
            ),
          ),
          const Gap(),
          Row(mainAxisAlignment: MainAxisAlignment.spaceAround, children: [
            CircleButton(
                icon: Icons.phone_in_talk_sharp,
                backgroundColor: primaryColor,
                onTap: () {
                  callPhoneNumber("251961088592");
                }),
            CircleButton(
                icon: Icons.message_outlined,
                backgroundColor: secondaryColor,
                onTap: () {
                  context.push(path.chat);
                }),
          ])
        ]),
      ),
    );
  }
}
