/*
import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:flutter_polyline_points/flutter_polyline_points.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/environment.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../../../core/shared_widgets/custom_button.dart';
import '../../../../core/shared_widgets/gap.dart';
import '../../../../core/utils/colors.dart';
import '../../../../core/utils/get_polyline_points.dart';
import '../../../../core/utils/images.dart';
import '../../../../core/utils/uti.dart';
import '../../../authentication/bloc/auth_bloc/auth_bloc.dart';
import '../../domain/entity/location_entity.dart';
import '../widgets/custom_drawer.dart';
import '../widgets/floating_container.dart';
import '../widgets/home_drawer_holder.dart';
import '../widgets/schedule_and_payment_method.dart';

class PickUpAdded extends StatelessWidget {
  PickUpAdded({
    Key? key,
    required this.source,
    required this.destination,
  }) : super(key: key);

  final LocationEntity? source;
  final LocationEntity? destination;

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _scaffoldKey,
      drawer: const CustomDrawer(),
      drawerScrimColor: Colors.transparent,
      body: ScaffoldBody(
        source: source,
        destination: destination,
      ),
    );
  }
}

class ScaffoldBody extends StatefulWidget {
  const ScaffoldBody({
    Key? key,
    required this.source,
    required this.destination,
  }) : super(key: key);

  final LocationEntity? source;
  final LocationEntity? destination;

  @override
  State<ScaffoldBody> createState() => _ScaffoldBodyState();
}

class _ScaffoldBodyState extends State<ScaffoldBody> {
  final Completer<GoogleMapController> _controller = Completer();
  final Set<Marker> _markers = {};
  List<LatLng> routePoints = [];
  PolylinePoints polylinePoints = PolylinePoints();
  int selectedVehicle = 0;
  BitmapDescriptor? pointAMarker;
  BitmapDescriptor? pointBMarker;

  // List<String> recentLocations = ["Priority", "Smart", "Deluxe"];
  List<String> recentLocations = [];
  List<Transform> actionIcons = [
    Transform.scale(
      scale: 1.0,
      child: Image.asset(priorityCar),
    ),
    Transform.scale(
      scale: 1.0,
      child: Image.asset(smartCar),
    ),
    Transform.scale(
      scale: 1.0,
      child: Image.asset(deluxeCar),
    ),
  ];

  @override
  void initState() {
    super.initState();
    if (widget.source != null && widget.destination != null) {
      getPolylineBetweenTwoPoints(LatLng(widget.source!.latitude, widget.source!.longitude),
          LatLng(widget.destination!.latitude, widget.destination!.longitude));
    }
    setMarkerImage();
  }

  void setMarkerImage() async {
    await Future.wait([
      getBitmapDescriptor(locationAMarker).then((value) => pointAMarker = value),
      getBitmapDescriptor(locationBMarker).then((value) => pointBMarker = value),
    ]);
    setState(() {});
  }

  Future<void> getPolylineBetweenTwoPoints(LatLng source, LatLng destination) async {
    PolylineResult result = await polylinePoints.getRouteBetweenCoordinates(AppEnvironment.googleMapsApiKey,
        PointLatLng(source.latitude, source.longitude), PointLatLng(destination.latitude, destination.longitude));
    if (result.points.isNotEmpty) {
      for (var point in result.points) {
        setState(() {
          routePoints.add(LatLng(point.latitude, point.longitude));
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final driver = context.watch<AuthBloc>().user.user;
    recentLocations = [
      AppLocalizations.of(context)!.priority,
      AppLocalizations.of(context)!.smart,
      AppLocalizations.of(context)!.deluxe,
    ];
    return Builder(builder: (context) {
      return Stack(
        children: [
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: LatLng(widget.source != null ? widget.source!.latitude : widget.destination!.latitude,
                  widget.source != null ? widget.source!.longitude : widget.destination!.longitude),
              zoom: 16.5,
            ),
            onMapCreated: (controller) {
              _controller.complete(controller);
            },
            markers: {
              if (widget.source != null)
                Marker(
                  markerId: const MarkerId("Point A"),
                  position: LatLng(widget.source!.latitude, widget.source!.longitude),
                  icon: pointAMarker ??
                      BitmapDescriptor.defaultMarkerWithHue(
                        BitmapDescriptor.hueCyan,
                      ),
                ),
              if (widget.destination != null)
                Marker(
                  markerId: const MarkerId("Point B"),
                  position: LatLng(widget.destination!.latitude, widget.destination!.longitude),
                  icon: pointBMarker ??
                      BitmapDescriptor.defaultMarkerWithHue(
                        BitmapDescriptor.hueCyan,
                      ),
                ),
            },
            polylines: {
              Polyline(polylineId: const PolylineId('route'), color: Colors.black, width: 5, points: routePoints),
            },
          ),
          const HomeDrawerHolder(),
          Positioned(
            bottom: (widget.source != null && widget.destination != null) ? 415 : 150,
            left: 5.w,
            child: Container(
              width: MediaQuery.of(context).size.width - 10.w,
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    spreadRadius: 2,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Column(
                    children: [
                      CircleAvatar(
                        backgroundColor: primaryColor,
                        minRadius: 17,
                        child: Text(
                          AppLocalizations.of(context)!.a,
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: white),
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5),
                        child: SvgPicture.asset(arrowDown),
                      ),
                      CircleAvatar(
                        backgroundColor: black.withOpacity(0.6),
                        minRadius: 17,
                        child: Text(
                          AppLocalizations.of(context)!.b,
                          style: Theme.of(context).textTheme.bodyMedium!.copyWith(color: white),
                        ),
                      ),
                    ],
                  ),
                  Gap(w: 3.w),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      SizedBox(
                        width: 250,
                        child: Text(
                          widget.source == null ? "-------------------" : widget.source!.locationText,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                      const Gap(h: 50),
                      SizedBox(
                        width: 250,
                        child: Text(
                          widget.destination == null ? "-------------------" : widget.destination!.locationText,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if (widget.source != null && widget.destination != null)
            Positioned(
                right: 0,
                bottom: 190,
                child: FloatingContainer(
                    width: MediaQuery.of(context).size.width - 5.w,
                    showCoupon: true,
                    height: 200,
                    title: AppLocalizations.of(context)!.select_vehicle,
                    child: SizedBox(
                      height: 100,
                      child: ListView.separated(
                          separatorBuilder: (context, index) {
                            return Gap(w: 6.w);
                          },
                          scrollDirection: Axis.horizontal,
                          itemCount: recentLocations.length,
                          itemBuilder: (context, index) {
                            return InkWell(
                              onTap: () {
                                setState(() {
                                  selectedVehicle = index;
                                });
                              },
                              child: Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
                                  margin: const EdgeInsets.all(5),
                                  decoration: BoxDecoration(
                                    color: white,
                                    borderRadius: BorderRadius.circular(8),
                                    border:
                                        Border.all(width: 3, color: selectedVehicle == index ? primaryColor : white),
                                    boxShadow: [
                                      BoxShadow(
                                        color: black.withOpacity(0.1), // Shadow color
                                        spreadRadius: 2, // Spread radius
                                        blurRadius: 5, // Blur radius
                                        offset: const Offset(0, 3), // Offset from the container
                                      ),
                                      BoxShadow(
                                        color: black.withOpacity(0.1), // Shadow color
                                        spreadRadius: 2, // Spread radius
                                        blurRadius: 5, // Blur radius
                                        offset: const Offset(3, 0), // Offset from the container
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      Column(
                                        children: [
                                          Text(recentLocations[index],
                                              style: Theme.of(context).textTheme.labelSmall!.copyWith(
                                                  fontSize: 13.5.sp,
                                                  color: black,
                                                  fontFamily: "Plus Jakarta Sans",
                                                  fontWeight: FontWeight.bold)),
                                          Row(children: [
                                            const Icon(Icons.person_rounded, size: 14),
                                            Text("x 5",
                                                style: Theme.of(context).textTheme.labelSmall!.copyWith(
                                                    fontSize: 9.5.sp,
                                                    fontFamily: "Plus Jakarta Sans",
                                                    color: primaryColor,
                                                    fontWeight: FontWeight.bold))
                                          ]),
                                          const Gap(h: 5),
                                          GestureDetector(
                                              onTap: () {
                                                context.push(path.carDetail);
                                              },
                                              child: actionIcons[index]),
                                        ],
                                      ),
                                      Gap(w: 3.w),
                                      Column(
                                        // mainAxisAlignment:
                                        //     MainAxisAlignment.spaceEvenly,
                                        children: [
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            children: [
                                              Text("5 ${AppLocalizations.of(context)!.min}",
                                                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                                      color: primaryColor,
                                                      fontSize: 12.5.sp,
                                                      fontFamily: "Plus Jakarta Sans",
                                                      fontWeight: FontWeight.bold)),
                                              Gap(w: 2.w),
                                              Text(AppLocalizations.of(context)!.auto,
                                                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                                                      fontSize: 11.5.sp,
                                                      fontFamily: "Plus Jakarta Sans",
                                                      fontWeight: FontWeight.bold)),
                                            ],
                                          ),
                                          Container(
                                              margin: const EdgeInsets.only(
                                                top: 8,
                                                bottom: 10,
                                              ),
                                              color: Colors.grey,
                                              height: 1,
                                              width: 80),
                                          Text(
                                              "50 ${Utils.appText(context: context, text: driver?.country?.englishCurrency ?? '', arabicText: driver?.country?.arabicCurrency ?? '')}",
                                              style: Theme.of(context).textTheme.bodyLarge!.copyWith(
                                                  fontSize: 16.5.sp,
                                                  fontWeight: FontWeight.bold,
                                                  fontFamily: "Plus Jakarta Sans"))
                                        ],
                                      ),
                                    ],
                                  )),
                            );
                          }),
                    ))),
          if (widget.source != null && widget.destination != null)
            Positioned(bottom: 110, right: 5.w, child: const ScheduleAndPayment()),
          Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 30, vertical: 40),
                child: CustomButton(
                  buttonText: widget.destination == null
                      ? AppLocalizations.of(context)!.select_drop_off_point
                      : widget.source == null
                          ? AppLocalizations.of(context)!.select_pick_up_point
                          : AppLocalizations.of(context)!.continue_,
                  width: 55.w,
                  borderRadius: 12,
                  onPressed: () {
                    if (widget.source == null) {
                      // Set text to "Pick up point"
                      context.push(path.pickLocation,
                          extra: {'dropOff': false, 'source': null, 'destination': widget.destination});
                    } else if (widget.destination == null) {
                      // Set text to "Select Drop off point"
                      context.push(path.pickLocation, extra: {'dropOff': true, 'source': widget.source});
                    } else if (widget.destination != null && widget.source != null) {
                      context.push(path.confirmBooking,
                          extra: {'source': widget.source, 'destination': widget.destination});
                    }
                  },
                )),
          ),
        ],
      );
    });
  }
}
*/
