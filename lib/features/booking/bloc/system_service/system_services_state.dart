part of 'system_services_cubit.dart';

sealed class SystemServicesState extends Equatable {
  const SystemServicesState();

  @override
  List<Object> get props => [];
}

final class SystemServicesInitial extends SystemServicesState {}

final class SystemServicesLoading extends SystemServicesState {}

final class SystemServicesLoaded extends SystemServicesState {}

final class SystemServicesError extends SystemServicesState {}
