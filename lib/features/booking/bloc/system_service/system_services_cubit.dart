import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:yalla_gai_driver/features/booking/model/system_service_model.dart';
import 'package:yalla_gai_driver/features/booking/repository/system_service_repository.dart';

part 'system_services_state.dart';

class SystemServicesCubit extends Cubit<SystemServicesState> {
  SystemServiceRepository serviceRepository;

  SystemServicesCubit(this.serviceRepository) : super(SystemServicesInitial());

  List<SystemService> services = [];

  fetchServicesList() async {
    try {
      emit(SystemServicesLoading());
      final result = await serviceRepository.fetchSystemServices();
      emit(SystemServicesLoaded());
      services = result;
    } catch (error) {
      log("System Services: ${error.toString()}");
      emit(SystemServicesError());
    }
  }
}
