part of 'tech_supports_cubit.dart';

sealed class TechSupportsState extends Equatable {
  const TechSupportsState();

  @override
  List<Object> get props => [];
}

final class TechSupportsInitialState extends TechSupportsState {}

final class TechSupportsLoadingState extends TechSupportsState {}

final class TechSupportsLoadedState extends TechSupportsState {
  final DateTime timestamp;

  TechSupportsLoadedState() : timestamp = DateTime.now();

  @override
  List<Object> get props => [timestamp];
}

final class TechSupportsErrorState extends TechSupportsState {}
