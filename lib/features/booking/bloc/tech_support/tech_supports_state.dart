part of 'tech_supports_cubit.dart';

sealed class TechSupportsState extends Equatable {
  const TechSupportsState();

  @override
  List<Object> get props => [];
}

final class TechSupportsInitialState extends TechSupportsState {}

final class TechSupportsLoadingState extends TechSupportsState {}

final class TechSupportsLoadedState extends TechSupportsState {}

final class TechSupportsErrorState extends TechSupportsState {}
