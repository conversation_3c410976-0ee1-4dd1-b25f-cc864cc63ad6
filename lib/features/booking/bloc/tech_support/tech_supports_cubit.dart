import 'dart:convert';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/booking/model/chat.dart';
import 'package:yalla_gai_driver/features/booking/repository/tech_support_repository.dart';

part 'tech_supports_state.dart';

class TechSupportsCubit extends Cubit<TechSupportsState> {
  TechSupportRemoteDatasource techSupportRemoteDatasource = TechSupportRemoteDatasource();

  TechSupportsCubit() : super(TechSupportsInitialState());

  List<ChatMessage> chats = [];
  final chatController = TextEditingController();

  sendChat() async {
    final message = chatController.text;
    chatController.clear();
    chatController.clearComposing();
    if (message.isEmpty) {
      return;
    }
    try {
      emit(TechSupportsLoadingState());
      final response = await techSupportRemoteDatasource.sendChat(message: message);
      log("Chat status code::${response.statusCode}\n${response.body}");
      if (response.isSuccess) {
        final message = ChatMessage.fromJson(jsonDecode(response.body)["message"]);
        chats.add(message);
        emit(TechSupportsLoadedState());
      } else {
        emit(TechSupportsErrorState());
      }
    } catch (err) {
      emit(TechSupportsErrorState());
      log("Chat error::$err");
    }
    log(chats.length.toString());
  }

  fetchChat() async {
    try {
      emit(TechSupportsLoadingState());
      final response = await techSupportRemoteDatasource.fetchChat();
      log("Chat status code::${response.statusCode}\n${response.body}");
      if (response.isSuccess) {
        chats.clear();
        var messages = json.decode(response.body)['messages'];
        for (var message in messages) {
          chats.add(ChatMessage.fromJson(message));
        }
        chats.sort((a, b) => DateTime.parse(a.createdAt.toString()).compareTo(DateTime.parse(b.createdAt.toString())));
        emit(TechSupportsLoadedState());
      } else {
        emit(TechSupportsErrorState());
      }
    } catch (err) {
      emit(TechSupportsErrorState());
      log("Chat error::$err");
    }
    log(chats.length.toString());
  }

  newTechSupportsEvent(PusherEvents event) async {
    String userId = LocalStorage.instance.userId ?? "";

    log("Event::${event.channelName}");
    log("Event data::${event.data}");

    try {
      final data = event.data['message'];
      if (data != null) {
        var chatMessage = ChatMessage.fromJson(data);
        log("New tech support message - SenderId: ${chatMessage.senderId}, ReceiverId: ${chatMessage.receiverId}, CurrentUserId: $userId");

        // For tech support, show messages where user is either sender or receiver
        // This ensures we see both sides of the conversation
        if (chatMessage.receiverId == userId || chatMessage.senderId == userId) {
          // Check if message already exists to avoid duplicates
          bool messageExists = chats.any((existingMessage) => existingMessage.id == chatMessage.id);

          if (!messageExists) {
            chats.add(chatMessage);
            chats.sort((a, b) => DateTime.parse(a.createdAt.toString()).compareTo(DateTime.parse(b.createdAt.toString())));

            log("Added new tech support message. Total messages: ${chats.length}");
            // Force UI update by emitting a new state instance
            emit(TechSupportsLoadedState());

            // Show notification only for received messages (not sent by current user)
            if (chatMessage.senderId != userId) {
              // CustomLocalNotification.showTechSupportNotification(chatMessage);
            }
          } else {
            log("Message already exists, skipping duplicate");
          }
        } else {
          log("Message not for current user - SenderId: ${chatMessage.senderId}, ReceiverId: ${chatMessage.receiverId}");
        }
      } else {
        log("No message data in event");
      }
    } catch (e) {
      log("Error processing tech support event: $e");
    }
  }

  // Method to manually refresh the UI (useful for debugging)
  void refreshUI() {
    emit(TechSupportsLoadedState());
  }

  // Method to get current chat count (useful for debugging)
  int getChatCount() {
    return chats.length;
  }
}
