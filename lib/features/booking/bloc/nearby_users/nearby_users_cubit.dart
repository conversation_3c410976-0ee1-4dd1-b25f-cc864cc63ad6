import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:yalla_gai_driver/features/booking/model/nearby_user.dart';
import 'package:yalla_gai_driver/features/booking/repository/service_repository.dart';

part 'nearby_users_state.dart';

class NearbyUserCubit extends Cubit<NearbyUserState> {
  ServiceRepository serviceRepository;

  NearbyUserCubit(this.serviceRepository) : super(NearbyUserStateInitial());

  List<NearbyUser> users = [];

  Future fetchNearbyUser() async {
    emit(NearbyUserStateLoading());
    try {
      users = await serviceRepository.getNearbyUsers();
      emit(NearbyUserStateLoaded());
    } catch (e) {
      log(e.toString());
      emit(NearbyUserStateError());
    }
    return;
  }
}
