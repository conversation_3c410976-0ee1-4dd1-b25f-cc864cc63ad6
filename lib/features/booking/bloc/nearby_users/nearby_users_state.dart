part of 'nearby_users_cubit.dart';

sealed class NearbyUserState extends Equatable {
  const NearbyUserState();

  @override
  List<Object> get props => [];
}

final class NearbyUserStateInitial extends NearbyUserState {}

final class NearbyUserStateLoading extends NearbyUserState {}

final class NearbyUserStateLoaded extends NearbyUserState {}

final class NearbyUserStateError extends NearbyUserState {}
