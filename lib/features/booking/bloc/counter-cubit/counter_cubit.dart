import 'dart:convert';
import 'dart:developer';
import 'package:bloc/bloc.dart';
import '../../model/counter.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/features/booking/repository/counter_repository.dart';

part 'counter_state.dart';

class CounterCubit extends Cubit<CounterState> {
  CounterRepository counterRepository;
  CounterCubit(this.counterRepository) : super(CounterInitial());

  saveCounter(CounterModel counter) async {
    final pref = await SharedPreferences.getInstance();
    await pref.setString("counter", jsonEncode(counter.toJson()));
  }

  deleteCounter() async {
    final pref = await SharedPreferences.getInstance();
    await pref.remove("counter");
  }

  Future<CounterModel?> fetchCounter() async {
    final pref = await SharedPreferences.getInstance();
    final response = pref.getString("counter");
    if (response != null) {
      counter = CounterModel.fromJson(jsonDecode(response));
      return counter;
    }
    return null;
  }

  CounterModel? counter;

  startCounter() async {
    emit(CounterStartLoading());
    try {
      counter = await counterRepository.startCounter();
      changeStages(stage: 'LastStage');
      saveCounter(counter!);
      emit(CounterStartSuccess(counter: counter));
    } catch (e) {
      emit(CounterStartFaulire());
    }
  }

  CounterEnd? endCounter;
  conterEnd() async {
    emit(CounterEndLoading());
    try {
      endCounter = await counterRepository.endCounter(counter?.id ?? '');
      changeStages(stage: 'end');
      emit(CounterEndSuccess());
      deleteCounter();
    } catch (e) {
      emit(CounterEndFaulire());
    }
  }

  updateClientGender({gender}) async {
    emit(CounterStartLoading());
    log('test$gender');
    try {
      endCounter = await counterRepository.updateGender(gender);
      emit(CounterLoaded());
    } catch (e) {
      emit(CounterError());
    }
  }

  String conunterStage = 'Start';
  changeStages({required String stage}) {
    emit(CounterLoading());
    conunterStage = stage;
    emit(CounterLoaded());
  }
}
