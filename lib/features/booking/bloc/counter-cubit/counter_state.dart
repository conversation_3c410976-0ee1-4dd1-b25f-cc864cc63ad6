part of 'counter_cubit.dart';

sealed class CounterState extends Equatable {
  const CounterState();

  @override
  List<Object> get props => [];
}

final class CounterInitial extends CounterState {}

final class CounterStartLoading extends CounterState {}

final class CounterLoading extends CounterState {}

final class CounterLoaded extends CounterState {}

final class CounterError extends CounterState {}

final class CounterStartSuccess extends CounterState {
  final CounterModel? counter;
  const CounterStartSuccess({required this.counter});
}

final class CounterStartFaulire extends CounterState {}

final class CounterEndLoading extends CounterState {}

final class CounterEndSuccess extends CounterState {}

final class CounterEndFaulire extends CounterState {}
