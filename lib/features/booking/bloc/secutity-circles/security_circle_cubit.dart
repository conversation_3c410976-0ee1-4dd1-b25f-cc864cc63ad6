import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'package:bloc/bloc.dart';
import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

part 'security_circle_state.dart';

class SecurityCircleCubit extends Cubit<SecurityCircleState> {
  SecurityCircleCubit() : super(SecurityCircleInitial());

  subscribe() async {
    Timer.periodic(const Duration(seconds: 5), (timer) async {
      var cityID = globalDriverProfile.user?.city?.id;
      if (cityID != null) {
        await CustomSocket.reSubscribeChannel(channelName: 
            "send-city-users-locations-to-drivers-channel-$cityID",);

        timer.cancel();
      }
    });
  }

  List<PoliceCircleModel> circlesData = [];
  onReceiveLocations(PusherEvents event) {
    var cityID = globalDriverProfile.user?.city?.id;
    if (event.channelName ==
        "send-city-users-locations-to-drivers-channel-$cityID") {
      log("==>> event.data ==>> ${event.data}");
      final messages = event.data['message'];
      if (messages != null) {
        circlesData.clear();
        emit(SecurityCircleLoading());
        for (var loc in messages) {
          circlesData.add(PoliceCircleModel.fromJson(loc));
        }
        createCircles();
        log("Locations:${circlesData.length} Circle::${polliceCircles.length}");
      }
    }
  }

  Set<Circle> polliceCircles = {};
  createCircles() {
    polliceCircles = Set.from(
      circlesData
          .asMap()
          .map((index, latLng) {
            return MapEntry(index, LatLng(latLng.latitude, latLng.longitude));
          })
          .entries
          .toList()
          .map(
            (center) => Circle(
              circleId: CircleId(center.value.toString()),
              radius: 500,
              center: center.value,
              fillColor: center.key == 0
                  ? primaryColor.withOpacity(0.1)
                  : primaryColor.withOpacity(0.35), // purple.withOpacity(0.35)
              strokeColor: Colors.transparent, // purple.withOpacity(0.5)
              onTap: () {},
            ),
          ),
    );
    emit(SecurityCircleLoaded());
  }
}

class PoliceCircleModel {
  final String id;
  final double latitude;
  final double longitude;

  const PoliceCircleModel(
      {required this.id, required this.latitude, required this.longitude,});
  factory PoliceCircleModel.fromJson(Map<String, dynamic> json) {
    return PoliceCircleModel(
        id: json['id'].toString(),
        latitude: double.tryParse(json['latitude'].toString()) ?? 0,
        longitude: double.tryParse(json['longitude'].toString()) ?? 0,);
  }
}
