part of 'ride_request_cubit.dart';

sealed class RideRequestState extends Equatable {
  const RideRequestState();

  @override
  List<Object> get props => [];
}

final class RideRequestInitial extends RideRequestState {}

final class RideNewRequest extends RideRequestState {
  final ComingRideRequest newRequest;

  const RideNewRequest({required this.newRequest});
}

final class RideRequestLoaded extends RideRequestState {}

final class RideTaken extends RideRequestState {
  final dynamic requestID;

  const RideTaken({required this.requestID});
}

final class RideTakenByAnotherDriver extends RideRequestState {
  const RideTakenByAnotherDriver();
}

final class RideRequestloading extends RideRequestState {}

final class RideRequestError extends RideRequestState {}

final class RideRequestCancelled extends RideRequestState {}
