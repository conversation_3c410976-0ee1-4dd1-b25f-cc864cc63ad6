import 'dart:convert';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
// import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/model/accept_request.dart';
import 'package:yalla_gai_driver/features/booking/model/ride_request.dart';

part 'ride_request_state.dart';

class RideRequestCubit extends Cubit<RideRequestState> {
  RideRequestCubit() : super(RideRequestInitial());
  ComingRideRequest? requestData;

  setComingData(NewRequest newRequest) {
    requestData = ComingRideRequest(
      type: newRequest.type,
      userId: newRequest.userId,
      status: newRequest.status,
      carTypeId: newRequest.carId,
      fare: newRequest.fare,
      destinationLocation: newRequest.destinationLocation,
      destinationLatitude: newRequest.destinationLatitude,
      destinationLongitude: newRequest.destinationLongitude,
      pickupLocation: newRequest.pickupLocation,
      pickupLatitude: newRequest.pickupLatitude,
      pickupLongitude: newRequest.pickupLongitude,
      id: newRequest.id,
      updatedAt: DateTime.parse(newRequest.updatedAt.toString()),
      createdAt: DateTime.parse(
        newRequest.createdAt.toString(),
      ),
    );
  }

  newRequestEvent(PusherEvents event) async {
    emit(RideRequestloading());
    log('Trigger');
    try {
      final message = event.data['message'];
      log("connected: Received message - ${message.toString()}");
      if (message != null) {
        log("connected: Received message user id - ${message["user_id"]}");
        // CustomSocket.reSubscribeChannel(
        //   channelName: 'cancel-ride-channel_${message["user_id"]}',
        // );
        CustomSocket.reSubscribeChannel(
          channelName: 'private-cancel-ride-channel_${message["user_id"]}',
        );
        emit(RideRequestLoaded());
        // FlutterOverlayWindow.shareData(message);
        // await FlutterOverlayWindow.showOverlay(
        //   height: 1000,
        //   // width: 100,
        //   enableDrag: true,
        // );
        requestData = ComingRideRequest.fromJson(message);

        emit(RideNewRequest(newRequest: requestData!));
      }
    } catch (e) {
      log("connected: Received e - ${event.channelName} : error : $e");
    }
  }

  accepted({bool emitState = true, PusherEvents? event}) async {
    if (emitState) {
      if (event != null) {
        final data = event.data;
        final driverId = data["message"]?["trip"]?["driver_id"]?.toString();
        if (driverId == globalDriverProfile.user?.id.toString()) {
          CustomSocket.unsubscribeChannel(
            channelName: "accept-request-done-channel_${requestData?.id}",
          );
          CustomSocket.unsubscribeChannel(
            channelName: "private-accept-request-done-channel_${requestData?.id}",
          );
          final requestID = data['new_request_id']?.toString();
          emit(RideTaken(requestID: requestID));
        } else {
          CustomSocket.unsubscribeChannel(
            channelName: "private-cancel-ride-channel_${requestData?.id}",
          );
          emit(RideRequestLoaded());
          requestData = null;
          emit(const RideTakenByAnotherDriver());
        }
      } else {
        emit(const RideTaken(requestID: "requestID"));
      }
    }
  }

  unsubscribe() async {
    CustomSocket.unsubscribeChannel(
      channelName:
          'private-enduser-send-new-request-channel_${globalDriverProfile.user?.id}',
    );
  }

  void requestCancelled(PusherEvents event) {
    requestData = null;
    emit(RideRequestCancelled());
      print('rrequestCancelled calling --> ${globalDriverProfile.user?.id}');

    CustomSocket.subscribeNewReqChannel(globalDriverProfile.user?.id ?? '');
  }
}
