import 'dart:developer';

import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/core/error/exception.dart';
import 'package:yalla_gai_driver/features/booking/model/accept_request.dart';
import 'package:yalla_gai_driver/features/booking/model/reject_reason.dart';
import 'package:yalla_gai_driver/features/booking/model/ride_status.dart';

import '../../model/ride_request.dart';
import '../../repository/trip_repository.dart';

part 'trip_event.dart';
part 'trip_state.dart';

class TripBloc extends Bloc<TripEvent, TripState> {
  TripRepository tripRepository = TripRepository();
  RequestAccept requestAccept = RequestAccept();
  List<TripCancelReason> reasons = [];

  TripBloc() : super(TripInitial()) {
    ////// Trip Get Start Trip
    on<TripGetStartTrip>((event, emit) async {
      try {
        emit(TripStartLoading());
        await tripRepository.getStartedTrip(event.newRequestId);
        emit(const TripStartSuccess());
      } catch (e) {
        emit(TripStartFaulire(message: e.toString()));
      }
    });
    ////// Trip Get cancel reason
    on<RejectTripReason>((event, emit) async {
      try {
        emit(RejectReasonLoading());
        reasons = await tripRepository.getTripCancelReason();
        emit(RejectReasonLoaded(reasons: reasons));
      } catch (e) {
        emit(TripStartFaulire(message: e.toString()));
      }
    });
    ////// Trip End Trip
    on<TripEndTrip>((event, emit) async {
      try {
        emit(TripEndTripLoading());
        await tripRepository.endTrip(event.newRequestId);
        emit(const TripEndTripSuccess());
      } catch (e) {
        emit(TripEndTripFaulire(message: e.toString()));
      }
    });
    ////// get correct state
    on<GetCorrectState>((event, emit) async {
      try {
        emit(TripEndTripLoading());
        if (event.status == 2) {
          emit(ArrivedAtPickup());
        } else if (event.status == 3) {
          emit(TripStarted());
        }
      } catch (e) {
        emit(TripEndTripFaulire(message: e.toString()));
      }
    });
    ////// change trip status
    on<ChangeTripStatus>((event, emit) async {
      log("Change trip status");
      log("trip status: ${event.status}, tripId: ${event.tripID}");
      try {
        emit(ChangeTripStatusLoading());
        final tripStatus = await tripRepository.changeTripStatus(tripId: event.tripID, status: event.status);
        switch (event.status) {
          case 2:
            emit(ArrivedAtPickup());
            break;
          case 3:
            emit(TripStarted());
            break;

          /// get trip id from here
          case 4:
            emit(TripEnd(tripStatus: tripStatus));

            SharedPreferences sharedPreferences = await SharedPreferences.getInstance();
            bool neverAskReview = sharedPreferences.getBool("never_ask_review") ?? false;
            if (!neverAskReview) {
              sharedPreferences.setBool("ask_review", true);
            }

            break;
          default:
        }
      } catch (e) {
        emit(ChangeTripStatusFailure(message: e.toString()));
      }
    });
    ////// rate cunstomer
    on<TripRateCustomer>((event, emit) async {
      try {
        log("Rating...");
        log("requestId: ${event.requestId}, rating: ${event.rating}");
        emit(const TripRateCustomerLoading());
        await tripRepository.rateCustomer(event.requestId, event.rating, event.message);
        requestAccept = RequestAccept();

        emit(const TripRateCustomerSuccess());
      } catch (e) {
        emit(TripRateCustomerFaulire(message: e.toString()));
      }
    });
    ////// reject trip
    on<RejectTrip>((event, emit) async {
      try {
        emit(TripRejecting());
        await tripRepository.rejectTrip(event.tripID.toString(), reason: event.reason, reasonID: event.cancelReasonID);
        emit(TripRejected());
      } catch (e) {
        log(e.toString());
        emit(TripRejectError(error: e.toString()));
      }
    });
    ////// Trip End Trip
    on<TripGetNewRequest>((event, emit) async {
      try {
        emit(TripGetNewRequestLoading());
        List<ComingRideRequest> rideRequests = await tripRepository.getPreviousTrips();
        emit(TripGetNewRequestSuccess(rideRequests: rideRequests));
      } catch (e) {
        emit(TripGetNewRequestFaulire(message: e.toString()));
      }
    });
    on<GetActiveTrip>((event, emit) async {
      try {
        emit(TripGetNewRequestLoading());
        requestAccept = await tripRepository.getActiveTrip();
        emit(TripAcceptNewRequestSuccess(requestAccept: requestAccept));
      } catch (e) {
        emit(TripGetNewRequestFaulire(message: e.toString()));
      }
    });
    ////// Trip End Trip
    on<TripAcceptNewRequest>((event, emit) async {
      try {
        emit(TripAcceptNewRequestLoading());
        requestAccept = await tripRepository.acceptNewRequest(event.tripId);
        emit(TripAcceptNewRequestSuccess(requestAccept: requestAccept));
      } catch (e) {
        emit(TripAcceptNewRequestFaulire(message: e.toString()));
      }
    });
    ////// Store Radius
    on<TripStoreRadius>((event, emit) async {
      try {
        emit(TripStoreRadiusLoading());
        await tripRepository.storeRadius(event.radius);
        emit(const TripStoreRadiusSuccess());
      } catch (e) {
        emit(TripStoreRadiusFaulire(message: e.toString()));
      }
    });
    ////// Trip Update Location
    on<TripUpdateLocation>((event, emit) async {
      try {
        await tripRepository.updateLocation(
          lat: event.lat,
          lng: event.lng,
          direction: event.direction,
          tripId: requestAccept.trip?.id.toString(),
          byGoogle: event.bygoogle,
        );
      } on UnauthorizedException catch (e) {
        emit(const TripUserUnauthorized());
        log("User unauthorized $e");
      } catch (e) {
        log("location:$e");
      }
    });
    ////// Trip Update Location
    on<TripComingRideRequest>((event, emit) {
      emit(TripComingRideRequestSuccess(rideRequest: event.rideRequest));
    });
  }
}
