part of 'trip_bloc.dart';

abstract class TripState extends Equatable {
  const TripState();

  @override
  List<Object> get props => [];
}

final class TripInitial extends TripState {}

final class TripStartLoading extends TripState {}

final class TripRejecting extends TripState {}

final class TripRejectError extends TripState {
  final String error;

  const TripRejectError({required this.error});
}

final class TripRejected extends TripState {}

final class TripStartSuccess extends TripState {
  const TripStartSuccess();
}

final class TripStartFaulire extends TripState {
  final String message;
  const TripStartFaulire({required this.message});
}

final class TripEndTripLoading extends TripState {}

final class TripEndTripSuccess extends TripState {
  const TripEndTripSuccess();
}

final class TripEndTripFaulire extends TripState {
  final String message;
  const TripEndTripFaulire({required this.message});
}

final class TripGetNewRequestLoading extends TripState {}

final class TripGetNewRequestSuccess extends TripState {
  final List<ComingRideRequest> rideRequests;
  const TripGetNewRequestSuccess({required this.rideRequests});
}

final class TripGetNewRequestFaulire extends TripState {
  final String message;
  const TripGetNewRequestFaulire({required this.message});
}

final class TripAcceptNewRequestLoading extends TripState {}

final class TripAcceptNewRequestSuccess extends TripState {
  final RequestAccept requestAccept;
  const TripAcceptNewRequestSuccess({required this.requestAccept});
}

final class TripAcceptNewRequestFaulire extends TripState {
  final String message;
  const TripAcceptNewRequestFaulire({required this.message});
}

final class TripStoreRadiusLoading extends TripState {}

final class TripStoreRadiusSuccess extends TripState {
  const TripStoreRadiusSuccess();
}

final class TripUserUnauthorized extends TripState {
  const TripUserUnauthorized();
}

final class TripStoreRadiusFaulire extends TripState {
  final String message;
  const TripStoreRadiusFaulire({required this.message});
}

final class TripUpdateLocationLoading extends TripState {}

final class TripUpdateLocationSuccess extends TripState {
  const TripUpdateLocationSuccess();
}

final class TripComingRideRequestSuccess extends TripState {
  final ComingRideRequest rideRequest;
  const TripComingRideRequestSuccess({required this.rideRequest});
}

final class TripUpdateLocationFaulire extends TripState {
  final String message;
  const TripUpdateLocationFaulire({required this.message});
}

final class TripRateCustomerLoading extends TripState {
  const TripRateCustomerLoading();
}

final class RejectReasonLoading extends TripState {}

final class RejectReasonLoaded extends TripState {
  final List<TripCancelReason> reasons;
  const RejectReasonLoaded({required this.reasons});
}

final class TripRateCustomerSuccess extends TripState {
  const TripRateCustomerSuccess();
}

final class TripRateCustomerFaulire extends TripState {
  final String message;
  const TripRateCustomerFaulire({required this.message});
}

final class ChangeTripStatusLoading extends TripState {}

final class ArrivedAtPickup extends TripState {}

final class TripStarted extends TripState {}

final class TripEnd extends TripState {
  final TripStatus tripStatus;

  const TripEnd({required this.tripStatus});
}

final class ChangeTripStatusFailure extends TripState {
  final String message;
  const ChangeTripStatusFailure({required this.message});
}
