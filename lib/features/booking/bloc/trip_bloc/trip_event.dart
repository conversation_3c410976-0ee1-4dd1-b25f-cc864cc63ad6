part of 'trip_bloc.dart';

abstract class TripEvent extends Equatable {
  const TripEvent();
}

class TripGetStartTrip extends TripEvent {
  final String newRequestId;

  const TripGetStartTrip({required this.newRequestId});

  @override
  List<Object?> get props => [newRequestId];
}

class ChangeTripStatus extends TripEvent {
  final String tripID;
  final int status;

  const ChangeTripStatus({required this.tripID, required this.status});

  @override
  List<Object?> get props => [tripID, status];
}

class RejectTrip extends TripEvent {
  final dynamic tripID, cancelReasonID, reason;

  const RejectTrip({required this.tripID, required this.cancelReasonID, required this.reason});

  @override
  List<Object?> get props => [tripID];
}

class RejectTripReason extends TripEvent {
  const RejectTripReason();

  @override
  List<Object?> get props => [];
}

class TripRateCustomer extends TripEvent {
  final String requestId;
  final String rating;
  final String message;

  const TripRateCustomer({required this.rating, required this.requestId, required this.message});

  @override
  List<Object?> get props => [requestId, message];
}

class TripEndTrip extends TripEvent {
  final String newRequestId;

  const TripEndTrip({required this.newRequestId});

  @override
  List<Object?> get props => [];
}

class TripStoreRadius extends TripEvent {
  final String radius;

  const TripStoreRadius({required this.radius});

  @override
  List<Object?> get props => [];
}

class TripGetNewRequest extends TripEvent {
  const TripGetNewRequest();

  @override
  List<Object?> get props => [];
}

class TripComingRideRequest extends TripEvent {
  final ComingRideRequest rideRequest;

  const TripComingRideRequest({required this.rideRequest});

  @override
  List<Object?> get props => [];
}

class TripAcceptNewRequest extends TripEvent {
  final String tripId;

  const TripAcceptNewRequest({required this.tripId});

  @override
  List<Object?> get props => [];
}

class GetActiveTrip extends TripEvent {
  @override
  List<Object?> get props => [];
}

class GetCorrectState extends TripEvent {
  final int status;

  const GetCorrectState({required this.status});

  @override
  List<Object?> get props => [];
}

class TripUpdateLocation extends TripEvent {
  final String lat;
  final String lng;
  final String direction;
  final bool bygoogle;

  const TripUpdateLocation({
    required this.lat,
    this.bygoogle = true,
    required this.lng,
    required this.direction,
  });

  @override
  List<Object?> get props => [];
}
