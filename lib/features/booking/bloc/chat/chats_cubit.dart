import 'dart:convert';
import 'dart:developer';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:pusher_channels_flutter/pusher_channels_flutter.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/notification/custom_local_notification.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/booking/model/chat.dart';
import 'package:yalla_gai_driver/features/booking/repository/chat_remote_datasource.dart';

part 'chats_state.dart';

class ChatsCubit extends Cubit<ChatsState> {
  ChatsRemoteDatasource chatsRemoteDatasource = ChatsRemoteDatasource();

  ChatsCubit() : super(ChatsInitialState());
  List<ChatMessage> chats = [];
  int unreadCount = 0;
  final chatController = TextEditingController();
  final scrollController = ScrollController();

  clearMessages() {
    emit(ChatsLoadingState());
    chats.clear();
    emit(ChatsLoadedState());
  }

  sendChat({required driverID}) async {
    final message = chatController.text.trim();
    chatController.clear();

    if (message == "") {
      return;
    }

    try {
      emit(ChatsSendingState());
      final response = await chatsRemoteDatasource.sendChat(message: message, receiverID: driverID);
      log("Chat status code::${response.statusCode}\n${response.body}");
      if (response.statusCode == 200) {
        final data = json.decode(response.body)['message'];
        chats.add(ChatMessage.fromJson(data));
        emit(ChatsLoadedState());
        if (scrollController.hasClients) {
          scrollController.animateTo(
            0,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      } else {
        emit(ChatsErrorState());
      }
    } catch (err) {
      emit(ChatsErrorState());
      log("Chat error::$err");
    }
    log(chats.length.toString());
  }

  fetchChat({required driverID}) async {
    /*if (chats.isNotEmpty) {
      return;
    }
    try {
      emit(ChatsLoadingState());
      final response =
          await chatsRemoteDatasource.fetchChat(receiverID: driverID);
      log("Chat status code::${response.statusCode}\n${response.body}");
      if (response.statusCode == 200) {
        var messages = json.decode(response.body)['messages'];
        for (var message in messages) {
          chats.add(ChatMessage.fromJson(message));
        }
        chats.sort((a, b) => DateTime.parse(a.createdAt.toString())
            .compareTo(DateTime.parse(b.createdAt.toString())));
        emit(ChatsLoadedState());
      } else {
        emit(ChatsErrorState());
      }
    } catch (err) {
      emit(ChatsErrorState());
      log("Chat error::$err");
    }
    log(chats.length.toString());*/
  }

  newChatEvent(PusherEvents event) async {
    String userId = LocalStorage.instance.userId ?? '';

    log("chat Event::${event.channelName}");
    log("chat Event data::${event.data}");
    final data = event.data['message'];
    if (data != null) {
      final message = ChatMessage.fromJson(data);
      log("sender id:${message.senderId}");
      log("receiver id:${message.receiverId}");
      log("user id:$userId");
      if (message.senderId == userId || message.receiverId == userId) {
        emit(ChatsLoadingState());
        chats.add(message);
        chats.sort((a, b) => DateTime.parse(a.createdAt.toString()).compareTo(DateTime.parse(b.createdAt.toString())));
        unreadCount++;
        emit(ChatsLoadedState());
        if (message.receiverId == userId) {
          CustomLocalNotification.showChatNotification(message);
        }
      }
      if (scrollController.hasClients) {
        scrollController.animateTo(
          0,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    }
  }

  clearUnreadCount() async {
    emit(ChatsLoadingState());
    unreadCount = 0;
    emit(ChatsLoadedState());
  }
}
