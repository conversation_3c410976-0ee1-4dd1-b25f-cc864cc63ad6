part of 'chats_cubit.dart';

sealed class ChatsState extends Equatable {
  const ChatsState();

  @override
  List<Object> get props => [];
}

final class ChatsInitialState extends ChatsState {}

final class ChatsLoadingState extends ChatsState {}

final class ChatsSendingState extends ChatsState {}

final class ChatsLoadedState extends ChatsState {}

final class ChatsErrorState extends ChatsState {}
