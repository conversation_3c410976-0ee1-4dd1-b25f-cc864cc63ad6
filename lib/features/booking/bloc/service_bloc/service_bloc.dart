import 'package:equatable/equatable.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/model/service.dart';

import '../../repository/service_repository.dart';

part 'service_event.dart';
part 'service_state.dart';

class ServiceBloc extends Bloc<ServiceEvent, ServiceState> {
  ServiceRepository serviceRepository = ServiceRepository();

  ServiceBloc() : super(ServiceInitial()) {
    //////ServiceGetService
    on<ServiceGetService>((event, emit) async {
      try {
        emit(ServiceGetServiceLoading());
        List<Service>? services = await serviceRepository.getServices();
        emit(ServiceGetServiceSuccess(services: services));
      } catch (e) {
        emit(ServiceGetServiceFaulire(message: e.toString()));
      }
    });
    ////// CounterStartCounter
    on<ServiceChangeStatus>((event, emit) async {
      try {
        emit(ServiceChangeStatusLoading());
        await serviceRepository.changeServiceStatus(event.id, event.status);
        globalDriverProfile.changeAvailabilityStatus(event.status == "on");
        emit(ServiceChangeStatusSuccess(event.status));
      } catch (e) {
        emit(ServiceChangeStatusFaulire(message: e.toString()));
      }
    });
  }
}
