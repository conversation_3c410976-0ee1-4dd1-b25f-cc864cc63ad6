part of 'service_bloc.dart';

abstract class ServiceEvent extends Equatable {
  const ServiceEvent();
}

class ServiceGetService extends ServiceEvent {
  const ServiceGetService();

  @override
  List<Object?> get props => [];
}

class ServiceChangeStatus extends ServiceEvent {
  final String status;
  final String? id;

  const ServiceChangeStatus({this.id, required this.status});

  @override
  List<Object?> get props => [];
}
