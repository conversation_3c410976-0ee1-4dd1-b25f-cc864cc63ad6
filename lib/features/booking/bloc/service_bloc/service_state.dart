part of 'service_bloc.dart';

abstract class ServiceState extends Equatable {
  const ServiceState();

  @override
  List<Object> get props => [];
}

final class ServiceInitial extends ServiceState {}

final class ServiceGetServiceLoading extends ServiceState {}

final class ServiceGetServiceSuccess extends ServiceState {
  final List<Service> services;
  const ServiceGetServiceSuccess({required this.services});
}

final class ServiceGetServiceFaulire extends ServiceState {
  final String message;
  const ServiceGetServiceFaulire({required this.message});
}

final class ServiceChangeStatusLoading extends ServiceState {}

final class ServiceChangeStatusSuccess extends ServiceState {
  final String status;
  const ServiceChangeStatusSuccess(this.status);
}

final class ServiceChangeStatusFaulire extends ServiceState {
  final String message;
  const ServiceChangeStatusFaulire({required this.message});
}
