import 'dart:developer';
import 'package:flutter/services.dart';

class CustomInputFormatter extends TextInputFormatter {
  //provide the seperator (" ",/)
  // default is /
  final String seperator;
  //insert the seperator for every lenght specify here
  // default is 2
  final int forEvery;
  const CustomInputFormatter({this.seperator = "/", this.forEvery = 2});
  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    var text = newValue.text;
    text = text.replaceAll(RegExp(seperator), "");
    log(text);
    if (newValue.selection.baseOffset == 0) {
      return newValue;
    }

    var buffer = StringBuffer();
    for (int i = 0; i < text.length; i++) {
      buffer.write(text[i]);
      var nonZeroIndex = i + 1;
      if (nonZeroIndex % forEvery == 0 && nonZeroIndex != text.length) {
        buffer.write(
            seperator); // Replace this with anything you want to put after each 4 numbers
      }
    }

    var string = buffer.toString();
    return newValue.copyWith(
        text: string,
        selection: TextSelection.collapsed(offset: string.length));
  }
}

// class CardExpInputFormatter extends TextInputFormatter {
//   @override
//   TextEditingValue formatEditUpdate(
//       TextEditingValue oldValue, TextEditingValue newValue) {
//     var text = newValue.text;
//     text = text.replaceAll(RegExp("/"), "");
//     log(text);
//     if (newValue.selection.baseOffset == 0) {
//       return newValue;
//     }

//     var buffer = StringBuffer();
//     if (text.length > 2) {
//       var firstTwo = text.substring(0, 2);
//       var lastDigit = text.substring(2, text.length);
//       buffer.write("$firstTwo/$lastDigit");
//     } else {
//       for (int i = 0; i < text.length; i++) {
//         buffer.write(text[i]);
//         var nonZeroIndex = i + 1;
//         if (nonZeroIndex % 2 == 0 && nonZeroIndex != text.length) {
//           buffer.write(
//               "/"); // Replace this with anything you want to put after each 4 numbers
//         }
//       }
//     }

//     var string = buffer.toString();
//     return newValue.copyWith(
//         text: string,
//         selection: TextSelection.collapsed(offset: string.length));
//   }
// }
