import 'location_entity.dart';

class RideRequest {
  final String name;
  final String id;
  final String fare;
  final String profilePicture;
  final LocationEntity userPickUpLocation;
  final LocationEntity? userDropLocation;

  RideRequest({
    required this.name,
    required this.fare,
    required this.id,
    required this.profilePicture,
    required this.userPickUpLocation,
    required this.userDropLocation,
  });
}
