import 'dart:developer';
import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';



class CardValidations {
  late BuildContext _context;
  CardValidations.of(BuildContext context) {
    _context = context;
  }

  String? validateEmail(String? email) {
    String pattern =
        r"^[a-zA-Z0-9.a-zA-Z0-9.!#$%&'*+-/=?^_`{|}~]+@[a-zA-Z0-9]+\.[a-zA-Z]+";
    RegExp regex = RegExp(pattern);
    if (email == null) {
      return AppLocalizations.of(_context)!.enter_email_address;
    }
    if (!regex.hasMatch(email)) {
      return AppLocalizations.of(_context)!.incorrect;
    }
    return null;
  }

  String? cardName(String? name) {
    if (name == null || name == "") {
      return AppLocalizations.of(_context)!.incorrect;
    }

    return null;
  }

  String? cardNumber(String? number) {
    if (number == null || number == "") {
      return AppLocalizations.of(_context)!.incorrect;
    }
    return null;
  }

  String? cvv(String? cvv) {
    if (cvv == null || cvv.length < 3) {
      return AppLocalizations.of(_context)!.incorrect;
    }
    return null;
  }

  String? expDate(String? date) {
    if (date == null || date.length < 5) {
      return AppLocalizations.of(_context)!.incorrect;
    }
    var month = int.tryParse(date.split("/").first) ?? 0;
    var year = int.tryParse(
            "${DateTime.now().year.toString().substring(0, 2)}${date.split("/").last}") ??
        0;
        log(year.toString());
    if (month > 12 || month < 1) {
      return AppLocalizations.of(_context)!.invalid_month;
    }
    var cardExpDate = DateTime(year, month);
    if (DateTime.now().isAfter(cardExpDate)) {
      return AppLocalizations.of(_context)!.card_expired;
    }

    return null;
  }
}
