import 'dart:typed_data';

import 'package:crop_your_image/crop_your_image.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


import '../../../core/routes/paths.dart' as path;
import '../../../core/shared_widgets/app_scafold.dart';
import '../../../core/utils/colors.dart';

class ImageCropperScreen extends StatefulWidget {
  const ImageCropperScreen({super.key, required this.image});

  final Uint8List image;

  @override
  State<ImageCropperScreen> createState() => _ImageCropperScreenState();
}

class _ImageCropperScreenState extends State<ImageCropperScreen> {
  final _cropController = CropController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context)!;
    return AppScaffold(
        appBar: AppBar(
          backgroundColor: white,
          centerTitle: true,
          forceMaterialTransparency: true,
          leading: GestureDetector(
            onTap: () {
              context.push(path.languageSetting);
            },
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 3.w),
              child: Image.asset(
                "assets/images/account/language.png",
                height: 25.h,
              ),
            ),
          ),
          title: Text(
            localizations.crop_your_image,
            style: Theme.of(context).textTheme.displayMedium,
          ),
          actions: [],
        ),
        body: Container(
          width: double.infinity,
          height: double.infinity,
          child: Center(
            child: Column(
              children: [
                Expanded(
                  child: Stack(
                    children: [
                      Crop(
                        willUpdateScale: (newScale) => newScale < 5,
                        controller: _cropController,
                        image: widget.image,
                        onCropped: (result) {
                          switch (result) {
                            case CropSuccess(:final croppedImage):
                              Navigator.pop(context, croppedImage);
                            case CropFailure(:final cause):
                              showDialog(
                                context: context,
                                builder: (context) => AlertDialog(
                                  title: Text('Error'),
                                  content:
                                      Text('Failed to crop image: ${cause}'),
                                  actions: [
                                    TextButton(
                                        onPressed: () => Navigator.pop(context),
                                        child: Text(localizations.okay)),
                                  ],
                                ),
                              );
                          }
                        },
                        withCircleUi: true,
                        cornerDotBuilder: (size, edgeAlignment) =>
                            const SizedBox.shrink(),
                        interactive: true,
                        fixCropRect: true,
                        radius: 20,
                        initialRectBuilder: InitialRectBuilder.withBuilder(
                          (viewportRect, imageRect) {
                            return Rect.fromLTRB(
                              viewportRect.left + 24,
                              viewportRect.top + 24,
                              viewportRect.right - 24,
                              viewportRect.bottom - 24,
                            );
                          },
                        ),
                      ),
                      IgnorePointer(
                        child: Padding(
                          padding: const EdgeInsets.all(24),
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border.all(width: 4, color: Colors.white),
                              borderRadius: BorderRadius.circular(20),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      const SizedBox(height: 16),
                      Container(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () {
                            _cropController.cropCircle();
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            child: Text(localizations.crop_it),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
        ));
  }
}
