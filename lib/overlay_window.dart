import 'dart:async';
import 'package:flutter/services.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

// import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'dart:developer';
import 'package:yalla_gai_driver/core/shared_widgets/custom_divider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/core/constants/app_utils.dart';
import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/booking/domain/entity/location_entity.dart';
import 'package:yalla_gai_driver/features/booking/domain/entity/ride_request.dart';
import 'package:yalla_gai_driver/features/booking/model/ride_request.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/pick_and_drop_points.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

class OverlayWindow extends StatefulWidget {
  const OverlayWindow({super.key, required this.rideRequest});
  final ComingRideRequest rideRequest;

  @override
  State<OverlayWindow> createState() => _OverlayWindowState();
}

class _OverlayWindowState extends State<OverlayWindow> {
  bool startButtonOn = false;
  bool cancelButtonOn = true;
  late int countdown;
  Color textColor = primaryColor;
  Timer? timer;

  final platform = MethodChannel('com.yallagai.captain/channel');

  @override
  void initState() {
    super.initState();
    if (mounted) {
      startButtonOn =
          context.read<TripBloc>().requestAccept.trip?.status.toString() == "2";
    }
    countdown = AppUtils.waitTime.toInt();
    CustomSocket.reSubscribeChannel(
      channelName: 'private-user-send-message-channel_${globalDriverProfile.user?.id}',
    );
    startCountdown();
  }

  @override
  void dispose() {
    timer?.cancel();
    super.dispose();
  }

  void startCountdown() {
    timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (countdown > 0) {
        if (mounted) {
          setState(() {
            countdown--;
            if (countdown <= 5) {
              textColor = Colors.red;
            }
          });
        }
      } else {
        // FlutterOverlayWindow.closeOverlay();
        timer.cancel();
        setState(() {
          cancelButtonOn = false;
        });
      }
    });
  }

  String formatSeconds(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;

    String minutesStr = minutes.toString().padLeft(2, '0');
    String secondsStr = remainingSeconds.toString().padLeft(2, '0');

    return '$minutesStr:$secondsStr';
  }

  @override
  Widget build(BuildContext context) {
    return tripNotStartedWidget();
  }

  Widget tripNotStartedWidget() {
    LocationEntity pickUpLocation = LocationEntity(
      locationText: widget.rideRequest.pickupLocation ?? "",
      latitude: widget.rideRequest.pickupLatitude,
      longitude: widget.rideRequest.pickupLongitude,
      streetName: null,
    );

    LocationEntity? dropLocation =
        widget.rideRequest.destinationLatitude != null &&
                widget.rideRequest.destinationLongitude != null
            ? LocationEntity(
                locationText: widget.rideRequest.destinationLocation ?? "",
                latitude: widget.rideRequest.destinationLatitude!,
                longitude: widget.rideRequest.destinationLongitude!,
                streetName: null,
              )
            : null;

    RideRequest rideRequest = RideRequest(
      fare: widget.rideRequest.fare ?? "",
      id: widget.rideRequest.id ?? "",
      name: "John Doe",
      profilePicture: "https://example.com/profile_picture.jpg",
      userPickUpLocation: pickUpLocation,
      userDropLocation: dropLocation,
    );
    return Container(
      decoration:
          BoxDecoration(color: white, borderRadius: BorderRadius.circular(20)),
      padding: EdgeInsets.all(10),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Gap(w: 1),
          Row(
            children: [
              Gap(w: 6),
              Icon(Icons.timer, color: textColor),
              Gap(w: 2),
              Text(
                formatSeconds(countdown),
                style: Theme.of(context).textTheme.bodySmall!.copyWith(
                      color: textColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
              ),
              Gap(w: 10),
              Text(
                AppLocalizations.of(context)!.new_request,
                style: Theme.of(context)
                    .textTheme
                    .bodyMedium!
                    .copyWith(fontWeight: FontWeight.bold, fontSize: 16),
              ),
            ],
          ),
          const Gap(h: 2),
          const CustomDivider(),
          const Gap(h: 2),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  const Icon(Icons.access_time_filled, size: 30),
                  Gap(w: 2),
                  Text(
                    AppLocalizations.of(context)!.now,
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(fontWeight: FontWeight.w600, fontSize: 15),
                  ),
                ],
              ),
              Container(
                height: 10,
                width: 1,
                color: Colors.black.withOpacity(0.1),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  const Icon(Icons.money, size: 30),
                  Gap(w: 2),
                  Text(
                    AppLocalizations.of(context)!.cash,
                    style: Theme.of(context)
                        .textTheme
                        .bodyMedium!
                        .copyWith(fontWeight: FontWeight.w600, fontSize: 15),
                  ),
                ],
              ),
            ],
          ),
          const Gap(h: 2),
          const CustomDivider(),
          const Gap(h: 2),
          Row(
            spacing: 5,
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Flexible(
                child: Text(
                  "North Atlantic Ocean",
                  // rideRequest.userPickUpLocation.locationText,
                  maxLines: 2,
                  textAlign: TextAlign.center,
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontSize: 12,
                        color: primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
              Expanded(child: Divider()),
              Flexible(
                child: Text(
                  "Alameer Hassan, Alameer Hassan, Irbid",
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                  // rideRequest.userDropLocation?.locationText ?? "",
                  style: Theme.of(context).textTheme.bodySmall!.copyWith(
                        fontSize: 12,
                        color: primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                ),
              ),
            ],
          ),
          Gap(h: 15),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            spacing: 10,
            children: [
              // CustomButton(
              //   width: 34,
              //   height: 5,
              //   buttonText: AppLocalizations.of(context)!.reject,
              //   borderRadius: 14,
              //   borderOnly: true,
              //   onPressed: () {
              //     // context.read<RideRequestCubit>().accepted(emitState: false);
              //     // timer?.cancel();
              //     // context.go(pathome);
              //   },
              // ),
              Flexible(
                child: GestureDetector(
                  // onTap: () => FlutterOverlayWindow.closeOverlay(),
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: white,
                      border: Border.all(
                        color: primaryColor,
                        width: 1,
                      ),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Center(
                            child: Text(
                              AppLocalizations.of(context)!.reject,
                              textAlign: TextAlign.center,
                              style: Theme.of(context)
                                  .textTheme
                                  .labelMedium!
                                  .copyWith(
                                    color: primaryColor,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

              Flexible(
                child: GestureDetector(
                  onTap: openMainAppFromOverlay,
                  child: DecoratedBox(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10),
                      color: primaryColor,
                    ),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Center(
                            child: Text(
                              AppLocalizations.of(context)!.accept,
                              textAlign: TextAlign.center,
                              style: Theme.of(context)
                                  .textTheme
                                  .labelMedium!
                                  .copyWith(
                                    color: white,
                                  ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              // CustomButton(
              //   width: 34,
              //   height: 5,
              //   buttonText: AppLocalizations.of(context)!.accept,

              //   borderRadius: 14,
              //   onPressed: () {
              //     // BlocProvider.of<TripBloc>(context)
              //     //     .add(TripAcceptNewRequest(tripId: widget.rideRequest.id));
              //     // widget.stopRingtone();
              //     // timer?.cancel();
              //   },
              // ),
            ],
          ),
          Gap(h: 16 + MediaQuery.paddingOf(context).bottom),
        ],
      ),
    );
  }

  void openMainAppFromOverlay() async {
    // const url = 'myapp://open';
    // if (await canLaunchUrl(Uri.parse(url))) {
    //   await launchUrl(Uri.parse(url));
    // } else {
    //   debugPrint('Cannot launch $url');
    // }
    // final platform = MethodChannel('overlay_channel');
    try {
      // Ask main to start overlay isolate
      await platform.invokeMethod('launchOverlayIsolate');

      // Give it time (or listen for a callback)
      await Future.delayed(Duration(seconds: 3));

      // Finally, launch main app via native
      await platform.invokeMethod('openMainApp');
    } catch (e) {
      // Handle error
      log('Error opening main app from overlay: $e');
    }
  }
}
