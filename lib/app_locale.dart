import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/utils/uti.dart';
import 'package:yalla_gai_driver/l10n/l10.dart';

class AppLocale extends ChangeNotifier {
  AppLocale() {
    init();
  }

  init() async {
    languageCode = Utils.phoneLocale();
  }

  String languageCode = 'en';
  late Locale _locale = (Locale(languageCode));

  Locale get locale => _locale;

  void setLocale(Locale local) {
    if (!L10n.all.contains(local)) return;
    _locale = local;
    languageCode = local.languageCode;
    LocalStorage.instance.languageCode = local.languageCode;
    notifyListeners();
  }
}
