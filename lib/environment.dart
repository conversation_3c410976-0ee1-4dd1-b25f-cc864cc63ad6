abstract interface class AppEnvironment {
  static String apiBaseUrl = "API_BASE_URL";
  // static String apiBaseUrlTest = "API_BASE_URL_TEST";
  static String publicBaseUrl = "PUBLIC_BASE_URL";
  // static String publicBaseUrlTest = "PUBLIC_BASE_URL_TEST";
  static String googleMapsApiKey = "GOOGLE_MAPS_API_KEY";
  static String macSecretKey = "REPLACE_SECRET_HERE";
  // static String macSecretKeyTest = "REPLACE_SECRET_HERE_TEST";
  static String stripePublicKey = "STRIPE_PUBLIC_KEY";
  static String stripeSecretKey = "STRIPE_SECRET_KEY";
  static String pusherApiKey = "PUSHER_API_KEY";
  static String pusherCluster = "PUSHER_CLUSTER";
  static String websocketUrl = 'WEBSOCKET_URL';
  static String authSocketUrl = 'AUTH_SOCKET';
  static bool apiEnvLocal = false;
}
