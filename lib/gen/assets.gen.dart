/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/widgets.dart';

class $AssetsImagesGen {
  const $AssetsImagesGen();

  $AssetsImagesAccountGen get account => const $AssetsImagesAccountGen();

  /// File path: assets/images/alert_location.svg
  String get alertLocation => 'assets/images/alert_location.svg';

  /// File path: assets/images/arrow-square-left.svg
  String get arrowSquareLeft => 'assets/images/arrow-square-left.svg';

  /// File path: assets/images/back_button.svg
  String get backButton => 'assets/images/back_button.svg';

  /// File path: assets/images/big_arc.svg
  String get bigArc => 'assets/images/big_arc.svg';

  /// File path: assets/images/box.png
  AssetGenImage get box => const AssetGenImage('assets/images/box.png');

  /// File path: assets/images/cancel_icon.svg
  String get cancelIcon => 'assets/images/cancel_icon.svg';

  /// File path: assets/images/carMarker.png
  AssetGenImage get carMarker =>
      const AssetGenImage('assets/images/carMarker.png');

  /// File path: assets/images/card_example.svg
  String get cardExample => 'assets/images/card_example.svg';

  /// File path: assets/images/cash_icon.svg
  String get cashIcon => 'assets/images/cash_icon.svg';

  /// File path: assets/images/celebrate_people.svg
  String get celebratePeople => 'assets/images/celebrate_people.svg';

  $AssetsImagesConnectGen get connect => const $AssetsImagesConnectGen();

  /// File path: assets/images/coupon_icon.svg
  String get couponIcon => 'assets/images/coupon_icon.svg';

  /// File path: assets/images/credit_card_icon.svg
  String get creditCardIcon => 'assets/images/credit_card_icon.svg';

  /// File path: assets/images/deluxe_car.png
  AssetGenImage get deluxeCar =>
      const AssetGenImage('assets/images/deluxe_car.png');

  /// File path: assets/images/destMarker.png
  AssetGenImage get destMarker =>
      const AssetGenImage('assets/images/destMarker.png');

  /// File path: assets/images/divider_icon.svg
  String get dividerIcon => 'assets/images/divider_icon.svg';

  /// File path: assets/images/down_arrow.svg
  String get downArrow => 'assets/images/down_arrow.svg';

  $AssetsImagesDriverGen get driver => const $AssetsImagesDriverGen();

  /// File path: assets/images/e.png
  AssetGenImage get e => const AssetGenImage('assets/images/e.png');

  /// File path: assets/images/edit_icon.svg
  String get editIcon => 'assets/images/edit_icon.svg';

  /// File path: assets/images/exch_icon.svg
  String get exchIcon => 'assets/images/exch_icon.svg';

  /// File path: assets/images/female_icon_selected.svg
  String get femaleIconSelected => 'assets/images/female_icon_selected.svg';

  /// File path: assets/images/female_icon_unselected.svg
  String get femaleIconUnselected => 'assets/images/female_icon_unselected.svg';

  /// File path: assets/images/female_user.png
  AssetGenImage get femaleUser =>
      const AssetGenImage('assets/images/female_user.png');

  /// File path: assets/images/gift_offer.svg
  String get giftOffer => 'assets/images/gift_offer.svg';

  /// File path: assets/images/green_wallet_icon.svg
  String get greenWalletIcon => 'assets/images/green_wallet_icon.svg';

  /// File path: assets/images/groupart.png
  AssetGenImage get groupart =>
      const AssetGenImage('assets/images/groupart.png');

  $AssetsImagesHomeGen get home => const $AssetsImagesHomeGen();

  /// File path: assets/images/location.svg
  String get location => 'assets/images/location.svg';

  /// File path: assets/images/location_a_marker.png
  AssetGenImage get locationAMarker =>
      const AssetGenImage('assets/images/location_a_marker.png');

  /// File path: assets/images/location_b_marker.png
  AssetGenImage get locationBMarker =>
      const AssetGenImage('assets/images/location_b_marker.png');

  /// File path: assets/images/location_icon.svg
  String get locationIcon => 'assets/images/location_icon.svg';

  /// File path: assets/images/location_pin_marker.png
  AssetGenImage get locationPinMarker =>
      const AssetGenImage('assets/images/location_pin_marker.png');

  /// File path: assets/images/login_component.svg
  String get loginComponent => 'assets/images/login_component.svg';

  /// File path: assets/images/logout.svg
  String get logout => 'assets/images/logout.svg';

  /// File path: assets/images/male_icon_selected.svg
  String get maleIconSelected => 'assets/images/male_icon_selected.svg';

  /// File path: assets/images/male_icon_unselected.svg
  String get maleIconUnselected => 'assets/images/male_icon_unselected.svg';

  /// File path: assets/images/man_sitting_on_car.svg
  String get manSittingOnCar => 'assets/images/man_sitting_on_car.svg';

  /// File path: assets/images/man_with_bag.png
  AssetGenImage get manWithBag =>
      const AssetGenImage('assets/images/man_with_bag.png');

  /// File path: assets/images/my_wallet.svg
  String get myWallet => 'assets/images/my_wallet.svg';

  /// File path: assets/images/new_card_added.svg
  String get newCardAdded => 'assets/images/new_card_added.svg';

  /// File path: assets/images/no_connection.png
  AssetGenImage get noConnection =>
      const AssetGenImage('assets/images/no_connection.png');

  /// File path: assets/images/notification_icon.svg
  String get notificationIcon => 'assets/images/notification_icon.svg';

  $AssetsImagesOfferGen get offer => const $AssetsImagesOfferGen();

  /// File path: assets/images/onboarding_image_one.png
  AssetGenImage get onboardingImageOne =>
      const AssetGenImage('assets/images/onboarding_image_one.png');

  /// File path: assets/images/onboarding_image_three.png
  AssetGenImage get onboardingImageThree =>
      const AssetGenImage('assets/images/onboarding_image_three.png');

  /// File path: assets/images/onboarding_image_two.jpg
  AssetGenImage get onboardingImageTwo =>
      const AssetGenImage('assets/images/onboarding_image_two.jpg');

  /// File path: assets/images/payment_icon.svg
  String get paymentIcon => 'assets/images/payment_icon.svg';

  /// File path: assets/images/person_icon.svg
  String get personIcon => 'assets/images/person_icon.svg';

  /// File path: assets/images/priority_big_car.png
  AssetGenImage get priorityBigCar =>
      const AssetGenImage('assets/images/priority_big_car.png');

  /// File path: assets/images/priority_car.png
  AssetGenImage get priorityCar =>
      const AssetGenImage('assets/images/priority_car.png');

  /// File path: assets/images/profile.svg
  String get profile => 'assets/images/profile.svg';

  /// File path: assets/images/relaxing.png
  AssetGenImage get relaxing =>
      const AssetGenImage('assets/images/relaxing.png');

  /// File path: assets/images/requests.svg
  String get requests => 'assets/images/requests.svg';

  /// File path: assets/images/service_header.svg
  String get serviceHeader => 'assets/images/service_header.svg';

  $AssetsImagesSettingGen get setting => const $AssetsImagesSettingGen();

  /// File path: assets/images/setting_icon.svg
  String get settingIcon => 'assets/images/setting_icon.svg';

  /// File path: assets/images/share_emergency_contact.svg
  String get shareEmergencyContact =>
      'assets/images/share_emergency_contact.svg';

  /// File path: assets/images/small_circle.svg
  String get smallCircle => 'assets/images/small_circle.svg';

  /// File path: assets/images/smart_car.png
  AssetGenImage get smartCar =>
      const AssetGenImage('assets/images/smart_car.png');

  /// File path: assets/images/splash_buildings_image.png
  AssetGenImage get splashBuildingsImage =>
      const AssetGenImage('assets/images/splash_buildings_image.png');

  /// File path: assets/images/splash_screen_image.png
  AssetGenImage get splashScreenImage =>
      const AssetGenImage('assets/images/splash_screen_image.png');

  $AssetsImagesSuggestionsAndComplaintsGen get suggestionsAndComplaints =>
      const $AssetsImagesSuggestionsAndComplaintsGen();
  $AssetsImagesSupervisorGen get supervisor =>
      const $AssetsImagesSupervisorGen();

  /// File path: assets/images/tasks_icon.svg
  String get tasksIcon => 'assets/images/tasks_icon.svg';

  /// File path: assets/images/tiny_red_circle.svg
  String get tinyRedCircle => 'assets/images/tiny_red_circle.svg';

  /// File path: assets/images/tips_icon.svg
  String get tipsIcon => 'assets/images/tips_icon.svg';

  /// File path: assets/images/under-maintenance.png
  AssetGenImage get underMaintenance =>
      const AssetGenImage('assets/images/under-maintenance.png');

  /// File path: assets/images/userMarker.png
  AssetGenImage get userMarker =>
      const AssetGenImage('assets/images/userMarker.png');

  $AssetsImagesWalletGen get wallet => const $AssetsImagesWalletGen();

  /// File path: assets/images/warning_sticker.svg
  String get warningSticker => 'assets/images/warning_sticker.svg';

  /// File path: assets/images/welcome_illustration.svg
  String get welcomeIllustration => 'assets/images/welcome_illustration.svg';

  /// File path: assets/images/welcome_shapes.svg
  String get welcomeShapes => 'assets/images/welcome_shapes.svg';

  /// File path: assets/images/yalla_gai_logo.png
  AssetGenImage get yallaGaiLogo =>
      const AssetGenImage('assets/images/yalla_gai_logo.png');

  /// List of all assets
  List<dynamic> get values => [
        alertLocation,
        arrowSquareLeft,
        backButton,
        bigArc,
        box,
        cancelIcon,
        carMarker,
        cardExample,
        cashIcon,
        celebratePeople,
        couponIcon,
        creditCardIcon,
        deluxeCar,
        destMarker,
        dividerIcon,
        downArrow,
        e,
        editIcon,
        exchIcon,
        femaleIconSelected,
        femaleIconUnselected,
        femaleUser,
        giftOffer,
        greenWalletIcon,
        groupart,
        location,
        locationAMarker,
        locationBMarker,
        locationIcon,
        locationPinMarker,
        loginComponent,
        logout,
        maleIconSelected,
        maleIconUnselected,
        manSittingOnCar,
        manWithBag,
        myWallet,
        newCardAdded,
        noConnection,
        notificationIcon,
        onboardingImageOne,
        onboardingImageThree,
        onboardingImageTwo,
        paymentIcon,
        personIcon,
        priorityBigCar,
        priorityCar,
        profile,
        relaxing,
        requests,
        serviceHeader,
        settingIcon,
        shareEmergencyContact,
        smallCircle,
        smartCar,
        splashBuildingsImage,
        splashScreenImage,
        tasksIcon,
        tinyRedCircle,
        tipsIcon,
        underMaintenance,
        userMarker,
        warningSticker,
        welcomeIllustration,
        welcomeShapes,
        yallaGaiLogo
      ];
}

class $AssetsLogoGen {
  const $AssetsLogoGen();

  /// File path: assets/logo/yalla_gai_black_logo.svg
  String get yallaGaiBlackLogo => 'assets/logo/yalla_gai_black_logo.svg';

  /// File path: assets/logo/yalla_gai_final_driver_logo.png
  AssetGenImage get yallaGaiFinalDriverLogo =>
      const AssetGenImage('assets/logo/yalla_gai_final_driver_logo.png');

  /// File path: assets/logo/yalla_gai_white_logo.svg
  String get yallaGaiWhiteLogo => 'assets/logo/yalla_gai_white_logo.svg';

  /// List of all assets
  List<dynamic> get values =>
      [yallaGaiBlackLogo, yallaGaiFinalDriverLogo, yallaGaiWhiteLogo];
}

class $AssetsSoundsGen {
  const $AssetsSoundsGen();

  /// File path: assets/sounds/alart.mp3
  String get alart => 'assets/sounds/alart.mp3';

  /// File path: assets/sounds/ride_alert.mp3
  String get rideAlert => 'assets/sounds/ride_alert.mp3';

  /// List of all assets
  List<String> get values => [alart, rideAlert];
}

class $AssetsImagesAccountGen {
  const $AssetsImagesAccountGen();

  /// File path: assets/images/account/accounttype.svg
  String get accounttype => 'assets/images/account/accounttype.svg';

  /// File path: assets/images/account/block.png
  AssetGenImage get block =>
      const AssetGenImage('assets/images/account/block.png');

  /// File path: assets/images/account/language.png
  AssetGenImage get language =>
      const AssetGenImage('assets/images/account/language.png');

  /// File path: assets/images/account/star.svg
  String get star => 'assets/images/account/star.svg';

  /// List of all assets
  List<dynamic> get values => [accounttype, block, language, star];
}

class $AssetsImagesConnectGen {
  const $AssetsImagesConnectGen();

  /// File path: assets/images/connect/connect.svg
  String get connect => 'assets/images/connect/connect.svg';

  /// File path: assets/images/connect/whatsapp.svg
  String get whatsapp => 'assets/images/connect/whatsapp.svg';

  /// List of all assets
  List<String> get values => [connect, whatsapp];
}

class $AssetsImagesDriverGen {
  const $AssetsImagesDriverGen();

  $AssetsImagesDriverChatGen get chat => const $AssetsImagesDriverChatGen();
  $AssetsImagesDriverDetailGen get detail =>
      const $AssetsImagesDriverDetailGen();
}

class $AssetsImagesHomeGen {
  const $AssetsImagesHomeGen();

  /// File path: assets/images/home/<USER>
  String get acceptedRequestsIcon =>
      'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  AssetGenImage get agreement =>
      const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  String get checkedImage => 'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  AssetGenImage get counterIcon =>
      const AssetGenImage('assets/images/home/<USER>');

  /// File path: assets/images/home/<USER>
  String get crown => 'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  String get currentLocationPointer =>
      'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  String get pathLocationIcon => 'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  String get policeCarLight => 'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  String get rejectedRequestsIcon =>
      'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  String get threePeopleIcon => 'assets/images/home/<USER>';

  /// File path: assets/images/home/<USER>
  String get trafficHint => 'assets/images/home/<USER>';

  /// List of all assets
  List<dynamic> get values => [
        acceptedRequestsIcon,
        agreement,
        checkedImage,
        counterIcon,
        crown,
        currentLocationPointer,
        pathLocationIcon,
        policeCarLight,
        rejectedRequestsIcon,
        threePeopleIcon,
        trafficHint
      ];
}

class $AssetsImagesOfferGen {
  const $AssetsImagesOfferGen();

  /// File path: assets/images/offer/balance.svg
  String get balance => 'assets/images/offer/balance.svg';

  /// File path: assets/images/offer/divider.svg
  String get divider => 'assets/images/offer/divider.svg';

  /// File path: assets/images/offer/offertick.svg
  String get offertick => 'assets/images/offer/offertick.svg';

  /// File path: assets/images/offer/wallet.svg
  String get wallet => 'assets/images/offer/wallet.svg';

  /// List of all assets
  List<String> get values => [balance, divider, offertick, wallet];
}

class $AssetsImagesSettingGen {
  const $AssetsImagesSettingGen();

  /// File path: assets/images/setting/bro.svg
  String get bro => 'assets/images/setting/bro.svg';

  /// File path: assets/images/setting/calendar-tick.svg
  String get calendarTick => 'assets/images/setting/calendar-tick.svg';

  /// File path: assets/images/setting/calendar.svg
  String get calendar => 'assets/images/setting/calendar.svg';

  /// File path: assets/images/setting/clock.svg
  String get clock => 'assets/images/setting/clock.svg';

  /// File path: assets/images/setting/frame.svg
  String get frame => 'assets/images/setting/frame.svg';

  /// File path: assets/images/setting/shield-tick.svg
  String get shieldTick => 'assets/images/setting/shield-tick.svg';

  /// List of all assets
  List<String> get values =>
      [bro, calendarTick, calendar, clock, frame, shieldTick];
}

class $AssetsImagesSuggestionsAndComplaintsGen {
  const $AssetsImagesSuggestionsAndComplaintsGen();

  /// File path: assets/images/suggestions_and_complaints/suggestionsicon.svg
  String get suggestionsicon =>
      'assets/images/suggestions_and_complaints/suggestionsicon.svg';

  /// List of all assets
  List<String> get values => [suggestionsicon];
}

class $AssetsImagesSupervisorGen {
  const $AssetsImagesSupervisorGen();

  /// File path: assets/images/supervisor/supervisor.svg
  String get supervisor => 'assets/images/supervisor/supervisor.svg';

  /// File path: assets/images/supervisor/techsupport.svg
  String get techsupport => 'assets/images/supervisor/techsupport.svg';

  /// List of all assets
  List<String> get values => [supervisor, techsupport];
}

class $AssetsImagesWalletGen {
  const $AssetsImagesWalletGen();

  /// File path: assets/images/wallet/banknote.svg
  String get banknote => 'assets/images/wallet/banknote.svg';

  /// File path: assets/images/wallet/dashed_line.svg
  String get dashedLine => 'assets/images/wallet/dashed_line.svg';

  /// File path: assets/images/wallet/wallet_app_bar.svg
  String get walletAppBar => 'assets/images/wallet/wallet_app_bar.svg';

  /// File path: assets/images/wallet/wallet_ticket.svg
  String get walletTicket => 'assets/images/wallet/wallet_ticket.svg';

  /// List of all assets
  List<String> get values => [banknote, dashedLine, walletAppBar, walletTicket];
}

class $AssetsImagesDriverChatGen {
  const $AssetsImagesDriverChatGen();

  /// File path: assets/images/driver/chat/send.svg
  String get send => 'assets/images/driver/chat/send.svg';

  /// File path: assets/images/driver/chat/shape.svg
  String get shape => 'assets/images/driver/chat/shape.svg';

  /// List of all assets
  List<String> get values => [send, shape];
}

class $AssetsImagesDriverDetailGen {
  const $AssetsImagesDriverDetailGen();

  /// File path: assets/images/driver/detail/car.svg
  String get car => 'assets/images/driver/detail/car.svg';

  /// File path: assets/images/driver/detail/star.svg
  String get star => 'assets/images/driver/detail/star.svg';

  /// File path: assets/images/driver/detail/time.svg
  String get time => 'assets/images/driver/detail/time.svg';

  /// List of all assets
  List<String> get values => [car, star, time];
}

class Assets {
  Assets._();

  static const $AssetsImagesGen images = $AssetsImagesGen();
  static const $AssetsLogoGen logo = $AssetsLogoGen();
  static const $AssetsSoundsGen sounds = $AssetsSoundsGen();
}

class AssetGenImage {
  const AssetGenImage(this._assetName);

  final String _assetName;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
