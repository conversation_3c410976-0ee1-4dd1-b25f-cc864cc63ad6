// lib/background_socket_task.dart
import 'dart:isolate';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';

// class BackgroundSocketTask extends TaskHandler {
//   SendPort? _sendPort;

//   @override
//   Future<void> onStart(DateTime timestamp, inputData) async {
//     _sendPort = await FlutterForegroundTask.getData(key: 'sendPort');
//     CustomSocket.connectAndListener(
//       context: BackgroundTaskContext(), // dummy, not used
//     );
//     // Forward messages from the socket to UI.
//     CustomSocket.connectionCompleter.future.then((_) {
//       _sendPort?.send("Socket Connected");
//     });
//   }

//   Future<void> onEvent(DateTime timestamp, inputData) async {
//     if (CustomSocket.connection) {
//       CustomSocket.channel?.sink.add('Heartbeat @ $timestamp');
//     }
//   }

//   @override
//   Future<void> onDestroy(DateTime timestamp,_) async {
//     await CustomSocket.disconnect();
//   }

//   void onButtonPressed(String id) {
//     if (id == 'stopButton') {
//       FlutterForegroundTask.stopService();
//     }
//   }

//   @override
//   void onRepeatEvent(DateTime timestamp) {
//     // TODO: implement onRepeatEvent
//   }
// }
