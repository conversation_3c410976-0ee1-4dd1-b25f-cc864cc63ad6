import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en')
  ];

  /// No description provided for @no_data.
  ///
  /// In en, this message translates to:
  /// **'no data'**
  String get no_data;

  /// No description provided for @account_typ.
  ///
  /// In en, this message translates to:
  /// **'Account Type'**
  String get account_typ;

  /// No description provided for @bronze.
  ///
  /// In en, this message translates to:
  /// **'Bronze'**
  String get bronze;

  /// No description provided for @silver.
  ///
  /// In en, this message translates to:
  /// **'Silver'**
  String get silver;

  /// No description provided for @gold.
  ///
  /// In en, this message translates to:
  /// **'Gold'**
  String get gold;

  /// No description provided for @for_all_users.
  ///
  /// In en, this message translates to:
  /// **'For All Users'**
  String get for_all_users;

  /// No description provided for @month.
  ///
  /// In en, this message translates to:
  /// **'/Month'**
  String get month;

  /// No description provided for @availability_not.
  ///
  /// In en, this message translates to:
  /// **'Availability: Not < '**
  String get availability_not;

  /// No description provided for @availability_for_all_drivers.
  ///
  /// In en, this message translates to:
  /// **'Availability: For All Drivers'**
  String get availability_for_all_drivers;

  /// No description provided for @passenger_or_driver.
  ///
  /// In en, this message translates to:
  /// **'Are you a passenger or a driver?'**
  String get passenger_or_driver;

  /// No description provided for @you_can_Change_mode_later.
  ///
  /// In en, this message translates to:
  /// **'You can change mode later'**
  String get you_can_Change_mode_later;

  /// No description provided for @passenger.
  ///
  /// In en, this message translates to:
  /// **'Passenger'**
  String get passenger;

  /// No description provided for @driver.
  ///
  /// In en, this message translates to:
  /// **'Driver'**
  String get driver;

  /// No description provided for @number_length_should_be.
  ///
  /// In en, this message translates to:
  /// **'Number length should be'**
  String get number_length_should_be;

  /// No description provided for @forgot_assword.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password'**
  String get forgot_assword;

  /// No description provided for @enter_your_mobile_umber.
  ///
  /// In en, this message translates to:
  /// **'\'Enter Your Mobile Number'**
  String get enter_your_mobile_umber;

  /// No description provided for @mobile_number.
  ///
  /// In en, this message translates to:
  /// **'Mobile Number'**
  String get mobile_number;

  /// No description provided for @continue_.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continue_;

  /// No description provided for @phone_number_is_required.
  ///
  /// In en, this message translates to:
  /// **'Phone number is required'**
  String get phone_number_is_required;

  /// No description provided for @first_number_can_not_be_0.
  ///
  /// In en, this message translates to:
  /// **'first number can not be 0'**
  String get first_number_can_not_be_0;

  /// No description provided for @number_length_should_be_9.
  ///
  /// In en, this message translates to:
  /// **'number length should be 9 or 10'**
  String get number_length_should_be_9;

  /// No description provided for @welcome_to.
  ///
  /// In en, this message translates to:
  /// **'Welcome to'**
  String get welcome_to;

  /// No description provided for @yalla_gai.
  ///
  /// In en, this message translates to:
  /// **'Yalla Gai'**
  String get yalla_gai;

  /// No description provided for @have_a_great_journey_with_yallaGai.
  ///
  /// In en, this message translates to:
  /// **'Have a great journey with YallaGai'**
  String get have_a_great_journey_with_yallaGai;

  /// No description provided for @next.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get next;

  /// No description provided for @request_ride.
  ///
  /// In en, this message translates to:
  /// **'Request Ride'**
  String get request_ride;

  /// No description provided for @real_time_tracking.
  ///
  /// In en, this message translates to:
  /// **'Real-time Tracking'**
  String get real_time_tracking;

  /// No description provided for @reliable_service.
  ///
  /// In en, this message translates to:
  /// **'Reliable Service'**
  String get reliable_service;

  /// No description provided for @skip.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skip;

  /// No description provided for @type_in_the_4_digit_number_sent_to_the_number.
  ///
  /// In en, this message translates to:
  /// **'Type in the 4-digit number sent to the number'**
  String get type_in_the_4_digit_number_sent_to_the_number;

  /// No description provided for @did_not_recieved_otp.
  ///
  /// In en, this message translates to:
  /// **'Didn\'t recieved OTP?'**
  String get did_not_recieved_otp;

  /// No description provided for @resend_otp.
  ///
  /// In en, this message translates to:
  /// **'Resend OTP'**
  String get resend_otp;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @verify.
  ///
  /// In en, this message translates to:
  /// **'Verify'**
  String get verify;

  /// No description provided for @enter_OTP.
  ///
  /// In en, this message translates to:
  /// **'Enter OTP'**
  String get enter_OTP;

  /// No description provided for @reset_password.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get reset_password;

  /// No description provided for @enter_new_password.
  ///
  /// In en, this message translates to:
  /// **'Enter New Password'**
  String get enter_new_password;

  /// No description provided for @confirm_password.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirm_password;

  /// No description provided for @passwords_should_be_the_same.
  ///
  /// In en, this message translates to:
  /// **'Passwords should be the same'**
  String get passwords_should_be_the_same;

  /// No description provided for @fill_your_profile.
  ///
  /// In en, this message translates to:
  /// **'Fill Your Profile'**
  String get fill_your_profile;

  /// No description provided for @arabic_ame.
  ///
  /// In en, this message translates to:
  /// **'Arabic Name'**
  String get arabic_ame;

  /// No description provided for @english_name.
  ///
  /// In en, this message translates to:
  /// **'English Name'**
  String get english_name;

  /// No description provided for @male.
  ///
  /// In en, this message translates to:
  /// **'Male'**
  String get male;

  /// No description provided for @female.
  ///
  /// In en, this message translates to:
  /// **'Female'**
  String get female;

  /// No description provided for @date_of_birth.
  ///
  /// In en, this message translates to:
  /// **'Date Of Birth'**
  String get date_of_birth;

  /// No description provided for @mm.
  ///
  /// In en, this message translates to:
  /// **'MM'**
  String get mm;

  /// No description provided for @dd.
  ///
  /// In en, this message translates to:
  /// **'DD'**
  String get dd;

  /// No description provided for @yy.
  ///
  /// In en, this message translates to:
  /// **'YY'**
  String get yy;

  /// No description provided for @id_number.
  ///
  /// In en, this message translates to:
  /// **'Id Number'**
  String get id_number;

  /// No description provided for @car_Photo.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Photos'**
  String get car_Photo;

  /// No description provided for @maximum_4_photos_are_required.
  ///
  /// In en, this message translates to:
  /// **'Maximum Upload 4'**
  String get maximum_4_photos_are_required;

  /// No description provided for @driver_license.
  ///
  /// In en, this message translates to:
  /// **' Driver’s License'**
  String get driver_license;

  /// No description provided for @driver_id.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Registration'**
  String get driver_id;

  /// No description provided for @criminal_record.
  ///
  /// In en, this message translates to:
  /// **'Vehicle Insurance'**
  String get criminal_record;

  /// No description provided for @certificate_of_non_security.
  ///
  /// In en, this message translates to:
  /// **'Criminal Record Clearance'**
  String get certificate_of_non_security;

  /// No description provided for @i_agree_to_terms_and_conditions_services_policies_and_prices.
  ///
  /// In en, this message translates to:
  /// **'I agree to terms and conditions, services, policies and prices'**
  String get i_agree_to_terms_and_conditions_services_policies_and_prices;

  /// No description provided for @i_agree_to_terms_and_conditions_including_working_with_us.
  ///
  /// In en, this message translates to:
  /// **'I agree to terms and conditions including working with us'**
  String get i_agree_to_terms_and_conditions_including_working_with_us;

  /// No description provided for @create_ccount.
  ///
  /// In en, this message translates to:
  /// **'Create Account'**
  String get create_ccount;

  /// No description provided for @enter_your_mobile_number.
  ///
  /// In en, this message translates to:
  /// **'Enter Your Mobile Number'**
  String get enter_your_mobile_number;

  /// No description provided for @do_you_have_account.
  ///
  /// In en, this message translates to:
  /// **'Do you have account?'**
  String get do_you_have_account;

  /// No description provided for @click_here.
  ///
  /// In en, this message translates to:
  /// **'Click Here'**
  String get click_here;

  /// No description provided for @i_agree_to_terms_and_conditions.
  ///
  /// In en, this message translates to:
  /// **'I agree to terms and conditions'**
  String get i_agree_to_terms_and_conditions;

  /// No description provided for @you_must_go_to_an_agent_to_charge_the_account_to_accept_it_please_do_give_id_number_phone_number.
  ///
  /// In en, this message translates to:
  /// **'You must go to an agent to charge the account to accept it. Please do give ID number, Phone number'**
  String get you_must_go_to_an_agent_to_charge_the_account_to_accept_it_please_do_give_id_number_phone_number;

  /// No description provided for @supervisors.
  ///
  /// In en, this message translates to:
  /// **'Supervisors'**
  String get supervisors;

  /// No description provided for @welcome_to_yalla_gai.
  ///
  /// In en, this message translates to:
  /// **'Welcome to Yalla Gai!'**
  String get welcome_to_yalla_gai;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @sign_up.
  ///
  /// In en, this message translates to:
  /// **'Sign-Up'**
  String get sign_up;

  /// No description provided for @add_new_car.
  ///
  /// In en, this message translates to:
  /// **'Add New Car'**
  String get add_new_car;

  /// No description provided for @card_name.
  ///
  /// In en, this message translates to:
  /// **'Card Name'**
  String get card_name;

  /// No description provided for @card_number.
  ///
  /// In en, this message translates to:
  /// **'Card Number'**
  String get card_number;

  /// No description provided for @expiry_date.
  ///
  /// In en, this message translates to:
  /// **'Expiry Date'**
  String get expiry_date;

  /// No description provided for @cvv.
  ///
  /// In en, this message translates to:
  /// **'CVV'**
  String get cvv;

  /// No description provided for @congrats_youve_successfully_added_your_card.
  ///
  /// In en, this message translates to:
  /// **'Congrats you’ve successfully added your card'**
  String get congrats_youve_successfully_added_your_card;

  /// No description provided for @price.
  ///
  /// In en, this message translates to:
  /// **'Price'**
  String get price;

  /// No description provided for @seats.
  ///
  /// In en, this message translates to:
  /// **'Seats'**
  String get seats;

  /// No description provided for @accepted.
  ///
  /// In en, this message translates to:
  /// **'Accepted'**
  String get accepted;

  /// No description provided for @yalla_auto.
  ///
  /// In en, this message translates to:
  /// **'Yalla Auto'**
  String get yalla_auto;

  /// No description provided for @minimum_fare.
  ///
  /// In en, this message translates to:
  /// **'Minimum Fare'**
  String get minimum_fare;

  /// No description provided for @maximum_fare.
  ///
  /// In en, this message translates to:
  /// **'Maximum Fare'**
  String get maximum_fare;

  /// No description provided for @flag_down_fee.
  ///
  /// In en, this message translates to:
  /// **'Flag-Down Fee'**
  String get flag_down_fee;

  /// No description provided for @avg_fdf.
  ///
  /// In en, this message translates to:
  /// **'Avg FDF'**
  String get avg_fdf;

  /// No description provided for @including_tax.
  ///
  /// In en, this message translates to:
  /// **'Including Tax'**
  String get including_tax;

  /// No description provided for @booking_fee.
  ///
  /// In en, this message translates to:
  /// **'Booking Fee'**
  String get booking_fee;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// No description provided for @thursday_friday.
  ///
  /// In en, this message translates to:
  /// **'Thursday, Friday'**
  String get thursday_friday;

  /// No description provided for @saturday_monday.
  ///
  /// In en, this message translates to:
  /// **'Saturday, Monday'**
  String get saturday_monday;

  /// No description provided for @less.
  ///
  /// In en, this message translates to:
  /// **'Less'**
  String get less;

  /// No description provided for @more.
  ///
  /// In en, this message translates to:
  /// **'More'**
  String get more;

  /// No description provided for @auto.
  ///
  /// In en, this message translates to:
  /// **'Auto'**
  String get auto;

  /// No description provided for @tarrifs.
  ///
  /// In en, this message translates to:
  /// **'Tarrifs'**
  String get tarrifs;

  /// No description provided for @morning.
  ///
  /// In en, this message translates to:
  /// **'Morning'**
  String get morning;

  /// No description provided for @type_a_message.
  ///
  /// In en, this message translates to:
  /// **'Type a message..'**
  String get type_a_message;

  /// No description provided for @confirm_order.
  ///
  /// In en, this message translates to:
  /// **'Confirm Order'**
  String get confirm_order;

  /// No description provided for @jod.
  ///
  /// In en, this message translates to:
  /// **'JOD'**
  String get jod;

  /// No description provided for @current_location.
  ///
  /// In en, this message translates to:
  /// **'Current Location'**
  String get current_location;

  /// No description provided for @set_your.
  ///
  /// In en, this message translates to:
  /// **'Set Your'**
  String get set_your;

  /// No description provided for @pick_up.
  ///
  /// In en, this message translates to:
  /// **'Pick-Up'**
  String get pick_up;

  /// No description provided for @drop_off.
  ///
  /// In en, this message translates to:
  /// **'Drop-Off'**
  String get drop_off;

  /// No description provided for @point.
  ///
  /// In en, this message translates to:
  /// **'Point'**
  String get point;

  /// No description provided for @select_on_map.
  ///
  /// In en, this message translates to:
  /// **'Select on Map'**
  String get select_on_map;

  /// No description provided for @add_current_location.
  ///
  /// In en, this message translates to:
  /// **'Add Current Location'**
  String get add_current_location;

  /// No description provided for @recent_rides.
  ///
  /// In en, this message translates to:
  /// **'Recent Rides'**
  String get recent_rides;

  /// No description provided for @unknown_street.
  ///
  /// In en, this message translates to:
  /// **'Unknown Street'**
  String get unknown_street;

  /// No description provided for @set_drop_point.
  ///
  /// In en, this message translates to:
  /// **'Set Drop Point'**
  String get set_drop_point;

  /// No description provided for @priority.
  ///
  /// In en, this message translates to:
  /// **'Priority'**
  String get priority;

  /// No description provided for @smart.
  ///
  /// In en, this message translates to:
  /// **'Smart'**
  String get smart;

  /// No description provided for @deluxe.
  ///
  /// In en, this message translates to:
  /// **'Deluxe'**
  String get deluxe;

  /// No description provided for @select_vehicle.
  ///
  /// In en, this message translates to:
  /// **'Select Vehicle'**
  String get select_vehicle;

  /// No description provided for @select_drop_off_point.
  ///
  /// In en, this message translates to:
  /// **'Select Drop-off Point'**
  String get select_drop_off_point;

  /// No description provided for @select_pick_up_point.
  ///
  /// In en, this message translates to:
  /// **'Select Pick-up Point'**
  String get select_pick_up_point;

  /// No description provided for @services.
  ///
  /// In en, this message translates to:
  /// **'Services'**
  String get services;

  /// No description provided for @prebooking.
  ///
  /// In en, this message translates to:
  /// **'Prebooking'**
  String get prebooking;

  /// No description provided for @airport.
  ///
  /// In en, this message translates to:
  /// **'AirPort'**
  String get airport;

  /// No description provided for @border_cross.
  ///
  /// In en, this message translates to:
  /// **'Border Cross'**
  String get border_cross;

  /// No description provided for @startup.
  ///
  /// In en, this message translates to:
  /// **'Startup'**
  String get startup;

  /// No description provided for @end_trip.
  ///
  /// In en, this message translates to:
  /// **'End Trip'**
  String get end_trip;

  /// No description provided for @swipe_right.
  ///
  /// In en, this message translates to:
  /// **'Swipe Right'**
  String get swipe_right;

  /// No description provided for @start_trip.
  ///
  /// In en, this message translates to:
  /// **'Start Trip'**
  String get start_trip;

  /// No description provided for @fetching_place_information.
  ///
  /// In en, this message translates to:
  /// **'Fetching Place Information ...'**
  String get fetching_place_information;

  /// No description provided for @are_you_sure_you_want_to_add_the_police_location.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to add the police location ?'**
  String get are_you_sure_you_want_to_add_the_police_location;

  /// No description provided for @mark_on_map.
  ///
  /// In en, this message translates to:
  /// **'Mark on Map'**
  String get mark_on_map;

  /// No description provided for @not_available.
  ///
  /// In en, this message translates to:
  /// **'Not Available'**
  String get not_available;

  /// No description provided for @available.
  ///
  /// In en, this message translates to:
  /// **'Available'**
  String get available;

  /// No description provided for @member_since.
  ///
  /// In en, this message translates to:
  /// **'Member Since'**
  String get member_since;

  /// No description provided for @car_brand.
  ///
  /// In en, this message translates to:
  /// **'Car Brand'**
  String get car_brand;

  /// No description provided for @plate_number.
  ///
  /// In en, this message translates to:
  /// **'Plate Number'**
  String get plate_number;

  /// No description provided for @start_time.
  ///
  /// In en, this message translates to:
  /// **'Start Time:'**
  String get start_time;

  /// No description provided for @counter.
  ///
  /// In en, this message translates to:
  /// **'Counter'**
  String get counter;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get start;

  /// No description provided for @time.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time;

  /// No description provided for @end_counte.
  ///
  /// In en, this message translates to:
  /// **'End Counter'**
  String get end_counte;

  /// No description provided for @the_price_fare_was.
  ///
  /// In en, this message translates to:
  /// **'The price fare was '**
  String get the_price_fare_was;

  /// No description provided for @okay.
  ///
  /// In en, this message translates to:
  /// **'Okay'**
  String get okay;

  /// No description provided for @coupon.
  ///
  /// In en, this message translates to:
  /// **'Coupon'**
  String get coupon;

  /// No description provided for @wrong_code.
  ///
  /// In en, this message translates to:
  /// **'Wrong Code'**
  String get wrong_code;

  /// No description provided for @apply.
  ///
  /// In en, this message translates to:
  /// **'Apply'**
  String get apply;

  /// No description provided for @profile.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profile;

  /// No description provided for @account_type.
  ///
  /// In en, this message translates to:
  /// **'Account Type'**
  String get account_type;

  /// No description provided for @payment.
  ///
  /// In en, this message translates to:
  /// **'Payment'**
  String get payment;

  /// No description provided for @supervisor.
  ///
  /// In en, this message translates to:
  /// **'Supervisor'**
  String get supervisor;

  /// No description provided for @technical_support.
  ///
  /// In en, this message translates to:
  /// **'Technical Support'**
  String get technical_support;

  /// No description provided for @car_type.
  ///
  /// In en, this message translates to:
  /// **'Car Type:'**
  String get car_type;

  /// No description provided for @plate_no.
  ///
  /// In en, this message translates to:
  /// **'Plate No:'**
  String get plate_no;

  /// No description provided for @rating.
  ///
  /// In en, this message translates to:
  /// **'Rating'**
  String get rating;

  /// No description provided for @trips.
  ///
  /// In en, this message translates to:
  /// **'Trips'**
  String get trips;

  /// No description provided for @years.
  ///
  /// In en, this message translates to:
  /// **'Years'**
  String get years;

  /// No description provided for @do_not_worry.
  ///
  /// In en, this message translates to:
  /// **'Don\'t Worry! We have shared your location to the Trusted emergency contacts and sent a message to them that you are in danger.'**
  String get do_not_worry;

  /// No description provided for @sharing_location.
  ///
  /// In en, this message translates to:
  /// **'Sharing you location with trusted emergency contacts and alerting them that you are in danger.'**
  String get sharing_location;

  /// No description provided for @congratulations_completed_trip.
  ///
  /// In en, this message translates to:
  /// **'Congratulations you\'ve successfully completed the trip'**
  String get congratulations_completed_trip;

  /// No description provided for @done.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get done;

  /// No description provided for @add_coupon.
  ///
  /// In en, this message translates to:
  /// **'Add Coupon'**
  String get add_coupon;

  /// No description provided for @choose_gender.
  ///
  /// In en, this message translates to:
  /// **'Choose Gender'**
  String get choose_gender;

  /// No description provided for @both.
  ///
  /// In en, this message translates to:
  /// **'both'**
  String get both;

  /// No description provided for @get_direction.
  ///
  /// In en, this message translates to:
  /// **'Get Direction'**
  String get get_direction;

  /// No description provided for @police_location.
  ///
  /// In en, this message translates to:
  /// **'Police Location'**
  String get police_location;

  /// No description provided for @fetching_place_info.
  ///
  /// In en, this message translates to:
  /// **'Fetching Place Information ...'**
  String get fetching_place_info;

  /// No description provided for @choose_radius.
  ///
  /// In en, this message translates to:
  /// **'Choose Radius'**
  String get choose_radius;

  /// No description provided for @set.
  ///
  /// In en, this message translates to:
  /// **'Set'**
  String get set;

  /// No description provided for @km.
  ///
  /// In en, this message translates to:
  /// **'KM'**
  String get km;

  /// No description provided for @rate_customer.
  ///
  /// In en, this message translates to:
  /// **'Rate Customer'**
  String get rate_customer;

  /// No description provided for @new_request.
  ///
  /// In en, this message translates to:
  /// **'New Request'**
  String get new_request;

  /// No description provided for @now.
  ///
  /// In en, this message translates to:
  /// **'Now'**
  String get now;

  /// No description provided for @reject.
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get reject;

  /// No description provided for @accept.
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get accept;

  /// No description provided for @order_now.
  ///
  /// In en, this message translates to:
  /// **'Order Now'**
  String get order_now;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @set_time.
  ///
  /// In en, this message translates to:
  /// **'Set Time'**
  String get set_time;

  /// No description provided for @payment_mode.
  ///
  /// In en, this message translates to:
  /// **'Payment Mode'**
  String get payment_mode;

  /// No description provided for @payment_methods.
  ///
  /// In en, this message translates to:
  /// **'Payment Method'**
  String get payment_methods;

  /// No description provided for @my_wallet.
  ///
  /// In en, this message translates to:
  /// **'My Wallet'**
  String get my_wallet;

  /// No description provided for @balance.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balance;

  /// No description provided for @add_card.
  ///
  /// In en, this message translates to:
  /// **'Add Card'**
  String get add_card;

  /// No description provided for @a.
  ///
  /// In en, this message translates to:
  /// **'A'**
  String get a;

  /// No description provided for @scheduled_orders.
  ///
  /// In en, this message translates to:
  /// **'Scheduled Orders'**
  String get scheduled_orders;

  /// No description provided for @traffic_hints.
  ///
  /// In en, this message translates to:
  /// **'Traffic Hints'**
  String get traffic_hints;

  /// No description provided for @heavy_traffic.
  ///
  /// In en, this message translates to:
  /// **'Heavy Traffic'**
  String get heavy_traffic;

  /// No description provided for @light_traffic.
  ///
  /// In en, this message translates to:
  /// **'Light Traffic'**
  String get light_traffic;

  /// No description provided for @no_traffic.
  ///
  /// In en, this message translates to:
  /// **'No Traffic'**
  String get no_traffic;

  /// No description provided for @balance_accepted_requests.
  ///
  /// In en, this message translates to:
  /// **'Balance'**
  String get balance_accepted_requests;

  /// No description provided for @working_hours.
  ///
  /// In en, this message translates to:
  /// **'Working Hours'**
  String get working_hours;

  /// No description provided for @number_of_trips.
  ///
  /// In en, this message translates to:
  /// **'Number of Trips'**
  String get number_of_trips;

  /// No description provided for @rejected_requests.
  ///
  /// In en, this message translates to:
  /// **'Rejected Requests'**
  String get rejected_requests;

  /// No description provided for @enable_location_permission.
  ///
  /// In en, this message translates to:
  /// **'Please enable location permission to use the map.'**
  String get enable_location_permission;

  /// No description provided for @enable_overlay_permission.
  ///
  /// In en, this message translates to:
  /// **'Please enable overlay permission to use the app in over other application.'**
  String get enable_overlay_permission;

  /// No description provided for @enable.
  ///
  /// In en, this message translates to:
  /// **'Enable'**
  String get enable;

  /// No description provided for @not_now.
  ///
  /// In en, this message translates to:
  /// **'Not now'**
  String get not_now;

  /// No description provided for @connect_us.
  ///
  /// In en, this message translates to:
  /// **'Connect Us'**
  String get connect_us;

  /// No description provided for @whatsapp.
  ///
  /// In en, this message translates to:
  /// **'Whatsapp'**
  String get whatsapp;

  /// No description provided for @telegram.
  ///
  /// In en, this message translates to:
  /// **'Telegram'**
  String get telegram;

  /// No description provided for @signal.
  ///
  /// In en, this message translates to:
  /// **'Signal'**
  String get signal;

  /// No description provided for @instagram.
  ///
  /// In en, this message translates to:
  /// **'Instagram'**
  String get instagram;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @account_registered_successfully.
  ///
  /// In en, this message translates to:
  /// **'Your account has been registered successfully'**
  String get account_registered_successfully;

  /// No description provided for @discount_code_first_two_trips.
  ///
  /// In en, this message translates to:
  /// **'Discount Code On the first two trips'**
  String get discount_code_first_two_trips;

  /// No description provided for @valid_until.
  ///
  /// In en, this message translates to:
  /// **'Valid until'**
  String get valid_until;

  /// No description provided for @lite.
  ///
  /// In en, this message translates to:
  /// **'Lite'**
  String get lite;

  /// No description provided for @zero_commission_day.
  ///
  /// In en, this message translates to:
  /// **'Zero Commission a day'**
  String get zero_commission_day;

  /// No description provided for @premium.
  ///
  /// In en, this message translates to:
  /// **'Premium'**
  String get premium;

  /// No description provided for @zero_commission_week.
  ///
  /// In en, this message translates to:
  /// **'Zero Commission a week'**
  String get zero_commission_week;

  /// No description provided for @offers.
  ///
  /// In en, this message translates to:
  /// **'Offers'**
  String get offers;

  /// No description provided for @use_our_products.
  ///
  /// In en, this message translates to:
  /// **'Use our products now!!!'**
  String get use_our_products;

  /// No description provided for @our_products.
  ///
  /// In en, this message translates to:
  /// **'Our products'**
  String get our_products;

  /// No description provided for @congratulations_plan_user.
  ///
  /// In en, this message translates to:
  /// **'Congratulations, you have now a'**
  String get congratulations_plan_user;

  /// No description provided for @buy_now.
  ///
  /// In en, this message translates to:
  /// **'Buy now'**
  String get buy_now;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @address_info.
  ///
  /// In en, this message translates to:
  /// **'Address Info'**
  String get address_info;

  /// No description provided for @name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// No description provided for @enter_location_name.
  ///
  /// In en, this message translates to:
  /// **'Enter location name'**
  String get enter_location_name;

  /// No description provided for @address_details.
  ///
  /// In en, this message translates to:
  /// **'Address Details'**
  String get address_details;

  /// No description provided for @enter_address_detail.
  ///
  /// In en, this message translates to:
  /// **'Enter address detail'**
  String get enter_address_detail;

  /// No description provided for @add_address.
  ///
  /// In en, this message translates to:
  /// **'Add Address'**
  String get add_address;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @college.
  ///
  /// In en, this message translates to:
  /// **'College'**
  String get college;

  /// No description provided for @office.
  ///
  /// In en, this message translates to:
  /// **'Office'**
  String get office;

  /// No description provided for @add_new_address.
  ///
  /// In en, this message translates to:
  /// **'+ Add New Addres'**
  String get add_new_address;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @emergency_contact.
  ///
  /// In en, this message translates to:
  /// **'Emergency Contact'**
  String get emergency_contact;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @add_contact.
  ///
  /// In en, this message translates to:
  /// **'Add Contact'**
  String get add_contact;

  /// No description provided for @notification.
  ///
  /// In en, this message translates to:
  /// **'Notification'**
  String get notification;

  /// No description provided for @general_notification.
  ///
  /// In en, this message translates to:
  /// **'General Notification'**
  String get general_notification;

  /// No description provided for @sound.
  ///
  /// In en, this message translates to:
  /// **'Sound'**
  String get sound;

  /// No description provided for @vibrate.
  ///
  /// In en, this message translates to:
  /// **'Vibrate'**
  String get vibrate;

  /// No description provided for @points.
  ///
  /// In en, this message translates to:
  /// **'Points'**
  String get points;

  /// No description provided for @withdraw_points.
  ///
  /// In en, this message translates to:
  /// **'Witdraw Ponits'**
  String get withdraw_points;

  /// No description provided for @redeem.
  ///
  /// In en, this message translates to:
  /// **'Redeem'**
  String get redeem;

  /// No description provided for @first_name.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get first_name;

  /// No description provided for @last_name.
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get last_name;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @tasks.
  ///
  /// In en, this message translates to:
  /// **'Tasks'**
  String get tasks;

  /// No description provided for @task.
  ///
  /// In en, this message translates to:
  /// **'Task'**
  String get task;

  /// No description provided for @complete_10_trips_reward.
  ///
  /// In en, this message translates to:
  /// **'Complete 10 trips and you will get a 50% discount on the value taken from you'**
  String get complete_10_trips_reward;

  /// No description provided for @reward.
  ///
  /// In en, this message translates to:
  /// **'Reward'**
  String get reward;

  /// No description provided for @cash.
  ///
  /// In en, this message translates to:
  /// **'CASH'**
  String get cash;

  /// No description provided for @requests.
  ///
  /// In en, this message translates to:
  /// **'Requests'**
  String get requests;

  /// No description provided for @previous_passengers.
  ///
  /// In en, this message translates to:
  /// **'Previous passengers'**
  String get previous_passengers;

  /// No description provided for @payment_details.
  ///
  /// In en, this message translates to:
  /// **'Payment Details'**
  String get payment_details;

  /// No description provided for @fare.
  ///
  /// In en, this message translates to:
  /// **'Fare'**
  String get fare;

  /// No description provided for @yallagai_percentage.
  ///
  /// In en, this message translates to:
  /// **'YallaGai’s Percentage'**
  String get yallagai_percentage;

  /// No description provided for @total.
  ///
  /// In en, this message translates to:
  /// **'Total'**
  String get total;

  /// No description provided for @date_and_distances.
  ///
  /// In en, this message translates to:
  /// **'Date and Distances'**
  String get date_and_distances;

  /// No description provided for @date_and_time.
  ///
  /// In en, this message translates to:
  /// **'Date and Time'**
  String get date_and_time;

  /// No description provided for @hour_time_24.
  ///
  /// In en, this message translates to:
  /// **'24-Hour Time'**
  String get hour_time_24;

  /// No description provided for @distances.
  ///
  /// In en, this message translates to:
  /// **'Distances'**
  String get distances;

  /// No description provided for @kilometers.
  ///
  /// In en, this message translates to:
  /// **'Kilometers'**
  String get kilometers;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @language.
  ///
  /// In en, this message translates to:
  /// **'Language'**
  String get language;

  /// No description provided for @suggested.
  ///
  /// In en, this message translates to:
  /// **'Suggested'**
  String get suggested;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'Arabic'**
  String get arabic;

  /// No description provided for @change_phone_number.
  ///
  /// In en, this message translates to:
  /// **'Do you want to change your phone number?'**
  String get change_phone_number;

  /// No description provided for @account_linkage.
  ///
  /// In en, this message translates to:
  /// **'Your account will be linked to your new mobile number.'**
  String get account_linkage;

  /// No description provided for @successfully_changed_phone_number.
  ///
  /// In en, this message translates to:
  /// **'Congratulations! You have successfully changed your phone number.'**
  String get successfully_changed_phone_number;

  /// No description provided for @change.
  ///
  /// In en, this message translates to:
  /// **'Change'**
  String get change;

  /// No description provided for @privacy_policy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacy_policy;

  /// No description provided for @new_phone_number.
  ///
  /// In en, this message translates to:
  /// **'Do you want to use a new phone number?'**
  String get new_phone_number;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @rules_terms.
  ///
  /// In en, this message translates to:
  /// **'Rules and Terms'**
  String get rules_terms;

  /// No description provided for @settings.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get settings;

  /// No description provided for @log_out.
  ///
  /// In en, this message translates to:
  /// **'Log Out'**
  String get log_out;

  /// No description provided for @confirm_log_out.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to log out?'**
  String get confirm_log_out;

  /// No description provided for @log_out_warning.
  ///
  /// In en, this message translates to:
  /// **'Logging out of your account will lose your data.'**
  String get log_out_warning;

  /// No description provided for @terms_conditions.
  ///
  /// In en, this message translates to:
  /// **'Terms and Conditions'**
  String get terms_conditions;

  /// No description provided for @suggestions_complaints.
  ///
  /// In en, this message translates to:
  /// **'Suggestions & Complaints'**
  String get suggestions_complaints;

  /// No description provided for @suggestions.
  ///
  /// In en, this message translates to:
  /// **'Suggestions'**
  String get suggestions;

  /// No description provided for @enter_suggestion.
  ///
  /// In en, this message translates to:
  /// **'Enter your suggestion'**
  String get enter_suggestion;

  /// No description provided for @suggestion.
  ///
  /// In en, this message translates to:
  /// **'Suggestion'**
  String get suggestion;

  /// No description provided for @complaints.
  ///
  /// In en, this message translates to:
  /// **'Complaints'**
  String get complaints;

  /// No description provided for @enter_complaint.
  ///
  /// In en, this message translates to:
  /// **'Enter your complaint'**
  String get enter_complaint;

  /// No description provided for @complaint.
  ///
  /// In en, this message translates to:
  /// **'Complaint'**
  String get complaint;

  /// No description provided for @attach_file_picture.
  ///
  /// In en, this message translates to:
  /// **'Attach any file or picture'**
  String get attach_file_picture;

  /// No description provided for @file_selected.
  ///
  /// In en, this message translates to:
  /// **'File selected: '**
  String get file_selected;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @suggestions_complaints_sent_successfully.
  ///
  /// In en, this message translates to:
  /// **'Suggestions and complaints sent successfully!'**
  String get suggestions_complaints_sent_successfully;

  /// No description provided for @supervisors_info.
  ///
  /// In en, this message translates to:
  /// **'Supervisors Info'**
  String get supervisors_info;

  /// No description provided for @select_city.
  ///
  /// In en, this message translates to:
  /// **'Select City'**
  String get select_city;

  /// No description provided for @tips.
  ///
  /// In en, this message translates to:
  /// **'Tips'**
  String get tips;

  /// No description provided for @balance_recharge_card.
  ///
  /// In en, this message translates to:
  /// **'Balance Recharge Card'**
  String get balance_recharge_card;

  /// No description provided for @enter_recharge_number.
  ///
  /// In en, this message translates to:
  /// **'Enter recharge number'**
  String get enter_recharge_number;

  /// No description provided for @successfully_added_to_wallet.
  ///
  /// In en, this message translates to:
  /// **'Congratulations! We have successfully added an amount to your wallet!'**
  String get successfully_added_to_wallet;

  /// No description provided for @mastercard.
  ///
  /// In en, this message translates to:
  /// **'Mastercard'**
  String get mastercard;

  /// No description provided for @wallet.
  ///
  /// In en, this message translates to:
  /// **'Wallet'**
  String get wallet;

  /// No description provided for @successfully_transferred_from_account.
  ///
  /// In en, this message translates to:
  /// **'Congratulations! You have successfully transferred an amount from your account.'**
  String get successfully_transferred_from_account;

  /// No description provided for @b.
  ///
  /// In en, this message translates to:
  /// **'B'**
  String get b;

  /// No description provided for @enter_phone_number.
  ///
  /// In en, this message translates to:
  /// **'Enter phone number'**
  String get enter_phone_number;

  /// No description provided for @pay.
  ///
  /// In en, this message translates to:
  /// **'Pay'**
  String get pay;

  /// No description provided for @top_up.
  ///
  /// In en, this message translates to:
  /// **'Top Up'**
  String get top_up;

  /// No description provided for @total_amount.
  ///
  /// In en, this message translates to:
  /// **'Total Amount:'**
  String get total_amount;

  /// No description provided for @balance_recharge_card_number.
  ///
  /// In en, this message translates to:
  /// **'Balance recharge card number'**
  String get balance_recharge_card_number;

  /// No description provided for @sent.
  ///
  /// In en, this message translates to:
  /// **'Sent'**
  String get sent;

  /// No description provided for @received.
  ///
  /// In en, this message translates to:
  /// **'Received'**
  String get received;

  /// No description provided for @select_payment_method.
  ///
  /// In en, this message translates to:
  /// **'Select the payment method you want to use.'**
  String get select_payment_method;

  /// No description provided for @add_new_card.
  ///
  /// In en, this message translates to:
  /// **'Add new card'**
  String get add_new_card;

  /// No description provided for @incentives.
  ///
  /// In en, this message translates to:
  /// **'Incentives'**
  String get incentives;

  /// No description provided for @priority_of_requests.
  ///
  /// In en, this message translates to:
  /// **'Priority of requests'**
  String get priority_of_requests;

  /// No description provided for @lowest_commision.
  ///
  /// In en, this message translates to:
  /// **'Lowest Commision'**
  String get lowest_commision;

  /// No description provided for @or.
  ///
  /// In en, this message translates to:
  /// **'or'**
  String get or;

  /// No description provided for @submit.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submit;

  /// No description provided for @cancellation_reason.
  ///
  /// In en, this message translates to:
  /// **'Cancellation Reason'**
  String get cancellation_reason;

  /// No description provided for @enter_other_reason.
  ///
  /// In en, this message translates to:
  /// **'Enter other reason'**
  String get enter_other_reason;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @driver_etails.
  ///
  /// In en, this message translates to:
  /// **'Driver Details'**
  String get driver_etails;

  /// No description provided for @unknown.
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get unknown;

  /// No description provided for @accetped_requests.
  ///
  /// In en, this message translates to:
  /// **'Accepted Requests'**
  String get accetped_requests;

  /// No description provided for @congratulations_you_are_now_a.
  ///
  /// In en, this message translates to:
  /// **'Congratulations, you’re now a'**
  String get congratulations_you_are_now_a;

  /// No description provided for @plan_user.
  ///
  /// In en, this message translates to:
  /// **'Plan user'**
  String get plan_user;

  /// No description provided for @miles.
  ///
  /// In en, this message translates to:
  /// **'Miles'**
  String get miles;

  /// No description provided for @phone_number.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phone_number;

  /// No description provided for @add.
  ///
  /// In en, this message translates to:
  /// **'Add'**
  String get add;

  /// No description provided for @min.
  ///
  /// In en, this message translates to:
  /// **'Min'**
  String get min;

  /// No description provided for @agree_to_all_above_conditions.
  ///
  /// In en, this message translates to:
  /// **'I agree to all conditons above'**
  String get agree_to_all_above_conditions;

  /// No description provided for @note.
  ///
  /// In en, this message translates to:
  /// **'Note: '**
  String get note;

  /// No description provided for @no_internet.
  ///
  /// In en, this message translates to:
  /// **'No Internet!!!'**
  String get no_internet;

  /// No description provided for @please_check_your_internet_connection_to_enjoy_our_new_services.
  ///
  /// In en, this message translates to:
  /// **'Please check your internet connection to enjoy our new services'**
  String get please_check_your_internet_connection_to_enjoy_our_new_services;

  /// No description provided for @time_paid_service.
  ///
  /// In en, this message translates to:
  /// **'The timer is actually paid service and you will get charged 0.02 JOD'**
  String get time_paid_service;

  /// No description provided for @type_of_car.
  ///
  /// In en, this message translates to:
  /// **'Type of car'**
  String get type_of_car;

  /// No description provided for @english_first_name.
  ///
  /// In en, this message translates to:
  /// **'Enlish First Name'**
  String get english_first_name;

  /// No description provided for @english_last_name.
  ///
  /// In en, this message translates to:
  /// **'Enlish Last Name'**
  String get english_last_name;

  /// No description provided for @arabic_first_name.
  ///
  /// In en, this message translates to:
  /// **'Arabic First Name'**
  String get arabic_first_name;

  /// No description provided for @arabic_last_name.
  ///
  /// In en, this message translates to:
  /// **'Arabic Last Name'**
  String get arabic_last_name;

  /// No description provided for @edit_profile.
  ///
  /// In en, this message translates to:
  /// **'Edit Profile'**
  String get edit_profile;

  /// No description provided for @m.
  ///
  /// In en, this message translates to:
  /// **'m'**
  String get m;

  /// No description provided for @account_updated_successfully.
  ///
  /// In en, this message translates to:
  /// **'Your account has been updated successfully'**
  String get account_updated_successfully;

  /// No description provided for @car_industry.
  ///
  /// In en, this message translates to:
  /// **'Car Model'**
  String get car_industry;

  /// No description provided for @english_full_name.
  ///
  /// In en, this message translates to:
  /// **'English Full Name'**
  String get english_full_name;

  /// No description provided for @car_year.
  ///
  /// In en, this message translates to:
  /// **'Year of Manufacturer'**
  String get car_year;

  /// No description provided for @arabic_full_name.
  ///
  /// In en, this message translates to:
  /// **'Arabic Full Name'**
  String get arabic_full_name;

  /// No description provided for @swipe_on_arrival.
  ///
  /// In en, this message translates to:
  /// **'Swipe on arrival'**
  String get swipe_on_arrival;

  /// No description provided for @choose_right_car_type_for_you.
  ///
  /// In en, this message translates to:
  /// **'Choose the categories that match your car'**
  String get choose_right_car_type_for_you;

  /// No description provided for @choose_right_car_type_for_you_subtitle.
  ///
  /// In en, this message translates to:
  /// **'You can choose multiple options from these categories, it increases the chance of receiving orders.'**
  String get choose_right_car_type_for_you_subtitle;

  /// No description provided for @choose_appropriate_car_type_for_service_vehicles.
  ///
  /// In en, this message translates to:
  /// **'Choose the categories that match your car as this category is present in the services available in the city'**
  String get choose_appropriate_car_type_for_service_vehicles;

  /// No description provided for @activate_cars_to_receive_orders.
  ///
  /// In en, this message translates to:
  /// **'Activate the cars you want to receive orders from'**
  String get activate_cars_to_receive_orders;

  /// No description provided for @terms_first_part.
  ///
  /// In en, this message translates to:
  /// **'By registering, I\'ve read and agreed to the'**
  String get terms_first_part;

  /// No description provided for @terms_last_part.
  ///
  /// In en, this message translates to:
  /// **''**
  String get terms_last_part;

  /// No description provided for @terms_of_service.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get terms_of_service;

  /// No description provided for @and.
  ///
  /// In en, this message translates to:
  /// **'and'**
  String get and;

  /// No description provided for @please_select_profile_picture.
  ///
  /// In en, this message translates to:
  /// **'Please select profile picture!'**
  String get please_select_profile_picture;

  /// No description provided for @please_select_car.
  ///
  /// In en, this message translates to:
  /// **'Please select car!'**
  String get please_select_car;

  /// No description provided for @please_select_right_car_type_for_you.
  ///
  /// In en, this message translates to:
  /// **'Please select the appropriate car categories.'**
  String get please_select_right_car_type_for_you;

  /// No description provided for @please_select_car_images.
  ///
  /// In en, this message translates to:
  /// **'Please select car images!'**
  String get please_select_car_images;

  /// No description provided for @contact_the_supervisor.
  ///
  /// In en, this message translates to:
  /// **'Contact the supervisor'**
  String get contact_the_supervisor;

  /// No description provided for @ride_requested_by_another_driver.
  ///
  /// In en, this message translates to:
  /// **'The request has been accepted from another driver'**
  String get ride_requested_by_another_driver;

  /// No description provided for @trip_request_cancelled.
  ///
  /// In en, this message translates to:
  /// **'The user canceled the trip!'**
  String get trip_request_cancelled;

  /// No description provided for @delete_account.
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get delete_account;

  /// No description provided for @delete_account_warning.
  ///
  /// In en, this message translates to:
  /// **'Your data will be permanently deleted.'**
  String get delete_account_warning;

  /// No description provided for @cancel_trip.
  ///
  /// In en, this message translates to:
  /// **'Cancel Trip?'**
  String get cancel_trip;

  /// No description provided for @cancel_trip_warning.
  ///
  /// In en, this message translates to:
  /// **'A cancellation tax will be deducted if you click OK.'**
  String get cancel_trip_warning;

  /// No description provided for @commission.
  ///
  /// In en, this message translates to:
  /// **'Commission'**
  String get commission;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @cancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get cancelled;

  /// No description provided for @in_the_trip.
  ///
  /// In en, this message translates to:
  /// **'In The Trip'**
  String get in_the_trip;

  /// No description provided for @min_rating.
  ///
  /// In en, this message translates to:
  /// **'Min. Rating'**
  String get min_rating;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @update_title.
  ///
  /// In en, this message translates to:
  /// **'Enjoy YallaGai new features'**
  String get update_title;

  /// No description provided for @update_notice.
  ///
  /// In en, this message translates to:
  /// **'You\'re requested to update your application'**
  String get update_notice;

  /// No description provided for @remind_me_later.
  ///
  /// In en, this message translates to:
  /// **'Remind me later'**
  String get remind_me_later;

  /// No description provided for @update_now.
  ///
  /// In en, this message translates to:
  /// **'Update now!'**
  String get update_now;

  /// No description provided for @app_name.
  ///
  /// In en, this message translates to:
  /// **'YallaGai'**
  String get app_name;

  /// No description provided for @under_maintenance_line1.
  ///
  /// In en, this message translates to:
  /// **'The app is in maintenance mode, we will be back soon.'**
  String get under_maintenance_line1;

  /// No description provided for @under_maintenance_line2.
  ///
  /// In en, this message translates to:
  /// **'A notification will be sent to you soon.'**
  String get under_maintenance_line2;

  /// No description provided for @enter_email_address.
  ///
  /// In en, this message translates to:
  /// **'Enter your email address'**
  String get enter_email_address;

  /// No description provided for @incorrect.
  ///
  /// In en, this message translates to:
  /// **'Incorrect'**
  String get incorrect;

  /// No description provided for @card_expired.
  ///
  /// In en, this message translates to:
  /// **'The card has expired'**
  String get card_expired;

  /// No description provided for @invalid_month.
  ///
  /// In en, this message translates to:
  /// **'Invalid month'**
  String get invalid_month;

  /// No description provided for @amount_send_note.
  ///
  /// In en, this message translates to:
  /// **'The amount will be sent from your wallet to the other user\'s wallet.'**
  String get amount_send_note;

  /// No description provided for @open_settings.
  ///
  /// In en, this message translates to:
  /// **'Open Settings'**
  String get open_settings;

  /// No description provided for @location_permission_denied.
  ///
  /// In en, this message translates to:
  /// **'Location Permission Denied'**
  String get location_permission_denied;

  /// No description provided for @location_permission_denied_message.
  ///
  /// In en, this message translates to:
  /// **'You have permanently denied for location usage permission. To enable real-time tracking and ensure a seamless experience, please allow \"Location Usage Permission\" in your device settings.'**
  String get location_permission_denied_message;

  /// No description provided for @location_permission_consent.
  ///
  /// In en, this message translates to:
  /// **'To provide real-time tracking for your rides and ensure a seamless experience, we require \"Location Always Usage Permission\".\n\nThe YallaGai app collects location data to track your location during trips, even in the background, to deliver a precise and smooth delivery experience.'**
  String get location_permission_consent;

  /// No description provided for @continues.
  ///
  /// In en, this message translates to:
  /// **'Continue'**
  String get continues;

  /// No description provided for @enter_valid_name.
  ///
  /// In en, this message translates to:
  /// **'Enter a valid name'**
  String get enter_valid_name;

  /// No description provided for @valid_plate_number.
  ///
  /// In en, this message translates to:
  /// **'Enter a valid plate number'**
  String get valid_plate_number;

  /// No description provided for @valid_date_of_birth.
  ///
  /// In en, this message translates to:
  /// **'Enter valid date of birth!'**
  String get valid_date_of_birth;

  /// No description provided for @valid_id_number.
  ///
  /// In en, this message translates to:
  /// **'Enter a valid id number'**
  String get valid_id_number;

  /// No description provided for @overlay_permission_needed.
  ///
  /// In en, this message translates to:
  /// **'Overlay Permission Needed'**
  String get overlay_permission_needed;

  /// No description provided for @overlay_permission_request.
  ///
  /// In en, this message translates to:
  /// **'To enhance your experience and provide continuous features such as instant notifications and quick access, we need your permission to run the app in the background and display it as a floating bubble on the screen.\nPlease allow Allow app over other Apps Access'**
  String get overlay_permission_request;

  /// No description provided for @crop_your_image.
  ///
  /// In en, this message translates to:
  /// **'Crop your image'**
  String get crop_your_image;

  /// No description provided for @crop_it.
  ///
  /// In en, this message translates to:
  /// **'Crop It!'**
  String get crop_it;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['ar', 'en'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar': return AppLocalizationsAr();
    case 'en': return AppLocalizationsEn();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
