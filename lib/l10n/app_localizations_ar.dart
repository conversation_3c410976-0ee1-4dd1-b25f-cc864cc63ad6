// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get no_data => 'لا توجد بيانات';

  @override
  String get account_typ => 'نوع الحساب';

  @override
  String get bronze => 'برونز';

  @override
  String get silver => 'فضة';

  @override
  String get gold => 'ذهب';

  @override
  String get for_all_users => 'لجميع المستخدمين';

  @override
  String get month => '/الشهر';

  @override
  String get availability_not => 'التوافر: ليس أقل من ';

  @override
  String get availability_for_all_drivers => 'التوافر: لجميع السائقين';

  @override
  String get passenger_or_driver => 'هل أنت راكب أم سائق؟';

  @override
  String get you_can_Change_mode_later => 'يمكنك تغيير الوضع لاحقًا';

  @override
  String get passenger => 'راكب';

  @override
  String get driver => 'سائق';

  @override
  String get number_length_should_be => 'يجب أن يكون طول الرقم';

  @override
  String get forgot_assword => 'نسيت كلمة المرور';

  @override
  String get enter_your_mobile_umber => 'أدخل رقم هاتفك المحمول';

  @override
  String get mobile_number => 'رقم الجوال';

  @override
  String get continue_ => 'استمرار';

  @override
  String get phone_number_is_required => 'رقم الهاتف مطلوب';

  @override
  String get first_number_can_not_be_0 => 'لا يمكن أن يكون الرقم الأول صفر';

  @override
  String get number_length_should_be_9 => 'يجب أن يكون طول الرقم 9 أو 10';

  @override
  String get welcome_to => 'مرحبًا بك في';

  @override
  String get yalla_gai => 'يلا جاي';

  @override
  String get have_a_great_journey_with_yallaGai => 'نتمنى لك رحلة ممتعة مع يلا جاي';

  @override
  String get next => 'التالي';

  @override
  String get request_ride => 'طلب رحلة';

  @override
  String get real_time_tracking => 'تتبع الوقت الحقيقي';

  @override
  String get reliable_service => 'خدمة موثوقة';

  @override
  String get skip => 'تخطى';

  @override
  String get type_in_the_4_digit_number_sent_to_the_number => 'اكتب الرقم المكون من 4 أرقام المرسل إلى الرقم';

  @override
  String get did_not_recieved_otp => 'لم تستلم رمز OTP؟';

  @override
  String get resend_otp => 'إعادة إرسال OTP';

  @override
  String get edit => 'تعديل';

  @override
  String get verify => 'التحقق';

  @override
  String get enter_OTP => 'أدخل رمز OTP';

  @override
  String get reset_password => 'إعادة تعيين كلمة المرور';

  @override
  String get enter_new_password => 'أدخل كلمة مرور جديدة';

  @override
  String get confirm_password => 'تأكيد كلمة المرور';

  @override
  String get passwords_should_be_the_same => 'يجب أن تكون كلمات المرور متطابقة';

  @override
  String get fill_your_profile => 'املأ ملفك الشخصي';

  @override
  String get arabic_ame => 'الاسم بالعربية';

  @override
  String get english_name => 'الاسم بالإنجليزية';

  @override
  String get male => 'ذكر';

  @override
  String get female => 'أنثى';

  @override
  String get date_of_birth => 'تاريخ الميلاد';

  @override
  String get mm => 'شهر';

  @override
  String get dd => 'يوم';

  @override
  String get yy => 'سنة';

  @override
  String get id_number => 'الرقم الوطني';

  @override
  String get car_Photo => 'صور السيارة';

  @override
  String get maximum_4_photos_are_required => 'بحد اقصى 4';

  @override
  String get driver_license => 'رخصة القيادة';

  @override
  String get driver_id => 'رخصة السيارة';

  @override
  String get criminal_record => 'تأمين المركبة';

  @override
  String get certificate_of_non_security => 'عدم محكومية';

  @override
  String get i_agree_to_terms_and_conditions_services_policies_and_prices => 'أوافق على الشروط والأحكام والخدمات والسياسات والأسعار';

  @override
  String get i_agree_to_terms_and_conditions_including_working_with_us => 'أوافق على الشروط والأحكام بما في ذلك العمل معنا';

  @override
  String get create_ccount => 'إنشاء حساب';

  @override
  String get enter_your_mobile_number => 'أدخل رقم هاتفك المحمول';

  @override
  String get do_you_have_account => 'هل لديك حساب؟';

  @override
  String get click_here => 'انقر هنا';

  @override
  String get i_agree_to_terms_and_conditions => 'أوافق على الشروط والأحكام';

  @override
  String get you_must_go_to_an_agent_to_charge_the_account_to_accept_it_please_do_give_id_number_phone_number => 'يجب عليك الذهاب إلى وكيل لشحن الحساب لقبوله. يرجى تقديم رقم الهوية ورقم الهاتف';

  @override
  String get supervisors => 'المشرفين';

  @override
  String get welcome_to_yalla_gai => 'مرحبًا بك في يلا جاي!';

  @override
  String get login => 'تسجيل الدخول';

  @override
  String get sign_up => 'التسجيل';

  @override
  String get add_new_car => 'إضافة سيارة جديدة';

  @override
  String get card_name => 'اسم البطاقة';

  @override
  String get card_number => 'رقم البطاقة';

  @override
  String get expiry_date => 'تاريخ الانتهاء';

  @override
  String get cvv => 'CVV';

  @override
  String get congrats_youve_successfully_added_your_card => 'تهانينا! لقد قمت بإضافة بطاقتك بنجاح';

  @override
  String get price => 'السعر';

  @override
  String get seats => 'المقاعد';

  @override
  String get accepted => 'مقبول';

  @override
  String get yalla_auto => 'يلا أوتو';

  @override
  String get minimum_fare => 'الحد الأدنى للأجرة';

  @override
  String get maximum_fare => 'الحد الأقصى للأجرة';

  @override
  String get flag_down_fee => 'رسوم البدء';

  @override
  String get avg_fdf => 'متوسط FDF';

  @override
  String get including_tax => 'شامل الضريبة';

  @override
  String get booking_fee => 'رسوم الحجز';

  @override
  String get tuesday => 'الثلاثاء';

  @override
  String get wednesday => 'الأربعاء';

  @override
  String get thursday => 'الخميس';

  @override
  String get friday => 'الجمعة';

  @override
  String get saturday => 'السبت';

  @override
  String get sunday => 'الأحد';

  @override
  String get monday => 'الاثنين';

  @override
  String get thursday_friday => 'الخميس، الجمعة';

  @override
  String get saturday_monday => 'السبت، الاثنين';

  @override
  String get less => 'أقل';

  @override
  String get more => 'أكثر';

  @override
  String get auto => 'أوتو';

  @override
  String get tarrifs => 'الأسعار';

  @override
  String get morning => 'الصباح';

  @override
  String get type_a_message => 'اكتب رسالة..';

  @override
  String get confirm_order => 'تأكيد الطلب';

  @override
  String get jod => 'دينار أردني';

  @override
  String get current_location => 'الموقع الحالي';

  @override
  String get set_your => 'حدد موقعك';

  @override
  String get pick_up => 'التقاط';

  @override
  String get drop_off => 'إسقاط';

  @override
  String get point => 'نقطة';

  @override
  String get select_on_map => 'اختر على الخريطة';

  @override
  String get add_current_location => 'إضافة الموقع الحالي';

  @override
  String get recent_rides => 'الرحلات الأخيرة';

  @override
  String get unknown_street => 'شارع غير معروف';

  @override
  String get set_drop_point => 'تحديد نقطة الإسقاط';

  @override
  String get priority => 'الأولوية';

  @override
  String get smart => 'ذكي';

  @override
  String get deluxe => 'فاخر';

  @override
  String get select_vehicle => 'اختر المركبة';

  @override
  String get select_drop_off_point => 'اختر نقطة الإسقاط';

  @override
  String get select_pick_up_point => 'اختر نقطة التقاط';

  @override
  String get services => 'الخدمات';

  @override
  String get prebooking => 'الحجز المسبق';

  @override
  String get airport => 'المطار';

  @override
  String get border_cross => 'عبور الحدود';

  @override
  String get startup => 'بداية';

  @override
  String get end_trip => 'انتهاء الرحلة';

  @override
  String get swipe_right => 'اسحب لليمين';

  @override
  String get start_trip => 'بدء الرحلة';

  @override
  String get fetching_place_information => 'جارٍ استرداد معلومات المكان ...';

  @override
  String get are_you_sure_you_want_to_add_the_police_location => 'هل أنت متأكد أنك تريد إضافة موقع الشرطة؟';

  @override
  String get mark_on_map => 'وضع علامة على الخريطة';

  @override
  String get not_available => 'غير متاح';

  @override
  String get available => 'متاح';

  @override
  String get member_since => 'عضو منذ';

  @override
  String get car_brand => 'ماركة السيارة';

  @override
  String get plate_number => 'رقم اللوحة';

  @override
  String get start_time => 'وقت البدء:';

  @override
  String get counter => 'عداد';

  @override
  String get start => 'بدء';

  @override
  String get time => 'الوقت';

  @override
  String get end_counte => 'إنهاء العد';

  @override
  String get the_price_fare_was => 'كانت أجرة السعر ';

  @override
  String get okay => 'حسناً';

  @override
  String get coupon => 'كوبون';

  @override
  String get wrong_code => 'رمز خاطئ';

  @override
  String get apply => 'تطبيق';

  @override
  String get profile => 'الملف الشخصي';

  @override
  String get account_type => 'نوع الحساب';

  @override
  String get payment => 'الدفع';

  @override
  String get supervisor => 'المشرف';

  @override
  String get technical_support => 'الدعم الفني';

  @override
  String get car_type => 'نوع السيارة:';

  @override
  String get plate_no => 'رقم اللوحة:';

  @override
  String get rating => 'التقييم';

  @override
  String get trips => 'الرحلات';

  @override
  String get years => 'سنوات';

  @override
  String get do_not_worry => 'لا تقلق! لقد قمنا بمشاركة موقعك مع جهات الاتصال الطارئة الموثوق بها وأرسلنا رسالة إليهم بأنك في خطر.';

  @override
  String get sharing_location => 'مشاركة موقعك مع جهات الاتصال الطارئة الموثوق بها وتنبيههم بأنك في خطر.';

  @override
  String get congratulations_completed_trip => 'تهانينا! لقد أكملت الرحلة بنجاح';

  @override
  String get done => 'تم';

  @override
  String get add_coupon => 'إضافة كوبون';

  @override
  String get choose_gender => 'اختر الجنس';

  @override
  String get both => 'كلاهما';

  @override
  String get get_direction => 'الحصول على الاتجاه';

  @override
  String get police_location => 'موقع الشرطة';

  @override
  String get fetching_place_info => 'جارٍ استرداد معلومات المكان ...';

  @override
  String get choose_radius => 'اختر نصف القطر';

  @override
  String get set => 'تعيين';

  @override
  String get km => 'كم';

  @override
  String get rate_customer => 'تقييم العميل';

  @override
  String get new_request => 'طلب جديد';

  @override
  String get now => 'الآن';

  @override
  String get reject => 'رفض';

  @override
  String get accept => 'قبول';

  @override
  String get order_now => 'اطلب الآن';

  @override
  String get cancel => 'إلغاء';

  @override
  String get set_time => 'تعيين الوقت';

  @override
  String get payment_mode => 'وضع الدفع';

  @override
  String get payment_methods => 'طريقة الدفع او السداد';

  @override
  String get my_wallet => 'محفظتي';

  @override
  String get balance => 'الرصيد';

  @override
  String get add_card => 'إضافة بطاقة';

  @override
  String get a => 'أ';

  @override
  String get scheduled_orders => 'الطلبات المجدولة';

  @override
  String get traffic_hints => 'تلميحات المرور';

  @override
  String get heavy_traffic => 'ازدحام مروري';

  @override
  String get light_traffic => 'حركة مرور خفيفة';

  @override
  String get no_traffic => 'لا توجد حركة مرور';

  @override
  String get balance_accepted_requests => 'الرصيد';

  @override
  String get working_hours => 'ساعات العمل';

  @override
  String get number_of_trips => 'عدد الرحلات';

  @override
  String get rejected_requests => 'الطلبات المرفوضة';

  @override
  String get enable_location_permission => 'يرجى تمكين إذن الموقع لاستخدام الخريطة.';

  @override
  String get enable_overlay_permission => 'يرجى اعطاء إذن التراكيب لاستخدام التطبيق في تطبيق آخر.';

  @override
  String get enable => 'تمكين';

  @override
  String get not_now => 'ليس الآن';

  @override
  String get connect_us => 'تواصل معنا';

  @override
  String get whatsapp => 'واتساب';

  @override
  String get telegram => 'تيليجرام';

  @override
  String get signal => 'سيغنال';

  @override
  String get instagram => 'إنستجرام';

  @override
  String get notifications => 'الإشعارات';

  @override
  String get account_registered_successfully => 'تم تسجيل حسابك بنجاح';

  @override
  String get discount_code_first_two_trips => 'رمز الخصم على أول رحلتين';

  @override
  String get valid_until => 'صالح حتى';

  @override
  String get lite => 'خفيف';

  @override
  String get zero_commission_day => 'يوم بدون عمولة';

  @override
  String get premium => 'بريميوم';

  @override
  String get zero_commission_week => 'أسبوع بدون عمولة';

  @override
  String get offers => 'العروض';

  @override
  String get use_our_products => 'استخدم منتجاتنا الآن!';

  @override
  String get our_products => 'منتجاتنا';

  @override
  String get congratulations_plan_user => 'تهانينا، لديك الآن';

  @override
  String get buy_now => 'اشتر الآن';

  @override
  String get address => 'العنوان';

  @override
  String get address_info => 'معلومات العنوان';

  @override
  String get name => 'الاسم';

  @override
  String get enter_location_name => 'أدخل اسم الموقع';

  @override
  String get address_details => 'تفاصيل العنوان';

  @override
  String get enter_address_detail => 'أدخل تفاصيل العنوان';

  @override
  String get add_address => 'إضافة العنوان';

  @override
  String get home => 'المنزل';

  @override
  String get college => 'الكلية';

  @override
  String get office => 'المكتب';

  @override
  String get add_new_address => '+ إضافة عنوان جديد';

  @override
  String get update => 'تحديث';

  @override
  String get emergency_contact => 'جهة اتصال طارئة';

  @override
  String get delete => 'حذف';

  @override
  String get add_contact => 'إضافة جهة اتصال';

  @override
  String get notification => 'الإشعار';

  @override
  String get general_notification => 'إشعار عام';

  @override
  String get sound => 'الصوت';

  @override
  String get vibrate => 'الاهتزاز';

  @override
  String get points => 'النقاط';

  @override
  String get withdraw_points => 'سحب النقاط';

  @override
  String get redeem => 'استرداد';

  @override
  String get first_name => 'الاسم الأول';

  @override
  String get last_name => 'الاسم الأخير';

  @override
  String get email => 'البريد الإلكتروني';

  @override
  String get password => 'كلمة المرور';

  @override
  String get tasks => 'المهام';

  @override
  String get task => 'المهمة';

  @override
  String get complete_10_trips_reward => 'أكمل 10 رحلات وستحصل على خصم 50٪ على القيمة المأخوذة منك';

  @override
  String get reward => 'المكافأة';

  @override
  String get cash => 'نقداً';

  @override
  String get requests => 'الطلبات';

  @override
  String get previous_passengers => 'الركاب السابقين';

  @override
  String get payment_details => 'تفاصيل الدفع';

  @override
  String get fare => 'الأجرة';

  @override
  String get yallagai_percentage => 'نسبة يلا جاي';

  @override
  String get total => 'الإجمالي';

  @override
  String get date_and_distances => 'التاريخ والمسافات';

  @override
  String get date_and_time => 'التاريخ والوقت';

  @override
  String get hour_time_24 => 'وقت 24 ساعة';

  @override
  String get distances => 'المسافات';

  @override
  String get kilometers => 'كيلومترات';

  @override
  String get english => 'الإنجليزية';

  @override
  String get language => 'اللغة';

  @override
  String get suggested => 'مقترح';

  @override
  String get arabic => 'العربية';

  @override
  String get change_phone_number => 'هل ترغب في تغيير رقم الهاتف الخاص بك؟';

  @override
  String get account_linkage => 'سيتم ربط حسابك برقم هاتفك المحمول الجديد.';

  @override
  String get successfully_changed_phone_number => 'تهانينا! لقد قمت بتغيير رقم هاتفك بنجاح.';

  @override
  String get change => 'تغيير';

  @override
  String get privacy_policy => 'سياسة الخصوصية';

  @override
  String get new_phone_number => 'هل ترغب في استخدام رقم هاتف جديد؟';

  @override
  String get no => 'لا';

  @override
  String get yes => 'نعم';

  @override
  String get rules_terms => 'القواعد والشروط';

  @override
  String get settings => 'الإعدادات';

  @override
  String get log_out => 'تسجيل الخروج';

  @override
  String get confirm_log_out => 'هل أنت متأكد أنك تريد تسجيل الخروج؟';

  @override
  String get log_out_warning => 'سيؤدي تسجيل الخروج من حسابك إلى فقدان بياناتك.';

  @override
  String get terms_conditions => 'الشروط والأحكام';

  @override
  String get suggestions_complaints => 'الاقتراحات والشكاوى';

  @override
  String get suggestions => 'الاقتراحات';

  @override
  String get enter_suggestion => 'أدخل اقتراحك';

  @override
  String get suggestion => 'الاقتراح';

  @override
  String get complaints => 'الشكاوى';

  @override
  String get enter_complaint => 'أدخل شكواك';

  @override
  String get complaint => 'الشكوى';

  @override
  String get attach_file_picture => 'إرفاق أي ملف أو صورة';

  @override
  String get file_selected => 'الملف المحدد: ';

  @override
  String get send => 'إرسال';

  @override
  String get suggestions_complaints_sent_successfully => 'تم إرسال الاقتراحات والشكاوى بنجاح!';

  @override
  String get supervisors_info => 'معلومات المشرفين';

  @override
  String get select_city => 'اختر المدينة';

  @override
  String get tips => 'نصائح';

  @override
  String get balance_recharge_card => 'بطاقة شحن الرصيد';

  @override
  String get enter_recharge_number => 'أدخل رقم الشحن';

  @override
  String get successfully_added_to_wallet => 'تهانينا! لقد قمنا بإضافة مبلغ بنجاح إلى محفظتك!';

  @override
  String get mastercard => 'ماستركارد';

  @override
  String get wallet => 'المحفظة';

  @override
  String get successfully_transferred_from_account => 'تهانينا! لقد قمت بتحويل مبلغ بنجاح من حسابك.';

  @override
  String get b => 'ب';

  @override
  String get enter_phone_number => 'أدخل رقم الهاتف';

  @override
  String get pay => 'دفع';

  @override
  String get top_up => 'شحن';

  @override
  String get total_amount => 'المبلغ الإجمالي:';

  @override
  String get balance_recharge_card_number => 'رقم بطاقة شحن الرصيد';

  @override
  String get sent => 'تم الإرسال';

  @override
  String get received => 'تم الاستلام';

  @override
  String get select_payment_method => 'حدد طريقة الدفع التي ترغب في استخدامها.';

  @override
  String get add_new_card => 'إضافة بطاقة جديدة';

  @override
  String get incentives => 'الحوافز';

  @override
  String get priority_of_requests => 'أولوية الطلبات';

  @override
  String get lowest_commision => 'أدنى عمولة';

  @override
  String get or => 'أو';

  @override
  String get submit => 'إرسال';

  @override
  String get cancellation_reason => 'سبب الإلغاء';

  @override
  String get enter_other_reason => 'أدخل سببًا آخر';

  @override
  String get other => 'أخرى';

  @override
  String get driver_etails => 'تفاصيل السائق';

  @override
  String get unknown => 'غير معروف';

  @override
  String get accetped_requests => 'الطلبات المقبولة';

  @override
  String get congratulations_you_are_now_a => 'تهانينا، أنت الآن';

  @override
  String get plan_user => 'مستخدم خطة';

  @override
  String get miles => 'ميل';

  @override
  String get phone_number => 'رقم الهاتف';

  @override
  String get add => 'إضافة';

  @override
  String get min => 'دقيقة';

  @override
  String get agree_to_all_above_conditions => 'أوافق على جميع الشروط أعلاه';

  @override
  String get note => 'ملاحظة: ';

  @override
  String get no_internet => 'No Internet!!!';

  @override
  String get please_check_your_internet_connection_to_enjoy_our_new_services => 'يرجى التحقق من اتصالك بالإنترنت للاستمتاع بخدماتنا';

  @override
  String get time_paid_service => 'المؤقت هو خدمة مدفوعة، وسيتم تحصيل 0.02 دينار أردني';

  @override
  String get type_of_car => 'نوع السيارة';

  @override
  String get english_first_name => 'الاسم الأول بالإنجليزية';

  @override
  String get english_last_name => 'الاسم الأخير بالإنجليزية';

  @override
  String get arabic_first_name => 'الاسم الأول بالعربية';

  @override
  String get arabic_last_name => 'الاسم الأخير بالعربية';

  @override
  String get edit_profile => 'تعديل الملف الشخصي';

  @override
  String get m => 'م';

  @override
  String get account_updated_successfully => 'لقد تم تحديث حسابك بنجاح';

  @override
  String get car_industry => 'طراز السيارة';

  @override
  String get english_full_name => 'الاسم الكامل باللغة الإنجليزية';

  @override
  String get car_year => 'سنة الشركة المصنعة';

  @override
  String get arabic_full_name => 'الاسم العربي الكامل';

  @override
  String get swipe_on_arrival => 'اسحب عند وصولك';

  @override
  String get choose_right_car_type_for_you => 'اختر الفئات المطابقة لسيارتك';

  @override
  String get choose_right_car_type_for_you_subtitle => 'يمكنك اختيار خيارات متعدده من هذ الفئات , انها تزيد من فرصة استقبال الطلبات';

  @override
  String get choose_appropriate_car_type_for_service_vehicles => 'اختر الفئات المطابقة لسيارتك حيث انه هذه الفئة موجوده في الخدمات المتاحه في المدينة';

  @override
  String get activate_cars_to_receive_orders => 'فعل السيارات التي تريد استلام الطلبات منها';

  @override
  String get terms_first_part => 'لقد قرأت';

  @override
  String get terms_last_part => 'افقت عليها';

  @override
  String get terms_of_service => 'شروط الخدمة';

  @override
  String get and => 'و';

  @override
  String get please_select_profile_picture => 'يرجى تحديد صورة الملف الشخصي!';

  @override
  String get please_select_car => 'يرجى تحديد السيارة!';

  @override
  String get please_select_right_car_type_for_you => 'اختر فئات السيارات التي تناسبك';

  @override
  String get please_select_car_images => 'يرجى تحديد صور السيارة!';

  @override
  String get contact_the_supervisor => 'تواصل مع المشرف';

  @override
  String get ride_requested_by_another_driver => 'لقد تم قبول الطلب من سائق اخر';

  @override
  String get trip_request_cancelled => 'المستخدم قام بالغاء الرحلة';

  @override
  String get delete_account => 'حذف الحساب';

  @override
  String get delete_account_warning => 'سوف يتم حذف بياناتك نهائيا ';

  @override
  String get cancel_trip => 'إلغاء الرحلة؟';

  @override
  String get cancel_trip_warning => 'سوف يتم خصم ضريبه الالغاء في حال النقر على الموافق';

  @override
  String get commission => 'العموله';

  @override
  String get completed => 'مكتمله';

  @override
  String get cancelled => 'ملغاه';

  @override
  String get in_the_trip => 'في الرحله';

  @override
  String get min_rating => 'الحد الادنى للتقييم';

  @override
  String get all => 'الكل';

  @override
  String get update_title => 'استمتع بميزات YallaGai الجديدة';

  @override
  String get update_notice => 'يُطلب منك تحديث التطبيق ';

  @override
  String get remind_me_later => 'ذكرني لاحقا';

  @override
  String get update_now => 'تحديث الآن';

  @override
  String get app_name => 'YallaGai';

  @override
  String get under_maintenance_line1 => 'التطبيق في وضع الصيانة سوف نعود قريبا';

  @override
  String get under_maintenance_line2 => 'سوف يتم ارسال اشعار اليك قريبا';

  @override
  String get enter_email_address => 'أدخل عنوان بريدك الالكتروني';

  @override
  String get incorrect => 'غير صحيح';

  @override
  String get card_expired => 'انتهت صلاحية البطاقة';

  @override
  String get invalid_month => 'شهر غير صالح';

  @override
  String get amount_send_note => 'سيتم ارسال المبلغ من محفظتك الى محفظة المستخدم الاخر';

  @override
  String get open_settings => 'افتح الإعدادات';

  @override
  String get location_permission_denied => 'تم رفض إذن الموقع';

  @override
  String get location_permission_denied_message => 'لقد رفضت بشكل دائم إذن استخدام الموقع. لتمكين التتبع في الوقت الفعلي وضمان تجربة سلسة، يرجى السماح بـ \"إذن استخدام الموقع\" في إعدادات جهازك.';

  @override
  String get location_permission_consent => 'لتوفير تتبع في الوقت الفعلي لرحلاتك وضمان تجربة سلسة، نحتاج إلى \"إذن استخدام الموقع دائمًا\".\n\nيجمع تطبيق YallaGai Captain بيانات الموقع لتتبع موقعك أثناء الرحلات، حتى في الخلفية، لتقديم تجربة توصيل دقيقة وسلسة.';

  @override
  String get continues => 'استمرار';

  @override
  String get enter_valid_name => 'أدخل اسماً صحيحاً';

  @override
  String get valid_plate_number => 'أدخل رقم لوحة سيارة صالح';

  @override
  String get valid_date_of_birth => 'أدخل تاريخ ميلاد صحيح';

  @override
  String get valid_id_number => 'أدخل رقم هوية';

  @override
  String get overlay_permission_needed => 'مطلوب إذن التراكب';

  @override
  String get overlay_permission_request => 'لتحسين تجربتك وتوفير ميزات مستمرة مثل الإشعارات الفورية والوصول السريع، نحتاج إلى إذنك لتشغيل التطبيق في الخلفية وعرضه كفقاعة عائمة على الشاشة.\nيرجى السماح للتطبيق بالوصول إلى تطبيقات أخرى.';

  @override
  String get crop_your_image => 'اقتصاص الصورة';

  @override
  String get crop_it => 'اقتصاصها';
}
