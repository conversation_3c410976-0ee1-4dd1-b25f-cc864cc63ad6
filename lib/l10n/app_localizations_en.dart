// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get no_data => 'no data';

  @override
  String get account_typ => 'Account Type';

  @override
  String get bronze => 'Bronze';

  @override
  String get silver => 'Silver';

  @override
  String get gold => 'Gold';

  @override
  String get for_all_users => 'For All Users';

  @override
  String get month => '/Month';

  @override
  String get availability_not => 'Availability: Not < ';

  @override
  String get availability_for_all_drivers => 'Availability: For All Drivers';

  @override
  String get passenger_or_driver => 'Are you a passenger or a driver?';

  @override
  String get you_can_Change_mode_later => 'You can change mode later';

  @override
  String get passenger => 'Passenger';

  @override
  String get driver => 'Driver';

  @override
  String get number_length_should_be => 'Number length should be';

  @override
  String get forgot_assword => 'Forgot Password';

  @override
  String get enter_your_mobile_umber => '\'Enter Your Mobile Number';

  @override
  String get mobile_number => 'Mobile Number';

  @override
  String get continue_ => 'Continue';

  @override
  String get phone_number_is_required => 'Phone number is required';

  @override
  String get first_number_can_not_be_0 => 'first number can not be 0';

  @override
  String get number_length_should_be_9 => 'number length should be 9 or 10';

  @override
  String get welcome_to => 'Welcome to';

  @override
  String get yalla_gai => 'Yalla Gai';

  @override
  String get have_a_great_journey_with_yallaGai => 'Have a great journey with YallaGai';

  @override
  String get next => 'Next';

  @override
  String get request_ride => 'Request Ride';

  @override
  String get real_time_tracking => 'Real-time Tracking';

  @override
  String get reliable_service => 'Reliable Service';

  @override
  String get skip => 'Skip';

  @override
  String get type_in_the_4_digit_number_sent_to_the_number => 'Type in the 4-digit number sent to the number';

  @override
  String get did_not_recieved_otp => 'Didn\'t recieved OTP?';

  @override
  String get resend_otp => 'Resend OTP';

  @override
  String get edit => 'Edit';

  @override
  String get verify => 'Verify';

  @override
  String get enter_OTP => 'Enter OTP';

  @override
  String get reset_password => 'Reset Password';

  @override
  String get enter_new_password => 'Enter New Password';

  @override
  String get confirm_password => 'Confirm Password';

  @override
  String get passwords_should_be_the_same => 'Passwords should be the same';

  @override
  String get fill_your_profile => 'Fill Your Profile';

  @override
  String get arabic_ame => 'Arabic Name';

  @override
  String get english_name => 'English Name';

  @override
  String get male => 'Male';

  @override
  String get female => 'Female';

  @override
  String get date_of_birth => 'Date Of Birth';

  @override
  String get mm => 'MM';

  @override
  String get dd => 'DD';

  @override
  String get yy => 'YY';

  @override
  String get id_number => 'Id Number';

  @override
  String get car_Photo => 'Vehicle Photos';

  @override
  String get maximum_4_photos_are_required => 'Maximum Upload 4';

  @override
  String get driver_license => ' Driver’s License';

  @override
  String get driver_id => 'Vehicle Registration';

  @override
  String get criminal_record => 'Vehicle Insurance';

  @override
  String get certificate_of_non_security => 'Criminal Record Clearance';

  @override
  String get i_agree_to_terms_and_conditions_services_policies_and_prices => 'I agree to terms and conditions, services, policies and prices';

  @override
  String get i_agree_to_terms_and_conditions_including_working_with_us => 'I agree to terms and conditions including working with us';

  @override
  String get create_ccount => 'Create Account';

  @override
  String get enter_your_mobile_number => 'Enter Your Mobile Number';

  @override
  String get do_you_have_account => 'Do you have account?';

  @override
  String get click_here => 'Click Here';

  @override
  String get i_agree_to_terms_and_conditions => 'I agree to terms and conditions';

  @override
  String get you_must_go_to_an_agent_to_charge_the_account_to_accept_it_please_do_give_id_number_phone_number => 'You must go to an agent to charge the account to accept it. Please do give ID number, Phone number';

  @override
  String get supervisors => 'Supervisors';

  @override
  String get welcome_to_yalla_gai => 'Welcome to Yalla Gai!';

  @override
  String get login => 'Login';

  @override
  String get sign_up => 'Sign-Up';

  @override
  String get add_new_car => 'Add New Car';

  @override
  String get card_name => 'Card Name';

  @override
  String get card_number => 'Card Number';

  @override
  String get expiry_date => 'Expiry Date';

  @override
  String get cvv => 'CVV';

  @override
  String get congrats_youve_successfully_added_your_card => 'Congrats you’ve successfully added your card';

  @override
  String get price => 'Price';

  @override
  String get seats => 'Seats';

  @override
  String get accepted => 'Accepted';

  @override
  String get yalla_auto => 'Yalla Auto';

  @override
  String get minimum_fare => 'Minimum Fare';

  @override
  String get maximum_fare => 'Maximum Fare';

  @override
  String get flag_down_fee => 'Flag-Down Fee';

  @override
  String get avg_fdf => 'Avg FDF';

  @override
  String get including_tax => 'Including Tax';

  @override
  String get booking_fee => 'Booking Fee';

  @override
  String get tuesday => 'Tuesday';

  @override
  String get wednesday => 'Wednesday';

  @override
  String get thursday => 'Thursday';

  @override
  String get friday => 'Friday';

  @override
  String get saturday => 'Saturday';

  @override
  String get sunday => 'Sunday';

  @override
  String get monday => 'Monday';

  @override
  String get thursday_friday => 'Thursday, Friday';

  @override
  String get saturday_monday => 'Saturday, Monday';

  @override
  String get less => 'Less';

  @override
  String get more => 'More';

  @override
  String get auto => 'Auto';

  @override
  String get tarrifs => 'Tarrifs';

  @override
  String get morning => 'Morning';

  @override
  String get type_a_message => 'Type a message..';

  @override
  String get confirm_order => 'Confirm Order';

  @override
  String get jod => 'JOD';

  @override
  String get current_location => 'Current Location';

  @override
  String get set_your => 'Set Your';

  @override
  String get pick_up => 'Pick-Up';

  @override
  String get drop_off => 'Drop-Off';

  @override
  String get point => 'Point';

  @override
  String get select_on_map => 'Select on Map';

  @override
  String get add_current_location => 'Add Current Location';

  @override
  String get recent_rides => 'Recent Rides';

  @override
  String get unknown_street => 'Unknown Street';

  @override
  String get set_drop_point => 'Set Drop Point';

  @override
  String get priority => 'Priority';

  @override
  String get smart => 'Smart';

  @override
  String get deluxe => 'Deluxe';

  @override
  String get select_vehicle => 'Select Vehicle';

  @override
  String get select_drop_off_point => 'Select Drop-off Point';

  @override
  String get select_pick_up_point => 'Select Pick-up Point';

  @override
  String get services => 'Services';

  @override
  String get prebooking => 'Prebooking';

  @override
  String get airport => 'AirPort';

  @override
  String get border_cross => 'Border Cross';

  @override
  String get startup => 'Startup';

  @override
  String get end_trip => 'End Trip';

  @override
  String get swipe_right => 'Swipe Right';

  @override
  String get start_trip => 'Start Trip';

  @override
  String get fetching_place_information => 'Fetching Place Information ...';

  @override
  String get are_you_sure_you_want_to_add_the_police_location => 'Are you sure you want to add the police location ?';

  @override
  String get mark_on_map => 'Mark on Map';

  @override
  String get not_available => 'Not Available';

  @override
  String get available => 'Available';

  @override
  String get member_since => 'Member Since';

  @override
  String get car_brand => 'Car Brand';

  @override
  String get plate_number => 'Plate Number';

  @override
  String get start_time => 'Start Time:';

  @override
  String get counter => 'Counter';

  @override
  String get start => 'Start';

  @override
  String get time => 'Time';

  @override
  String get end_counte => 'End Counter';

  @override
  String get the_price_fare_was => 'The price fare was ';

  @override
  String get okay => 'Okay';

  @override
  String get coupon => 'Coupon';

  @override
  String get wrong_code => 'Wrong Code';

  @override
  String get apply => 'Apply';

  @override
  String get profile => 'Profile';

  @override
  String get account_type => 'Account Type';

  @override
  String get payment => 'Payment';

  @override
  String get supervisor => 'Supervisor';

  @override
  String get technical_support => 'Technical Support';

  @override
  String get car_type => 'Car Type:';

  @override
  String get plate_no => 'Plate No:';

  @override
  String get rating => 'Rating';

  @override
  String get trips => 'Trips';

  @override
  String get years => 'Years';

  @override
  String get do_not_worry => 'Don\'t Worry! We have shared your location to the Trusted emergency contacts and sent a message to them that you are in danger.';

  @override
  String get sharing_location => 'Sharing you location with trusted emergency contacts and alerting them that you are in danger.';

  @override
  String get congratulations_completed_trip => 'Congratulations you\'ve successfully completed the trip';

  @override
  String get done => 'Done';

  @override
  String get add_coupon => 'Add Coupon';

  @override
  String get choose_gender => 'Choose Gender';

  @override
  String get both => 'both';

  @override
  String get get_direction => 'Get Direction';

  @override
  String get police_location => 'Police Location';

  @override
  String get fetching_place_info => 'Fetching Place Information ...';

  @override
  String get choose_radius => 'Choose Radius';

  @override
  String get set => 'Set';

  @override
  String get km => 'KM';

  @override
  String get rate_customer => 'Rate Customer';

  @override
  String get new_request => 'New Request';

  @override
  String get now => 'Now';

  @override
  String get reject => 'Reject';

  @override
  String get accept => 'Accept';

  @override
  String get order_now => 'Order Now';

  @override
  String get cancel => 'Cancel';

  @override
  String get set_time => 'Set Time';

  @override
  String get payment_mode => 'Payment Mode';

  @override
  String get payment_methods => 'Payment Method';

  @override
  String get my_wallet => 'My Wallet';

  @override
  String get balance => 'Balance';

  @override
  String get add_card => 'Add Card';

  @override
  String get a => 'A';

  @override
  String get scheduled_orders => 'Scheduled Orders';

  @override
  String get traffic_hints => 'Traffic Hints';

  @override
  String get heavy_traffic => 'Heavy Traffic';

  @override
  String get light_traffic => 'Light Traffic';

  @override
  String get no_traffic => 'No Traffic';

  @override
  String get balance_accepted_requests => 'Balance';

  @override
  String get working_hours => 'Working Hours';

  @override
  String get number_of_trips => 'Number of Trips';

  @override
  String get rejected_requests => 'Rejected Requests';

  @override
  String get enable_location_permission => 'Please enable location permission to use the map.';

  @override
  String get enable_overlay_permission => 'Please enable overlay permission to use the app in over other application.';

  @override
  String get enable => 'Enable';

  @override
  String get not_now => 'Not now';

  @override
  String get connect_us => 'Connect Us';

  @override
  String get whatsapp => 'Whatsapp';

  @override
  String get telegram => 'Telegram';

  @override
  String get signal => 'Signal';

  @override
  String get instagram => 'Instagram';

  @override
  String get notifications => 'Notifications';

  @override
  String get account_registered_successfully => 'Your account has been registered successfully';

  @override
  String get discount_code_first_two_trips => 'Discount Code On the first two trips';

  @override
  String get valid_until => 'Valid until';

  @override
  String get lite => 'Lite';

  @override
  String get zero_commission_day => 'Zero Commission a day';

  @override
  String get premium => 'Premium';

  @override
  String get zero_commission_week => 'Zero Commission a week';

  @override
  String get offers => 'Offers';

  @override
  String get use_our_products => 'Use our products now!!!';

  @override
  String get our_products => 'Our products';

  @override
  String get congratulations_plan_user => 'Congratulations, you have now a';

  @override
  String get buy_now => 'Buy now';

  @override
  String get address => 'Address';

  @override
  String get address_info => 'Address Info';

  @override
  String get name => 'Name';

  @override
  String get enter_location_name => 'Enter location name';

  @override
  String get address_details => 'Address Details';

  @override
  String get enter_address_detail => 'Enter address detail';

  @override
  String get add_address => 'Add Address';

  @override
  String get home => 'Home';

  @override
  String get college => 'College';

  @override
  String get office => 'Office';

  @override
  String get add_new_address => '+ Add New Addres';

  @override
  String get update => 'Update';

  @override
  String get emergency_contact => 'Emergency Contact';

  @override
  String get delete => 'Delete';

  @override
  String get add_contact => 'Add Contact';

  @override
  String get notification => 'Notification';

  @override
  String get general_notification => 'General Notification';

  @override
  String get sound => 'Sound';

  @override
  String get vibrate => 'Vibrate';

  @override
  String get points => 'Points';

  @override
  String get withdraw_points => 'Witdraw Ponits';

  @override
  String get redeem => 'Redeem';

  @override
  String get first_name => 'First Name';

  @override
  String get last_name => 'Last Name';

  @override
  String get email => 'Email';

  @override
  String get password => 'Password';

  @override
  String get tasks => 'Tasks';

  @override
  String get task => 'Task';

  @override
  String get complete_10_trips_reward => 'Complete 10 trips and you will get a 50% discount on the value taken from you';

  @override
  String get reward => 'Reward';

  @override
  String get cash => 'CASH';

  @override
  String get requests => 'Requests';

  @override
  String get previous_passengers => 'Previous passengers';

  @override
  String get payment_details => 'Payment Details';

  @override
  String get fare => 'Fare';

  @override
  String get yallagai_percentage => 'YallaGai’s Percentage';

  @override
  String get total => 'Total';

  @override
  String get date_and_distances => 'Date and Distances';

  @override
  String get date_and_time => 'Date and Time';

  @override
  String get hour_time_24 => '24-Hour Time';

  @override
  String get distances => 'Distances';

  @override
  String get kilometers => 'Kilometers';

  @override
  String get english => 'English';

  @override
  String get language => 'Language';

  @override
  String get suggested => 'Suggested';

  @override
  String get arabic => 'Arabic';

  @override
  String get change_phone_number => 'Do you want to change your phone number?';

  @override
  String get account_linkage => 'Your account will be linked to your new mobile number.';

  @override
  String get successfully_changed_phone_number => 'Congratulations! You have successfully changed your phone number.';

  @override
  String get change => 'Change';

  @override
  String get privacy_policy => 'Privacy Policy';

  @override
  String get new_phone_number => 'Do you want to use a new phone number?';

  @override
  String get no => 'No';

  @override
  String get yes => 'Yes';

  @override
  String get rules_terms => 'Rules and Terms';

  @override
  String get settings => 'Settings';

  @override
  String get log_out => 'Log Out';

  @override
  String get confirm_log_out => 'Are you sure you want to log out?';

  @override
  String get log_out_warning => 'Logging out of your account will lose your data.';

  @override
  String get terms_conditions => 'Terms and Conditions';

  @override
  String get suggestions_complaints => 'Suggestions & Complaints';

  @override
  String get suggestions => 'Suggestions';

  @override
  String get enter_suggestion => 'Enter your suggestion';

  @override
  String get suggestion => 'Suggestion';

  @override
  String get complaints => 'Complaints';

  @override
  String get enter_complaint => 'Enter your complaint';

  @override
  String get complaint => 'Complaint';

  @override
  String get attach_file_picture => 'Attach any file or picture';

  @override
  String get file_selected => 'File selected: ';

  @override
  String get send => 'Send';

  @override
  String get suggestions_complaints_sent_successfully => 'Suggestions and complaints sent successfully!';

  @override
  String get supervisors_info => 'Supervisors Info';

  @override
  String get select_city => 'Select City';

  @override
  String get tips => 'Tips';

  @override
  String get balance_recharge_card => 'Balance Recharge Card';

  @override
  String get enter_recharge_number => 'Enter recharge number';

  @override
  String get successfully_added_to_wallet => 'Congratulations! We have successfully added an amount to your wallet!';

  @override
  String get mastercard => 'Mastercard';

  @override
  String get wallet => 'Wallet';

  @override
  String get successfully_transferred_from_account => 'Congratulations! You have successfully transferred an amount from your account.';

  @override
  String get b => 'B';

  @override
  String get enter_phone_number => 'Enter phone number';

  @override
  String get pay => 'Pay';

  @override
  String get top_up => 'Top Up';

  @override
  String get total_amount => 'Total Amount:';

  @override
  String get balance_recharge_card_number => 'Balance recharge card number';

  @override
  String get sent => 'Sent';

  @override
  String get received => 'Received';

  @override
  String get select_payment_method => 'Select the payment method you want to use.';

  @override
  String get add_new_card => 'Add new card';

  @override
  String get incentives => 'Incentives';

  @override
  String get priority_of_requests => 'Priority of requests';

  @override
  String get lowest_commision => 'Lowest Commision';

  @override
  String get or => 'or';

  @override
  String get submit => 'Submit';

  @override
  String get cancellation_reason => 'Cancellation Reason';

  @override
  String get enter_other_reason => 'Enter other reason';

  @override
  String get other => 'Other';

  @override
  String get driver_etails => 'Driver Details';

  @override
  String get unknown => 'Unknown';

  @override
  String get accetped_requests => 'Accepted Requests';

  @override
  String get congratulations_you_are_now_a => 'Congratulations, you’re now a';

  @override
  String get plan_user => 'Plan user';

  @override
  String get miles => 'Miles';

  @override
  String get phone_number => 'Phone Number';

  @override
  String get add => 'Add';

  @override
  String get min => 'Min';

  @override
  String get agree_to_all_above_conditions => 'I agree to all conditons above';

  @override
  String get note => 'Note: ';

  @override
  String get no_internet => 'No Internet!!!';

  @override
  String get please_check_your_internet_connection_to_enjoy_our_new_services => 'Please check your internet connection to enjoy our new services';

  @override
  String get time_paid_service => 'The timer is actually paid service and you will get charged 0.02 JOD';

  @override
  String get type_of_car => 'Type of car';

  @override
  String get english_first_name => 'Enlish First Name';

  @override
  String get english_last_name => 'Enlish Last Name';

  @override
  String get arabic_first_name => 'Arabic First Name';

  @override
  String get arabic_last_name => 'Arabic Last Name';

  @override
  String get edit_profile => 'Edit Profile';

  @override
  String get m => 'm';

  @override
  String get account_updated_successfully => 'Your account has been updated successfully';

  @override
  String get car_industry => 'Car Model';

  @override
  String get english_full_name => 'English Full Name';

  @override
  String get car_year => 'Year of Manufacturer';

  @override
  String get arabic_full_name => 'Arabic Full Name';

  @override
  String get swipe_on_arrival => 'Swipe on arrival';

  @override
  String get choose_right_car_type_for_you => 'Choose the categories that match your car';

  @override
  String get choose_right_car_type_for_you_subtitle => 'You can choose multiple options from these categories, it increases the chance of receiving orders.';

  @override
  String get choose_appropriate_car_type_for_service_vehicles => 'Choose the categories that match your car as this category is present in the services available in the city';

  @override
  String get activate_cars_to_receive_orders => 'Activate the cars you want to receive orders from';

  @override
  String get terms_first_part => 'By registering, I\'ve read and agreed to the';

  @override
  String get terms_last_part => '';

  @override
  String get terms_of_service => 'Terms of Service';

  @override
  String get and => 'and';

  @override
  String get please_select_profile_picture => 'Please select profile picture!';

  @override
  String get please_select_car => 'Please select car!';

  @override
  String get please_select_right_car_type_for_you => 'Please select the appropriate car categories.';

  @override
  String get please_select_car_images => 'Please select car images!';

  @override
  String get contact_the_supervisor => 'Contact the supervisor';

  @override
  String get ride_requested_by_another_driver => 'The request has been accepted from another driver';

  @override
  String get trip_request_cancelled => 'The user canceled the trip!';

  @override
  String get delete_account => 'Delete Account';

  @override
  String get delete_account_warning => 'Your data will be permanently deleted.';

  @override
  String get cancel_trip => 'Cancel Trip?';

  @override
  String get cancel_trip_warning => 'A cancellation tax will be deducted if you click OK.';

  @override
  String get commission => 'Commission';

  @override
  String get completed => 'Completed';

  @override
  String get cancelled => 'Cancelled';

  @override
  String get in_the_trip => 'In The Trip';

  @override
  String get min_rating => 'Min. Rating';

  @override
  String get all => 'All';

  @override
  String get update_title => 'Enjoy YallaGai new features';

  @override
  String get update_notice => 'You\'re requested to update your application';

  @override
  String get remind_me_later => 'Remind me later';

  @override
  String get update_now => 'Update now!';

  @override
  String get app_name => 'YallaGai';

  @override
  String get under_maintenance_line1 => 'The app is in maintenance mode, we will be back soon.';

  @override
  String get under_maintenance_line2 => 'A notification will be sent to you soon.';

  @override
  String get enter_email_address => 'Enter your email address';

  @override
  String get incorrect => 'Incorrect';

  @override
  String get card_expired => 'The card has expired';

  @override
  String get invalid_month => 'Invalid month';

  @override
  String get amount_send_note => 'The amount will be sent from your wallet to the other user\'s wallet.';

  @override
  String get open_settings => 'Open Settings';

  @override
  String get location_permission_denied => 'Location Permission Denied';

  @override
  String get location_permission_denied_message => 'You have permanently denied for location usage permission. To enable real-time tracking and ensure a seamless experience, please allow \"Location Usage Permission\" in your device settings.';

  @override
  String get location_permission_consent => 'To provide real-time tracking for your rides and ensure a seamless experience, we require \"Location Always Usage Permission\".\n\nThe YallaGai app collects location data to track your location during trips, even in the background, to deliver a precise and smooth delivery experience.';

  @override
  String get continues => 'Continue';

  @override
  String get enter_valid_name => 'Enter a valid name';

  @override
  String get valid_plate_number => 'Enter a valid plate number';

  @override
  String get valid_date_of_birth => 'Enter valid date of birth!';

  @override
  String get valid_id_number => 'Enter a valid id number';

  @override
  String get overlay_permission_needed => 'Overlay Permission Needed';

  @override
  String get overlay_permission_request => 'To enhance your experience and provide continuous features such as instant notifications and quick access, we need your permission to run the app in the background and display it as a floating bubble on the screen.\nPlease allow Allow app over other Apps Access';

  @override
  String get crop_your_image => 'Crop your image';

  @override
  String get crop_it => 'Crop It!';
}
