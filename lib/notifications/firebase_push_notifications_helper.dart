import 'dart:convert';
import 'dart:developer';
import 'dart:io';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:yalla_gai_driver/core/constants/end_points.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/core/notification/custom_local_notification.dart';

class PushNotificationHelper {
  late BuildContext context;

  static updateFCMToken() async {
    final token = await FirebaseMessaging.instance.getToken();
    if (token != null) {
      log("FirebaseToken: $token", name: "PushNotificationHelper");
      await HttpApiClient().post(EndPoints.updateFCMToken(), body: jsonEncode({"token": token}));
    }
  }

  static deleteFcmToken() async {
    await FirebaseMessaging.instance.deleteToken();
  }

  static Future<void> initialized() async {
    getDeviceToken();
    FirebaseMessaging.onMessage.listen(_onMessageHandler);
    FirebaseMessaging.onMessageOpenedApp.listen(_onMessageOpenedAppHandler);
  }

  static Future<void> getDeviceToken() async {
    final token = await FirebaseMessaging.instance.getToken();
    log("FirebaseToken: $token", name: "PushNotificationHelper");
  }

  static Future<bool> _checkPermission() async {
    if (Platform.isAndroid) {
      final hasPermission = await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
      return hasPermission ?? false;
    } else if (Platform.isIOS) {
      final hasPermission = await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(sound: true, alert: true, badge: true, provisional: true);
      return hasPermission ?? false;
    }
    return false;
  }

  static Future<void> _onMessageHandler(RemoteMessage message) async {
    log("onMessage: ${message.notification?.toMap()}", name: "PushNotificationHelper");

    if (message.notification != null) {
      final hasPermission = await _checkPermission();
      if (hasPermission) {
        final androidDetails = AndroidNotificationDetails(
          'yalla-gai-notification',
          'General Notifications',
          icon: '@drawable/ic_notification',
          priority: Priority.high,
          channelShowBadge: true,
        );
        const iosDetails = DarwinNotificationDetails(categoryIdentifier: "yalla-gai-notification");
        final notificationDetails = NotificationDetails(android: androidDetails, iOS: iosDetails);

        final notificationId = message.hashCode;
        return flutterLocalNotificationsPlugin.show(
          notificationId,
          message.notification?.title,
          message.notification?.body,
          notificationDetails,
          payload: jsonEncode(message.data),
        );
      }
    }
  }

  static Future<void> _onMessageOpenedAppHandler(RemoteMessage message) async {
    log("onMessageOpenedApp: ${message.notification?.toMap()}", name: "PushNotificationHelper");
    if (message.notification != null) {}
  }
}
