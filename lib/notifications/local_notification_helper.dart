/*
import 'dart:developer';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:yalla_gai_driver/core/notification/custom_local_notification.dart';

@pragma('vm:entry-point')
Future<void> localBackgroundHelper(NotificationResponse response) async {
  const notificationDetails = NotificationDetails(
      android: AndroidNotificationDetails("YallaGai", "YallaGai", importance: Importance.max, priority: Priority.high));
  await flutterLocalNotificationsPlugin.show(123123, response.payload, response.payload, notificationDetails,
      payload: response.payload);
}

const DarwinInitializationSettings initializationSettingsDarwin =
    DarwinInitializationSettings(onDidReceiveLocalNotification: onDidReceiveLocalNotification);
const AndroidInitializationSettings initializationSettingsAndroid =
    AndroidInitializationSettings("@drawable/ic_notification");

void onDidReceiveLocalNotification(int id, String? title, String? body, String? payload) async {
  // display a dialog with the notification details, tap ok to go to another page
}

class LocalNoticationHelper {
  static void displayNotification(RemoteMessage message) async {
    try {
      final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      const notificationDetails = NotificationDetails(
        android: AndroidNotificationDetails(
          "YallaGai",
          "YallaGai",
          importance: Importance.max,
          priority: Priority.high,
        ),
      );
      await flutterLocalNotificationsPlugin.show(
        id,
        message.notification?.title,
        message.notification?.body,
        notificationDetails,
        payload: message.data['_id'] ?? "",
      );
    } on Exception catch (e) {
      log("Notification Error:$e");
    }
  }
}
*/
