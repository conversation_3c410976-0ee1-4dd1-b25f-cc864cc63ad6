import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart';
import 'package:yalla_gai_driver/core/error/exception.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/environment.dart';

class HttpApiClient extends BaseClient {
  HttpApiClient();

  final Client _internalClient = Client();

  void _log(String data) {
    developer.log(data, name: 'HttpApiClient');
  }

  void _logError(String data, Object? error, [StackTrace? stackTrace]) {
    developer.log(data, name: 'HttpApiClient', error: error, stackTrace: stackTrace);
  }

  void _logRequest(BaseRequest request) {
    _log("--------------------------------------------- Request Start ---------------------------------------------");
    _log("Request: ${request.method} ${request.url}");
    _log("Headers: ${jsonEncode(request.headers)}");
    if (request is Request) _log("Data: ${request.body}");
    _log("--------------------------------------------- Request End ---------------------------------------------");
  }

  void _logResponse(BaseResponse response) {
    // _log("--------------------------------------------- Response Start ---------------------------------------------");
    // _log("Response: ${response.request?.method} ${response.request?.url}");
    // _log("Status Code: ${response.statusCode}");
    // _log("Headers: ${jsonEncode(response.headers)}");
    // _log("--------------------------------------------- Response End ---------------------------------------------");
  }

  @override
  Future<StreamedResponse> send(BaseRequest request) async {
    final headers = <String, String>{};
    headers[HttpHeaders.acceptHeader] = "application/json";

    if (request is Request && (request.method == 'POST' || request.method == 'PUT')) {
      headers[HttpHeaders.contentTypeHeader] = "application/json";
      final requestSignature = _generateHash(request.bodyBytes);
      headers['x-signature'] = requestSignature;
    }

    final accessToken = LocalStorage.instance.accessToken;
    if (accessToken != null) {
      headers[HttpHeaders.authorizationHeader] = 'Bearer $accessToken';
    }

    request.headers.addAll(headers);
    if (kDebugMode) _logRequest(request);
    try {
      final response = await _internalClient.send(request);
      if (kDebugMode) _logResponse(response);

      if (!response.isSuccess) {
        if (response.statusCode == 401) {
          throw UnauthorizedException();
        }

        final responseBodyBytes = await response.stream.toBytes();
        final responseString = String.fromCharCodes(responseBodyBytes);
        // if (kDebugMode) _log("Response Data: $responseString");
        final responseData = jsonDecode(responseString);
        throw ServerException(message: responseData["message"] ?? responseData["error"] ?? 'Something went wrong');
      }

      final contentTypeHeader = response.headers[HttpHeaders.contentTypeHeader];
      if (response.isSuccess && contentTypeHeader != null && contentTypeHeader.contains("application/json")) {
        final receivedHmac = response.headers['x-signature'];
        final responseBodyBytes = await response.stream.toBytes();
        // _log("Response Data: ${String.fromCharCodes(responseBodyBytes)}");

        if (receivedHmac == null || !_verifyHmac(responseBodyBytes, receivedHmac)) {
          throw InsecureNetworkException();
        }

        return StreamedResponse(
          ByteStream.fromBytes(responseBodyBytes),
          response.statusCode,
          headers: response.headers,
          contentLength: response.contentLength,
          isRedirect: response.isRedirect,
          persistentConnection: response.persistentConnection,
          reasonPhrase: response.reasonPhrase,
          request: response.request,
        );
      }

      return response;
    } catch (error, stackTrace) {
      _logError(error.toString(), error, stackTrace);

      switch (error) {
        case SocketException() || ClientException() || HttpException():
          _logError(error.toString(), error, stackTrace);
          throw NetworkException();
        case InsecureNetworkException() || ServerException() || UnauthorizedException():
          rethrow;
        default:
          throw UnknownException();
      }
    }
  }

  String _generateHash(Uint8List data) {
    final hmac = Hmac(sha256, utf8.encode(AppEnvironment.macSecretKey));
    return hmac.convert(data).toString();
  }

  bool _verifyHmac(Uint8List data, String receivedHmac) {
    final computedHmac = _generateHash(data);
    return computedHmac == receivedHmac;
  }
}

extension $BaseResponseExtension on BaseResponse {
  bool get isSuccess => statusCode >= 200 && statusCode < 300;
}
