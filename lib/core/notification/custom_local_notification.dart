import 'dart:io';
import 'dart:ui';

import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:go_router/go_router.dart';
import 'package:http/http.dart';
import 'package:path_provider/path_provider.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/booking/model/chat.dart';

import '../../../../core/routes/paths.dart' as path;
import '../../features/notification/domain/entity/notification_model.dart';
import '../routes/router_config.dart';

/*@pragma('vm:entry-point')*/
FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
/*
@pragma('vm:entry-point')
void onDidReceiveLocalNotification(int? id, String? title, String? body, String? payload) async {
  flutterLocalNotificationsPlugin.show(
      **********,
      title,
      body,
      const NotificationDetails(
          android: AndroidNotificationDetails(
        "**********",
        "ride-status",
        fullScreenIntent: true,
        importance: Importance.high,
        priority: Priority.high,
      )));

  log("Notification received");
  // display a dialog with the notification details, tap ok to go to another page
}*/

class CustomLocalNotification {
  final AndroidInitializationSettings initializationSettingsAndroid =
      const AndroidInitializationSettings('@drawable/ic_notification');
  final DarwinInitializationSettings initializationSettingsDarwin = const DarwinInitializationSettings();

  init() async {
    final InitializationSettings initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsDarwin,
    );
    flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: onDidReceiveNotificationResponse,
      // onDidReceiveBackgroundNotificationResponse: _onDidReceiveNotificationResponse,
    );
  }

  static requestPermission() async {
    if (Platform.isAndroid) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
    }
    if (Platform.isIOS) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(alert: true, badge: true, sound: true);
    }
  }

  static Future<bool> _checkPermission() async {
    if (Platform.isAndroid) {
      final hasPermission = await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();
      return hasPermission ?? false;
    } else if (Platform.isIOS) {
      final hasPermission = await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions(sound: true, alert: true, badge: true, provisional: true);
      return hasPermission ?? false;
    }
    return false;
  }

  static showChatNotification(ChatMessage message) async {
    final hasPermission = await _checkPermission();
    if (hasPermission != true) return;

    final id = DateTime.now().millisecond;
    flutterLocalNotificationsPlugin.show(
      id,
      "Chat",
      message.message,
      const NotificationDetails(
        android: AndroidNotificationDetails(
          "Chat Notification",
          "Chat Notification",
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(categoryIdentifier: "chat"),
      ),
      payload: "chat_message",
    );
  }

  static showTechSupportNotification(ChatMessage message) async {
    final hasPermission = await _checkPermission();
    if (hasPermission != true) return;

    final id = DateTime.now().second;
    flutterLocalNotificationsPlugin.show(
      id,
      "Tech Support",
      message.message,
      NotificationDetails(
        android: AndroidNotificationDetails(
          "$id",
          "tech support",
          importance: Importance.high,
          priority: Priority.high,
        ),
        iOS: DarwinNotificationDetails(),
      ),
    );
  }

  static showDriverNotification(NotificationMessage message, [Locale? locale]) async {
    final hasPermission = await _checkPermission();
    if (hasPermission != true) return;

    String title = message.title ?? '';
    String body = message.title ?? '';
    if (locale?.languageCode == 'ar') {
      title = message.arTitle ?? title;
      body = message.arMessage ?? body;
    }

    const id = 789;
    final bigPicturePath =
        await downloadAndSavePicture('${AppEnvironment.publicBaseUrl}${message.filePath}', 'bigPicture');
    AndroidNotificationDetails androidPlatformChannelSpecifics = AndroidNotificationDetails(
        "$id", "driver notification",
        channelDescription: 'your channel description',
        importance: Importance.max,
        priority: Priority.high,
        ticker: 'ticker',
        styleInformation: BigPictureStyleInformation(FilePathAndroidBitmap(bigPicturePath!)));
    NotificationDetails platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: DarwinNotificationDetails(),
    );
    flutterLocalNotificationsPlugin.show(
      id,
      title,
      body,
      platformChannelSpecifics,
    );
  }

  static Future<String?> downloadAndSavePicture(String? url, String fileName) async {
    if (url == null) return null;
    final Directory directory = await getApplicationDocumentsDirectory();
    final String filePath = '${directory.path}/$fileName';
    final Response response = await get(Uri.parse(url));
    final File file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);
    return filePath;
  }
}

void onDidReceiveNotificationResponse(NotificationResponse details) {
  print("_onDidReceiveNotificationResponse => ${details.payload}");
  if (details.payload == "chat_message") {
    navigatorKey.currentContext?.push(path.chat);
  }
}
