import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:go_router/go_router.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:yalla_gai_driver/app_locale.dart';
import 'package:yalla_gai_driver/core/socket/custom_socket.dart';
import 'package:yalla_gai_driver/features/account_type/presentation/screen/account_type_detail.dart';
import 'package:yalla_gai_driver/features/account_type/presentation/screen/account_types.dart';
import 'package:yalla_gai_driver/features/authentication/presentation/screen/login_page.dart';
import 'package:yalla_gai_driver/features/authentication/presentation/screen/supervisor_selection_page.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/booking/presentation/screens/tech_support.dart';
import 'package:yalla_gai_driver/features/connect/presentation/screen/connect_us.dart';
import 'package:yalla_gai_driver/features/notification/presentation/screen/new_notification.dart';
import 'package:yalla_gai_driver/features/offer/presentation/screen/offers.dart';
import 'package:yalla_gai_driver/features/profile/presentation/screen/edit_profile.dart';
import 'package:yalla_gai_driver/features/requests/presentation/screen/requests.dart';
import 'package:yalla_gai_driver/features/suggestions/presentation/screen/suggestions_complaints.dart';
import 'package:yalla_gai_driver/features/supervisor/presentation/screens/supervisor_info.dart';
import 'package:yalla_gai_driver/features/tips/presentation/screens/tips.dart';
import 'package:yalla_gai_driver/features/wallet/presentation/screens/balance_recharge_card.dart';
import 'package:yalla_gai_driver/features/wallet/presentation/widgets/payment_methods.dart';

import '../../features/authentication/presentation/screen/choose_mode.dart';
import '../../features/authentication/presentation/screen/forgot_password.dart';
import '../../features/authentication/presentation/screen/onboarding.dart';
import '../../features/authentication/presentation/screen/otp_screen.dart';
import '../../features/authentication/presentation/screen/reset_password.dart';
import '../../features/authentication/presentation/screen/set_profile.dart';
import '../../features/authentication/presentation/screen/sign_up.dart';
import '../../features/authentication/presentation/screen/splash_screen.dart';
import '../../features/authentication/presentation/screen/welcom_screen.dart';
import '../../features/booking/presentation/screens/accept_ride.dart';
import '../../features/booking/presentation/screens/add_card.dart';
import '../../features/booking/presentation/screens/car_detail.dart';
import '../../features/booking/presentation/screens/chat.dart';
import '../../features/booking/presentation/screens/confirm_page.dart';
import '../../features/booking/presentation/screens/driver_details.dart';
import '../../features/booking/presentation/screens/home.dart';
import '../../features/booking/presentation/screens/pick_location.dart';
import '../../features/booking/presentation/screens/pick_up_added.dart';
import '../../features/booking/presentation/screens/ride_request.dart';
import '../../features/booking/presentation/screens/services.dart';
import '../../features/booking/presentation/screens/trip_started.dart' as trip;
import '../../features/profile/presentation/screen/emergency_contact.dart';
import '../../features/setting/presentation/screens/date_and_distance.dart';
import '../../features/setting/presentation/screens/language.dart';
import '../../features/setting/presentation/screens/phone_number.dart';
import '../../features/setting/presentation/screens/privacy_policy.dart';
import '../../features/setting/presentation/screens/rules_and_terms.dart';
import '../../features/setting/presentation/screens/setting.dart';
import '../../features/setting/presentation/screens/terms_and_conditions.dart';
import '../../features/wallet/presentation/screens/wallet.dart';
import '../../features/wallet/presentation/screens/wallet_send.dart';
import '../../features/wallet/presentation/screens/wallet_send_payment.dart';
import '../../features/wallet/presentation/screens/wallet_top_up.dart';
import '../../features/wallet/presentation/screens/wallet_top_up_payment.dart';
import '../../features/wallet/presentation/screens/wallet_top_up_total.dart';
import '../utils/theme.dart';
import 'paths.dart' as path;

final navigatorKey = GlobalKey<NavigatorState>();
final GoRouter _router = GoRouter(
  initialLocation: path.splash,
  navigatorKey: navigatorKey,
  routes: <GoRoute>[
    GoRoute(
      path: path.balanceRechargePage,
      builder: (BuildContext context, GoRouterState state) => const BalanceRechargePage(),
    ),
    GoRoute(
      path: path.splash,
      builder: (BuildContext context, GoRouterState state) => const SplashScreen(),
    ),
    GoRoute(
      path: path.onboarding,
      builder: (BuildContext context, GoRouterState state) => const Onboarding(),
    ),
    GoRoute(
      path: path.welcome,
      builder: (BuildContext context, GoRouterState state) => const WelcomePage(),
    ),
    GoRoute(
      path: path.login,
      builder: (BuildContext context, GoRouterState state) => const LoginPage(),
    ),
    GoRoute(
      path: path.signUp,
      builder: (BuildContext context, GoRouterState state) => const SignUpPage(),
    ),
    GoRoute(
      path: path.editProfile,
      builder: (BuildContext context, GoRouterState state) => const EditProfile(),
    ),
    GoRoute(
      path: path.otp,
      builder: (BuildContext context, GoRouterState state) {
        var extra = state.extra as Map<String, dynamic>;
        return OTPScreen(
          phoneNumber: extra['phoneNumber'],
          isForgotPassword: extra['isForgotPassword'] ?? false,
          isFromChangePhoneNumber: extra['isFromChangePhoneNumber'] ?? false,
        );
      },
    ),
    GoRoute(
      path: path.forgotPassword,
      builder: (BuildContext context, GoRouterState state) => const ForgotPassword(),
    ),
    GoRoute(
      path: path.resetPassword,
      builder: (BuildContext context, GoRouterState state) => const ResetPassword(),
    ),
    GoRoute(
      path: path.chooseMode,
      builder: (BuildContext context, GoRouterState state) => const ChooseMode(),
    ),
    GoRoute(
      path: path.setProfile,
      builder: (BuildContext context, GoRouterState state) => const SetProfile(),
    ),
    GoRoute(
      path: path.home,
      builder: (BuildContext context, GoRouterState state) => const DriverHome(),
    ),
    /*GoRoute(
        path: path.pickLocation,
        builder: (BuildContext context, GoRouterState state) {
          var extra = state.extra as Map<String, dynamic>;
          return PickLocation(
              dropOff: extra['dropOff'] ?? false, source: extra['source'], destination: extra['destination']);
        }),
    GoRoute(
        path: path.pickUpAdded,
        builder: (BuildContext context, GoRouterState state) {
          var extra = state.extra as Map<String, dynamic>;
          return PickUpAdded(
            source: extra['source'],
            destination: extra['destination'],
          );
        }),*/

    GoRoute(
      path: path.addCard,
      builder: (BuildContext context, GoRouterState state) => const AddCard(),
    ),
    GoRoute(
      path: path.wallet,
      builder: (BuildContext context, GoRouterState state) => const Wallet(),
    ),
    GoRoute(
      path: path.walletTopUp,
      builder: (BuildContext context, GoRouterState state) => const WalletTopUp(),
    ),
    GoRoute(
      path: path.walletTopUpTotal,
      builder: (BuildContext context, GoRouterState state) {
        var extra = state.extra as Map<String, int>;
        return WalletTopUpTotal(topUpAmount: extra['topUpAmount']!);
      },
    ),
    // GoRoute(
    //     path: path.walletTopUpPayment,
    //     builder: (BuildContext context, GoRouterState state) {
    //       var extra = state.extra as Map<String, dynamic>;
    //       return WalletTopUpPayment(
    //         phoneNumber: extra['phone_number'],
    //         sendAmount: extra["amount"],
    //       );
    //     }),
    GoRoute(
      path: path.walletTopUpPayment,
      builder: (BuildContext context, GoRouterState state) => const WalletTopUpPayment(),
    ),
    GoRoute(
      path: path.payment,
      builder: (BuildContext context, GoRouterState state) => const PaymentMethods(type: ""),
    ),
    GoRoute(
      path: path.walletSend,
      builder: (BuildContext context, GoRouterState state) => const WalletSend(),
    ),
    GoRoute(
        path: path.walletSendPayment,
        builder: (BuildContext context, GoRouterState state) {
          return const WalletSendPayment();
        },),
    GoRoute(
        path: path.setting,
        builder: (BuildContext context, GoRouterState state) {
          return const SettingsPage();
        },),
    GoRoute(
        path: path.changePhoneNumber,
        builder: (BuildContext context, GoRouterState state) {
          return const ChangePhoneNumber();
        },),
    GoRoute(
        path: path.languageSetting,
        builder: (BuildContext context, GoRouterState state) {
          return const ChangeLanguage();
        },),
    GoRoute(
        path: path.dateAndDistanceSetting,
        builder: (BuildContext context, GoRouterState state) {
          return const DateAndDistance();
        },),
    GoRoute(
        path: path.rulesAndTerms,
        builder: (BuildContext context, GoRouterState state) {
          return const RulesAndTerms();
        },),
    GoRoute(
        path: path.privacyPolicy,
        builder: (BuildContext context, GoRouterState state) {
          return const PrivacyPolicy();
        },),
    GoRoute(
        path: path.termsAndConditions,
        builder: (BuildContext context, GoRouterState state) {
          return const TermsAndConditions();
        },),
    /*GoRoute(
      path: path.carDetail,
      builder: (BuildContext context, GoRouterState state) => const CarDetail(),
    ),*/
    /*GoRoute(
        path: path.confirmBooking,
        builder: (BuildContext context, GoRouterState state) {
          var extra = state.extra as Map<String, dynamic>;
          return ConfirmBooking(source: extra['source'], destination: extra['destination']);
        }),*/

    GoRoute(
      path: path.notification,
      builder: (BuildContext context, GoRouterState state) => const Notifications(),
    ),
    // GoRoute(
    //   path: path.address,
    //   builder: (BuildContext context, GoRouterState state) => Address(),
    // ),
    // GoRoute(
    //   path: path.addAddress,
    //   builder: (BuildContext context, GoRouterState state) => AddAddress(),
    // ),
    GoRoute(
      path: path.emergencyContact,
      builder: (BuildContext context, GoRouterState state) => const EmergencyContact(),
    ),

    GoRoute(
      path: path.chat,
      builder: (BuildContext context, GoRouterState state) => const Chat(),
    ),
    GoRoute(
      path: path.driverDetails,
      builder: (BuildContext context, GoRouterState state) => const DriverDetails(),
    ),
    GoRoute(
      path: path.connectUs,
      builder: (BuildContext context, GoRouterState state) => const ConnectUs(),
    ),
    GoRoute(
      path: path.offers,
      builder: (BuildContext context, GoRouterState state) => const Offers(),
    ),
    GoRoute(
      path: path.accountType,
      builder: (BuildContext context, GoRouterState state) => const AccountTypes(),
    ),
    GoRoute(
        path: path.accountTypeDetail,
        builder: (BuildContext context, GoRouterState state) {
          var extra = state.extra as Map<String, dynamic>;
          return AccountTypeDetail(
            selectedIndex: extra['selectedIndex'],
            accountTypeModel: extra["account_type"],
          );
        },),
    GoRoute(
      path: path.supervisor,
      builder: (BuildContext context, GoRouterState state) => const SupervisorsInfo(),
    ),
    GoRoute(
        path: path.newRideRequest,
        builder: (BuildContext context, GoRouterState state) {
          var extra = state.extra as Map<String, dynamic>;
          return NewRideRequest(
            request: extra['rideRequest'],
            countdown: extra['countDown'],
          );
        },),
    GoRoute(
        path: path.acceptRide,
        builder: (BuildContext context, GoRouterState state) {
          // var extra = state.extra as Map<String, dynamic>;
          return const AcceptRide(
              // requestAccept: extra['requestAccept'],
              );
        },),
    GoRoute(
        path: path.tripStarted,
        builder: (BuildContext context, GoRouterState state) {
          var extra = state.extra as Map<String, dynamic>;
          return trip.TripStarted(
            rideRequest: extra['rideRequest'],
            requestAccept: extra['requestAccept'],
          );
        },),
    GoRoute(
      path: path.suggestionsAndComplaints,
      builder: (BuildContext context, GoRouterState state) => const SuggestionsPage(),
    ),
    GoRoute(
      path: path.suggestionsAndComplaints,
      builder: (BuildContext context, GoRouterState state) => const SuggestionsPage(),
    ),
    GoRoute(
      path: path.suggestionsAndComplaints,
      builder: (BuildContext context, GoRouterState state) => const SuggestionsPage(),
    ),
    GoRoute(
      path: path.services,
      builder: (BuildContext context, GoRouterState state) => const ServicesPage(),
    ),
    GoRoute(
      path: path.requests,
      builder: (BuildContext context, GoRouterState state) => const Requests(),
    ),
    GoRoute(
      path: path.transitionPage,
      builder: (BuildContext context, GoRouterState state) => const TransisionPageFromLogin(),
    ),
    GoRoute(
      path: path.techSupport,
      builder: (BuildContext context, GoRouterState state) => const TechSupport(),
    ),
    GoRoute(
      path: path.tips,
      builder: (BuildContext context, GoRouterState state) => const Tips(),
    ),
  ],
);

class AppRouter extends StatelessWidget {
  const AppRouter({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => AppLocale(),
      builder: (context, child) {
        final provider = Provider.of<AppLocale>(context);

        return BlocListener<TripBloc, TripState>(
          listener: (context, state) {
            if (state is TripUserUnauthorized) {
              // CustomSocket.disconnect();
              SharedPreferences.getInstance().then((value) {
                value.clear();
                _router.go(path.login);
                _router.refresh();
              });
            }
          },
          child: MaterialApp.router(
            debugShowCheckedModeBanner: false,
            routeInformationProvider: _router.routeInformationProvider,
            routeInformationParser: _router.routeInformationParser,
            routerDelegate: _router.routerDelegate,
            theme: appTheme,
            locale: provider.locale,
            // locale: Locale("ar"),
            supportedLocales: AppLocalizations.supportedLocales,
            localizationsDelegates: AppLocalizations.localizationsDelegates,
          ),
        );
      },
    );
  }
}
