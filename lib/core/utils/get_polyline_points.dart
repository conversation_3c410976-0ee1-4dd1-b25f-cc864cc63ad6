import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/services.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

List<LatLng> decodeEncodedPolyline(String encoded) {
  List<LatLng> poly = [];
  int index = 0, len = encoded.length;
  int lat = 0, lng = 0;

  while (index < len) {
    int b, shift = 0, result = 0;
    do {
      b = encoded.codeUnitAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);
    int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
    lat += dlat;

    shift = 0;
    result = 0;
    do {
      b = encoded.codeUnitAt(index++) - 63;
      result |= (b & 0x1f) << shift;
      shift += 5;
    } while (b >= 0x20);
    int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
    lng += dlng;
    LatLng p = LatLng((lat / 1E5).toDouble(), (lng / 1E5).toDouble());
    poly.add(p);
  }
  return poly;
}

LatLng getCenterPoint(LatLng point1, LatLng point2) {
  double lat1 = point1.latitude * math.pi / 180;
  double lon1 = point1.longitude * math.pi / 180;
  double lat2 = point2.latitude * math.pi / 180;
  double lon2 = point2.longitude * math.pi / 180;

  double dLon = lon2 - lon1;

  double bx = math.cos(lat2) * math.cos(dLon);
  double by = math.cos(lat2) * math.sin(dLon);

  double latMid =
      math.atan2(math.sin(lat1) + math.sin(lat2), math.sqrt((math.cos(lat1) + bx) * (math.cos(lat1) + bx) + by * by));
  double lonMid = lon1 + math.atan2(by, math.cos(lat1) + bx);

  return LatLng(latMid * 180 / math.pi, lonMid * 180 / math.pi);
}

LatLngBounds computeBounds(Iterable<LatLng> list) {
  assert(list.isNotEmpty);
  var firstLatLng = list.first;
  var s = firstLatLng.latitude, n = firstLatLng.latitude, w = firstLatLng.longitude, e = firstLatLng.longitude;
  for (var latlng in list) {
    s = math.min(s, latlng.latitude);
    n = math.max(n, latlng.latitude);
    w = math.min(w, latlng.longitude);
    e = math.max(e, latlng.longitude);
  }
  return LatLngBounds(southwest: LatLng(s, w), northeast: LatLng(n, e));
}

double calculateBearing(LatLng start, LatLng end) {
  double lat1 = start.latitude * math.pi / 180;
  double lon1 = start.longitude * math.pi / 180;
  double lat2 = end.latitude * math.pi / 180;
  double lon2 = end.longitude * math.pi / 180;

  double dLon = lon2 - lon1;

  double y = math.sin(dLon) * math.cos(lat2);
  double x = math.cos(lat1) * math.sin(lat2) - math.sin(lat1) * math.cos(lat2) * math.cos(dLon);

  double bearing = math.atan2(y, x);
  bearing = (bearing * 180 / math.pi + 360) % 360; // Normalize to 0-360 degrees

  return bearing;
}

Future<BitmapDescriptor> getBitmapDescriptor(String asset, [double size = 60]) async {
  return rootBundle.load(asset).then((value) async {
    ui.Codec codec = await ui.instantiateImageCodec(value.buffer.asUint8List(), targetHeight: size.toInt());
    ui.FrameInfo fi = await codec.getNextFrame();
    final byteImage = (await fi.image.toByteData(format: ui.ImageByteFormat.png))?.buffer.asUint8List();
    return BitmapDescriptor.bytes(Uint8List.view(byteImage!.buffer));
  });
}
