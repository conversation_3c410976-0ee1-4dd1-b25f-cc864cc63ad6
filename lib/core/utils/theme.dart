import 'package:flutter/material.dart';

import 'colors.dart';

const fontFamily = 'Plus Jakarta Sans';

var appTheme = ThemeData(
    primaryColor: primaryColor,
    dialogBackgroundColor: white,
    drawerTheme: const DrawerThemeData(backgroundColor: white),
    useMaterial3: true,
    scaffoldBackgroundColor: white,
    indicatorColor: primaryColor,
    appBarTheme: const AppBarTheme(
      backgroundColor: white,
    ),
    colorScheme: ColorScheme.fromSeed(seedColor: primaryColor),
    textSelectionTheme: TextSelectionThemeData(
      cursorColor: primaryColor,
      selectionHandleColor: primaryColor,
      selectionColor: primaryColor.withOpacity(0.1),
    ),
    inputDecorationTheme: const InputDecorationTheme(
        focusedBorder: UnderlineInputBorder(
      borderSide: BorderSide(color: primaryColor),
    )),
    switchTheme: SwitchThemeData(
      trackOutlineColor: WidgetStateColor.resolveWith((states) => primaryColor),
      trackColor: WidgetStateColor.resolveWith((states) => primaryColor),
      thumbColor: WidgetStateColor.resolveWith((states) => white),
    ),
    radioTheme: RadioThemeData(
      materialTapTargetSize: MaterialTapTargetSize.padded,
      fillColor: WidgetStateColor.resolveWith((states) => primaryColor),
    ),
    textTheme: TextTheme(
      labelSmall: TextStyle(
        fontFamily: fontFamily,
        color: black.withOpacity(0.5),
        fontWeight: FontWeight.w400,
        fontSize: 15,
      ),
      bodyLarge: const TextStyle(
        fontFamily: fontFamily,
        color: black,
        fontWeight: FontWeight.bold,
        fontSize: 18,
      ),
      bodyMedium: const TextStyle(
        fontFamily: fontFamily,
        color: black,
        fontWeight: FontWeight.w500,
        fontSize: 18,
      ),
      bodySmall: const TextStyle(
        fontFamily: fontFamily,
        color: black,
        fontSize: 15,
      ),
      labelMedium: const TextStyle(
        fontFamily: fontFamily,
        color: black,
        fontWeight: FontWeight.w700,
        fontSize: 16,
      ),
      labelLarge: const TextStyle(
        fontFamily: fontFamily,
        color: blackTextColor,
        fontWeight: FontWeight.w500,
        fontSize: 16,
      ),
      displayLarge: const TextStyle(
        fontFamily: fontFamily,
        fontSize: 25,
        fontWeight: FontWeight.w600,
      ),
      displayMedium: const TextStyle(fontFamily: fontFamily, fontSize: 19, fontWeight: FontWeight.w700, color: black),
    ));
