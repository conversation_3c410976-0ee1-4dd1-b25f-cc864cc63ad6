import 'dart:math' show asin, cos, sin, sqrt, pow, pi;

import 'package:google_maps_flutter/google_maps_flutter.dart';

double _haversineDistance(LatLng p1, LatLng p2) {
  const int radiusOfEarth = 6371; // in km
  final double lat1 = p1.latitude;
  final double lon1 = p1.longitude;
  final double lat2 = p2.latitude;
  final double lon2 = p2.longitude;

  final double dLat = _toRadians(lat2 - lat1);
  final double dLon = _toRadians(lon2 - lon1);

  final double a = pow(sin(dLat / 2), 2) +
      cos(_toRadians(lat1)) * cos(_toRadians(lat2)) * pow(sin(dLon / 2), 2);

  final double c = 2 * asin(sqrt(a));
  return radiusOfEarth * c;
}

double _toRadians(double degree) => degree * pi / 180;

List<LatLng> mergeCoordinates(List<LatLng> coordinates) {
  List<LatLng> mergedCoordinates = [];

  for (int i = 0; i < coordinates.length; i++) {
    int count = 0;
    LatLng center = coordinates[i];

    for (int j = i + 1; j < coordinates.length; j++) {
      if (_haversineDistance(center, coordinates[j]) <= 2) {
        center = LatLng(
          (center.latitude * count + coordinates[j].latitude) / (count + 1),
          (center.longitude * count + coordinates[j].longitude) / (count + 1),
        );
        count++;
        coordinates.removeAt(j--);
      }
    }

    mergedCoordinates.add(center);
  }

  return mergedCoordinates.toList();
}

List<LatLng> filterCoordinates(List<LatLng> mergedCoordinates) {
  Map<LatLng, int> countMap = {};
  for (LatLng coordinate in mergedCoordinates) {
    if (!countMap.containsKey(coordinate)) {
      countMap[coordinate] = mergedCoordinates
          .where((c) => _haversineDistance(coordinate, c) <= 2)
          .length;
    }
  }

  return countMap.entries
      .where((entry) => entry.value > 10)
      .map((entry) => entry.key)
      .toList();
}

List<LatLng> amanCoordinates = const [
  LatLng(31.9516, 35.9234),
  LatLng(31.9517, 35.9235),
  LatLng(31.9518, 35.9236),
  LatLng(31.9519, 35.9237),
  LatLng(31.9520, 35.9238),
  LatLng(31.9521, 35.9239),
  LatLng(31.9522, 35.9240),
  LatLng(31.9523, 35.9241),
  LatLng(31.9524, 35.9242),
  LatLng(31.9525, 35.9243),
  LatLng(31.9526, 35.9244),
  LatLng(31.9527, 35.9245),
  LatLng(31.9528, 35.9246),
  LatLng(31.9529, 35.9247),
  LatLng(31.9530, 35.9248),
  LatLng(31.9531, 35.9249),
  LatLng(31.9532, 35.9250),
  LatLng(31.9533, 35.9251),
  LatLng(31.9534, 35.9252),
  LatLng(31.9535, 35.9253),
  LatLng(31.9536, 35.9254),
  LatLng(31.9537, 35.9255),
  LatLng(31.9538, 35.9256),
  LatLng(31.9539, 35.9257),
  LatLng(31.9540, 35.9258),
  LatLng(31.9541, 35.9259),
  LatLng(31.9542, 35.9260),
  LatLng(31.9543, 35.9261),
  LatLng(31.9544, 35.9262),
  LatLng(31.9545, 35.9263),
  LatLng(31.9546, 35.9264),
  LatLng(31.9547, 35.9265),
  LatLng(31.9548, 35.9266),
  LatLng(31.9549, 35.9267),
  LatLng(31.9550, 35.9268),
  LatLng(31.9551, 35.9269),
  LatLng(31.9552, 35.9270),
  LatLng(31.9553, 35.9271),
  LatLng(31.9554, 35.9272),
  LatLng(31.9555, 35.9273),
  LatLng(31.9556, 35.9274),
  LatLng(31.9557, 35.9275),
  LatLng(31.9558, 35.9276),
  LatLng(31.9559, 35.9277),
  LatLng(31.9560, 35.9278),
  LatLng(31.9561, 35.9279),
  LatLng(31.9562, 35.9280),
  LatLng(31.9563, 35.9281),
  LatLng(31.9564, 35.9282),
  LatLng(31.9565, 35.9283),
  LatLng(31.9566, 35.9284),
  LatLng(31.9567, 35.9285),
  LatLng(31.9568, 35.9286),
  LatLng(31.9569, 35.9287),
  LatLng(31.9570, 35.9288),
  LatLng(31.9571, 35.9289),
  LatLng(31.9572, 35.9290),
  LatLng(31.9573, 35.9291),
  LatLng(31.9574, 35.9292),
  LatLng(31.9575, 35.9293),
  LatLng(31.9576, 35.9294),
  LatLng(31.9577, 35.9295),
  LatLng(31.9578, 35.9296),
  LatLng(31.9579, 35.9297),
  LatLng(31.9580, 35.9298),
  LatLng(31.9581, 35.9299),
  LatLng(31.9582, 35.9300),
  LatLng(31.9583, 35.9301),
  LatLng(31.9584, 35.9302),
  LatLng(31.9584, 35.9302),
  LatLng(31.9585, 35.9303),
  LatLng(31.9586, 35.9304),
  LatLng(31.9587, 35.9305),
  LatLng(31.9588, 35.9306),
  LatLng(31.9589, 35.9307),
  LatLng(31.9590, 35.9308),
  LatLng(31.9591, 35.9309),
  LatLng(31.9592, 35.9310),
  LatLng(31.9593, 35.9311),
  LatLng(31.9594, 35.9312),
  LatLng(31.9595, 35.9313),
  LatLng(31.9596, 35.9314),
  LatLng(31.9597, 35.9315),
  LatLng(31.9598, 35.9316),
  LatLng(31.9599, 35.9317),
  LatLng(31.9600, 35.9318),
  LatLng(31.9601, 35.9319),
  LatLng(31.9602, 35.9320),
  LatLng(31.9603, 35.9321),
  LatLng(31.9604, 35.9322),
  LatLng(31.9605, 35.9323),
  LatLng(31.9606, 35.9324),
  LatLng(31.9607, 35.9325),
  LatLng(31.9608, 35.9326),
  LatLng(31.9609, 35.9327),
  LatLng(31.9610, 35.9328),
  LatLng(31.9611, 35.9329),
  LatLng(31.9612, 35.9330),
  LatLng(31.9613, 35.9331),
  LatLng(31.9614, 35.9332),
  LatLng(31.9615, 35.9333),
  LatLng(31.9616, 35.9334),
  LatLng(31.9617, 35.9335),
  LatLng(31.9618, 35.9336),
  LatLng(31.9619, 35.9337),
  LatLng(31.9620, 35.9338),
  LatLng(31.9621, 35.9339),
  LatLng(31.9622, 35.9340),
  LatLng(31.9623, 35.9341),
  LatLng(31.9624, 35.9342),
  LatLng(31.9625, 35.9343),
  LatLng(31.9626, 35.9344),
  LatLng(31.9627, 35.9345),
  LatLng(31.9628, 35.9346),
  LatLng(31.9629, 35.9347),
  LatLng(31.9630, 35.9348),
  LatLng(31.9631, 35.9349),
  LatLng(31.9632, 35.9350),
  LatLng(31.9633, 35.9351),
  LatLng(31.9634, 35.9352),
  LatLng(31.9635, 35.9353),
  LatLng(31.9636, 35.9354),
  LatLng(31.9637, 35.9355),
  LatLng(31.9638, 35.9356),
  LatLng(31.9639, 35.9357),
  LatLng(31.9640, 35.9358),
  LatLng(31.9641, 35.9359),
  LatLng(31.9642, 35.9360),
  LatLng(31.9643, 35.9361),
  LatLng(31.9644, 35.9362),
  LatLng(31.9645, 35.9363),
  LatLng(31.9646, 35.9364),
  LatLng(31.9647, 35.9365),
  LatLng(31.9648, 35.9366),
  LatLng(31.9649, 35.9367),
  LatLng(31.9650, 35.9368),
  LatLng(31.9651, 35.9369),
  LatLng(31.9652, 35.9370),
  LatLng(31.9653, 35.9371),
];


