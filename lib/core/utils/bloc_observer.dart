import 'dart:developer' as developer;

import 'package:flutter_bloc/flutter_bloc.dart';

class MyGlobalObserver extends BlocObserver {
  @override
  void onEvent(Bloc bloc, Object? event) {
    super.onEvent(bloc, event);
    // developer.log('onEvent: $event', name: bloc.runtimeType.toString());
  }

  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    // developer.log('onChange: $change', name: bloc.runtimeType.toString());
  }

  @override
  void onTransition(Bloc bloc, Transition transition) {
    super.onTransition(bloc, transition);
    // developer.log('onTransition: $transition', name: bloc.runtimeType.toString());
  }

  @override
  void onError(BlocBase bloc, Object error, StackTrace stackTrace) {
    // developer.log(
    //   'onError: ',
    //   name: bloc.runtimeType.toString(),
    //   error: error,
    //   stackTrace: stackTrace,
    // );
    super.onError(bloc, error, stackTrace);
  }
}
