import 'dart:developer';

import 'package:url_launcher/url_launcher.dart';

void launchGoogleMapsApp(
    double destinationLatitude, double destinationLongitude) async {
      final String destination = '$destinationLatitude,$destinationLongitude';
  final String googleMapsUri =
      'https://maps.google.com?q=$destination&z=15&dirflg=d';
   try {
    if (await canLaunchUrl(Uri.parse(googleMapsUri))) {
      await launchUrl(Uri.parse(googleMapsUri));
    } else {
      throw 'Could not launch Google Maps';
    }
  } catch (e) {
    log('Error launching Google Maps: $e');
  }
}
