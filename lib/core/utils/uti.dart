import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';

class Utils {
  static String phoneLocale() {
    final deviceLocale = LocalStorage.instance.languageCode ?? PlatformDispatcher.instance.locale.languageCode;
    if (deviceLocale.startsWith("ar")) {
      return "ar";
    }
    return 'en';
  }

  static formatDate(date) {
    DateTime dateTime = DateTime.parse(date);

    return DateFormat('hh MMM d, hh:mm a').format(dateTime);
  }

  static String appText({required BuildContext context, required String text, bool listen = true, String? arabicText}) {
    if (arabicText == null) {
      return text;
    }

    final languageCode = Localizations.localeOf(context).languageCode;
    return languageCode == "ar" ? arabicText : text;
  }

  static bool isAribic(BuildContext context, {bool listen = true}) {
    return Localizations.localeOf(context).languageCode == "ar";
  }
}
