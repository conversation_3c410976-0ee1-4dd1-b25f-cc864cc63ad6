import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/cubit/carmodel_cubit.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/links/links_cubit.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/chat/chats_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/connectivity/connectivity_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/counter-cubit/counter_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/nearby_users/nearby_users_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/ride_request/ride_request_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/secutity-circles/security_circle_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/system_service/system_services_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/tech_support/tech_supports_cubit.dart';
import 'package:yalla_gai_driver/features/booking/repository/counter_repository.dart';
import 'package:yalla_gai_driver/features/booking/repository/service_repository.dart';
import 'package:yalla_gai_driver/features/booking/repository/system_service_repository.dart';
import 'package:yalla_gai_driver/features/connect/data/remote/contact_us_repo.dart';
import 'package:yalla_gai_driver/features/connect/presentation/bloc/cubit/contactus_cubit.dart';
import 'package:yalla_gai_driver/features/notification/bloc/cubit/notification_cubit.dart';
import 'package:yalla_gai_driver/features/offer/data/remote/offer_repo.dart';
import 'package:yalla_gai_driver/features/offer/presentation/bloc/cubit/offer_cubit.dart';
import 'package:yalla_gai_driver/features/setting/bloc/app_version/app_version_cubit.dart';
import 'package:yalla_gai_driver/features/setting/repository/settings_repository.dart';
import 'package:yalla_gai_driver/features/suggestions/bloc/trip_bloc/suggestion_bloc.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/cubit/payment_cubit.dart';
import 'package:yalla_gai_driver/features/wallet/bloc/wallet_bloc/wallet_bloc.dart';

import '../../features/account_type/bloc/chat/account__type_bloc.dart';
import '../../features/authentication/bloc/language/language_bloc.dart';
import '../../features/authentication/repository/car_repo.dart';
import '../../features/booking/bloc/service_bloc/service_bloc.dart';
import '../../features/booking/bloc/trip_bloc/trip_bloc.dart';
import '../../features/notification/repository/notification_repo.dart';
import '../../features/setting/bloc/terms_and_condition/terms_and_condition_bloc.dart';
import '../../features/supervisor/bloc/chat/neighbourhood_bloc.dart';
import '../../features/tips/bloc/trip_bloc/tip_bloc.dart';
import '../../features/wallet/repository/wallet_repository.dart';

List<BlocProvider> getAllBlocProviders() {
  return [
    /*BlocProvider<SignInBloc>(create: (_) => getIt<SignInBloc>()),*/
    BlocProvider<AuthBloc>(create: (context) => AuthBloc()),
    BlocProvider<TermsAndConditionsBloc>(create: (context) => TermsAndConditionsBloc()),
    BlocProvider<WalletBloc>(create: (context) => WalletBloc()),
    BlocProvider<NotificationCubit>(create: (context) => NotificationCubit(NotificationRepository())),
    BlocProvider<TripBloc>(create: (context) => TripBloc()),
    BlocProvider<TipBloc>(create: (context) => TipBloc()),
    BlocProvider<CounterCubit>(create: (context) => CounterCubit(CounterRepository())),
    BlocProvider<SuggestionBloc>(create: (context) => SuggestionBloc()),
    BlocProvider<AccountTypeBloc>(create: (context) => AccountTypeBloc()),
    BlocProvider<NeighbourHoodBloc>(create: (context) => NeighbourHoodBloc()),
    BlocProvider<ServiceBloc>(create: (context) => ServiceBloc()),
    BlocProvider<PaymentCubit>(create: (context) => PaymentCubit(WalletRepository())),
    BlocProvider<LanaguageBloc>(create: (context) => LanaguageBloc()),
    BlocProvider<ChatsCubit>(create: (context) => ChatsCubit()),
    BlocProvider<CarmodelCubit>(create: (context) => CarmodelCubit(CarRepo())),
    BlocProvider<TechSupportsCubit>(create: (context) => TechSupportsCubit()),
    BlocProvider<RideRequestCubit>(create: (context) => RideRequestCubit()),
    BlocProvider<OfferCubit>(create: (context) => OfferCubit(OfferRepo())),
    BlocProvider<ContactusCubit>(create: (context) => ContactusCubit(ContactUsRepo())),
    BlocProvider<SecurityCircleCubit>(create: (context) => SecurityCircleCubit()),
    BlocProvider<ConnectivityCubit>(create: (context) => ConnectivityCubit()),
    BlocProvider<ConnectivityCubit>(create: (context) => ConnectivityCubit()),
    BlocProvider<LinksCubit>(create: (context) => LinksCubit(AuthRepository())),
    BlocProvider<NearbyUserCubit>(create: (context) => NearbyUserCubit(ServiceRepository())),
    BlocProvider<AppVersionCubit>(create: (context) => AppVersionCubit(SettingsRepository())),
    BlocProvider<SystemServicesCubit>(create: (context) => SystemServicesCubit(SystemServiceRepository())),
  ];
}
