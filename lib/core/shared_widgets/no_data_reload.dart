import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';


class NoDataReload extends StatelessWidget {
  final void Function()? onPressed;
  final double? height;
  final double? width;

  const NoDataReload({super.key, required this.onPressed, this.width, this.height});

  @override
  Widget build(BuildContext context) {
    return  Column(
      children: [
        Text(AppLocalizations.of(context)!.no_data)
      ],
    );
  }
}
