import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/core/shared_widgets/gap.dart';
import 'package:yalla_gai_driver/features/booking/bloc/connectivity/connectivity_cubit.dart';

class AppScaffold extends StatelessWidget {
  const AppScaffold(
      {super.key,
      this.drawer,
      required this.body,
      this.backgroundColor,
      this.isLoading = false,
      this.drawerScrimColor,
      this.resizeToAvoidBottomInset = true,
      this.floatingActionButtonLocation,
      this.floatingActionButton,
      this.bottomSheet,
      this.appBar});

  final Widget? drawer, floatingActionButton, bottomSheet;
  final Widget body;
  final Color? backgroundColor, drawerScrimColor;
  final bool isLoading;
  final PreferredSizeWidget? appBar;
  final FloatingActionButtonLocation? floatingActionButtonLocation;
  final bool resizeToAvoidBottomInset;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      drawer: drawer,
      backgroundColor: backgroundColor,
      drawerScrimColor: drawerScrimColor,
      appBar: appBar,
      floatingActionButton: floatingActionButton,
      floatingActionButtonLocation: floatingActionButtonLocation,
      bottomSheet: bottomSheet,
      resizeToAvoidBottomInset: resizeToAvoidBottomInset,
      body: AbsorbPointer(
        absorbing: isLoading,
        child: Opacity(
          opacity: isLoading ? 0.5 : 1.0,
          child: Stack(
            children: [
              body,
              isLoading
                  ? const Center(
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation(primaryColor),
                      ),
                    )
                  : const SizedBox.shrink(),
            ],
          ),
        ),
      ),
    );
  }
}

///
///no internet
///
class NoConnection extends StatelessWidget {
  const NoConnection({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return BlocListener<ConnectivityCubit, ConnectivityState>(
      listener: (context, state) {
        if (state is ConnectivityConnected) {
          Navigator.pop(context);
        }
      },
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppLocalizations.of(context)!.no_internet,
            style: const TextStyle(
                color: Colors.red, fontWeight: FontWeight.bold, fontSize: 24),
          ),
          const Gap(),
          Text(
              AppLocalizations.of(context)!
                  .please_check_your_internet_connection_to_enjoy_our_new_services,
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16)),
          Gap(
            h: 1.h,
          ),
        ],
      ),
    );
  }
}
