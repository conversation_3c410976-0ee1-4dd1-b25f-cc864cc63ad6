import 'package:flutter/material.dart';
import 'package:responsive_sizer/responsive_sizer.dart';

import '../utils/colors.dart';

class CustomButton extends StatelessWidget {
  /// an alternative flexible button where width and height
  /// are minimum constraints
  static StatelessWidget flexible({
    Color? borderColor,
    Color? color,
    required double borderRadius,
    bool borderOnly = false,
    double? height,
    bool? isLoading,
    bool oneSideOff = false,
    double? width,
    required String buttonText,
    void Function()? onPressed,
  }) =>
      _CustomButtonFlexible(
          buttonText: buttonText,
          borderRadius: borderRadius,
          borderOnly: borderOnly,
          height: height,
          color: color,
          oneSideOff: oneSideOff,
          width: width,
          isLoading: isLoading,
          borderColor: borderColor,
          onPressed: onPressed,);

  const CustomButton(
      {required this.buttonText,
      required this.borderRadius,
      this.borderOnly = false,
      this.height,
      this.color,
      this.oneSideOff = false,
      this.width,
      this.isLoading,
      this.borderColor,
      super.key,
      this.onPressed,});
  final Color? borderColor;
  final Color? color;
  final double borderRadius;
  final bool borderOnly;
  final double? height;
  final bool? isLoading;
  final bool oneSideOff;
  final double? width;
  final String buttonText;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final border = BorderSide(
        width: 1,
        color: borderColor ??
            (borderOnly ? black.withOpacity(0.2) : Colors.transparent),);

    return InkWell(
      onTap: onPressed,
      child: Container(
        width: width ?? 96.w,
        height: height ?? 8.h,
        padding: EdgeInsets.symmetric(horizontal: (4).w),
        decoration: BoxDecoration(
          borderRadius: oneSideOff
              ? BorderRadius.only(
                  topLeft: Radius.zero,
                  topRight: Radius.circular(borderRadius),
                  bottomLeft: Radius.circular(borderRadius),
                  bottomRight: Radius.circular(borderRadius),
                )
              : BorderRadius.circular(borderRadius),
          color: borderOnly
              ? white
              : onPressed != null
                  ? color ?? primaryColor
                  : primaryColor.withOpacity(0.3),
          border: onPressed != null
              ? Border(bottom: border, top: border, left: border, right: border)
              : Border.all(
                  width: 0, color: borderOnly ? black.withOpacity(0.3) : white,),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading == true)
              const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            if (isLoading == true)
              const SizedBox(
                width: 5,
              ),
            Center(
              child: Text(buttonText,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.labelMedium!.copyWith(
                        color: borderOnly ? black : white,
                      ),),
            ),
          ],
        ),
      ),
    );
  }
}

class _CustomButtonFlexible extends StatelessWidget {
  const _CustomButtonFlexible(
      {required this.buttonText,
      required this.borderRadius,
      required this.borderOnly,
      required this.height,
      required this.color,
      required this.oneSideOff,
      required this.width,
      required this.isLoading,
      required this.borderColor,
      required this.onPressed,});
  final Color? borderColor;
  final Color? color;
  final double borderRadius;
  final bool borderOnly;
  final double? height;
  final bool? isLoading;
  final bool oneSideOff;
  final double? width;
  final String buttonText;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final border = BorderSide(
        width: 1,
        color: borderColor ??
            (borderOnly ? black.withOpacity(0.2) : Colors.transparent),);

    return InkWell(
      onTap: onPressed,
      child: Container(
        constraints: BoxConstraints(
          minWidth: width ?? 85.w,
          minHeight: height ?? 6.h,
        ),
        padding: EdgeInsets.symmetric(horizontal: (4).w),
        decoration: BoxDecoration(
          borderRadius: oneSideOff
              ? BorderRadius.only(
                  topLeft: Radius.zero,
                  topRight: Radius.circular(borderRadius),
                  bottomLeft: Radius.circular(borderRadius),
                  bottomRight: Radius.circular(borderRadius),
                )
              : BorderRadius.circular(borderRadius),
          color: borderOnly
              ? white
              : onPressed != null
                  ? color ?? primaryColor
                  : primaryColor.withOpacity(0.3),
          border: onPressed != null
              ? Border(bottom: border, top: border, left: border, right: border)
              : Border.all(
                  width: 0, color: borderOnly ? black.withOpacity(0.3) : white,),
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (isLoading == true)
              const SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  color: Colors.white,
                ),
              ),
            if (isLoading == true)
              const SizedBox(
                width: 5,
              ),
            Center(
              child: Text(buttonText,
                  textAlign: TextAlign.center,
                  style: Theme.of(context).textTheme.labelMedium!.copyWith(
                        color: borderOnly ? black : white,
                      ),),
            ),
          ],
        ),
      ),
    );
  }
}
