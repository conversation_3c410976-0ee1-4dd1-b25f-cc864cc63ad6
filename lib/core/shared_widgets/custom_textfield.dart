import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../utils/colors.dart';

class CustomTextField extends StatefulWidget {
  const CustomTextField({
    this.multiline = false,
    this.hasBorder,
    this.height,
    required this.width,
    this.keyboardType,
    required this.borderRadius,
    this.icon,
    this.onChanged,
    this.isPassword = false,
    this.readOnly = false,
    required this.validator,
    Key? key,
    required this.textEditingController,
    required this.labelText,
    this.labelColor,
    this.inputFormatters,
    this.textColor,
    this.hintText,
  }) : super(key: key);

  final bool multiline;
  final double width;
  final double? height;
  final bool isPassword;
  final bool readOnly;
  final double borderRadius;
  final Widget? icon;
  final TextInputType? keyboardType;
  final String labelText;
  final String? hintText;
  final Function(String)? onChanged;
  final String? Function(String?) validator; // Updated validator parameter
  final TextEditingController textEditingController;
  final Color? labelColor;
  final Color? textColor;
  final bool? hasBorder;
  final List<TextInputFormatter>? inputFormatters;

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  final FocusNode _textFieldFocusNode = FocusNode();

  @override
  void dispose() {
    _textFieldFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      obscureText: widget.isPassword,
      validator: widget.validator,
      autovalidateMode: AutovalidateMode.onUserInteraction,
      maxLines: widget.multiline ? null : 1,
      style: Theme.of(context).textTheme.bodyMedium!.copyWith(
            fontFamily: "Plus Jakarta Sans",
            fontWeight: FontWeight.w700,
            color: widget.textColor ?? primaryColor,
            fontSize: 18,
          ),
      inputFormatters: widget.inputFormatters,
      readOnly: widget.readOnly,
      // Decreased the font size
      keyboardType: widget.keyboardType,
      cursorColor: widget.textColor ?? primaryColor,
      focusNode: _textFieldFocusNode,
      controller: widget.textEditingController,
      onChanged: widget.onChanged,
      decoration: InputDecoration(
        constraints: BoxConstraints(maxWidth: widget.width),
        hintText: widget.hintText,
        hintStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontFamily: "Plus Jakarta Sans",
              fontWeight: FontWeight.w400,
              color: widget.labelColor ?? black.withOpacity(0.5),
              fontSize: 13,
            ),
        focusColor: primaryColor,
        suffixIcon: widget.icon,
        labelText: widget.labelText,
        // Changed label to labelText
        labelStyle: Theme.of(context).textTheme.bodyMedium!.copyWith(
              fontFamily: "Plus Jakarta Sans",
              fontWeight: FontWeight.w400,
              color: widget.labelColor ?? black.withOpacity(0.5),
              fontSize: 13,
            ),
        // Decreased the font size and adjusted the position
        border: widget.hasBorder != null && widget.hasBorder!
            ? OutlineInputBorder(
                borderSide: const BorderSide(color: primaryColor),
                borderRadius: BorderRadius.circular(widget.borderRadius),
              )
            : const UnderlineInputBorder(
                borderSide: BorderSide(color: primaryColor),
              ),
      ),
    );
  }
}
