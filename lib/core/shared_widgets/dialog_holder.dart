import 'dart:ui';

import 'package:flutter/material.dart';

showCustomDialog(
  BuildContext context,
  Widget content,
) {
  showGeneralDialog(
    barrierDismissible: false,
    barrierLabel: '',
    barrierColor: Colors.black12,
    transitionDuration: const Duration(milliseconds: 100),
    pageBuilder: (ctx, anim1, anim2) =>
        Dialog.fullscreen(backgroundColor: Colors.transparent, child: content),
    transitionBuilder: (ctx, anim1, anim2, child) => BackdropFilter(
      filter:
          ImageFilter.blur(sigmaX: 6 * anim1.value, sigmaY: 6 * anim1.value),
      child: FadeTransition(
        opacity: anim1,
        child: child,
      ),
    ),
    context: context,
  );
}
