import 'dart:async';

import 'package:shared_preferences/shared_preferences.dart';

class LocalStorage {
  LocalStorage._();

  static final LocalStorage instance = LocalStorage._();

  late SharedPreferences _preferences;

  Future<void> init() async {
    _preferences = await SharedPreferences.getInstance();
  }

  String? getString(String key) => _preferences.getString(key);

  void setString(String key, String value) {
    _preferences.setString(key, value);
  }

  Future<void> clear() {
    return _preferences.clear();
  }

  String? get accessToken => _preferences.getString('token');

  String? get userId => _preferences.getString("id");

  String? get roleId => _preferences.getString("role_id");

  String? get languageCode => _preferences.getString("languageCode");

  bool? get isFirstTime => _preferences.getBool("isFirstTime");

  bool? get askLocationPermission => _preferences.getBool("askLocationPermission");

  set accessToken(String? value) {
    if (value == null) {
      _preferences.remove('token');
      return;
    }
    _preferences.setString('token', value);
  }

  set userId(String? value) {
    if (value == null) {
      _preferences.remove('id');
      return;
    }
    _preferences.setString('id', value);
  }

  set roleId(String? value) {
    if (value == null) {
      _preferences.remove('role_id');
      return;
    }
    _preferences.setString('role_id', value);
  }

  set waitTime(double value) {
    _preferences.setDouble('waitTime', value);
  }

  set counterFees(double value) {
    _preferences.setDouble('counterFees', value);
  }

  set languageCode(String? value) {
    if (value == null) {
      _preferences.remove('languageCode');
      return;
    }
    _preferences.setString('languageCode', value);
  }

  set isFirstTime(bool? value) {
    if (value == null) {
      _preferences.remove('isFirstTime');
      return;
    }
    _preferences.setBool('isFirstTime', value);
  }

  set askLocationPermission(bool? value) {
    if (value == null) {
      _preferences.remove('askLocationPermission');
      return;
    }
    _preferences.setBool('askLocationPermission', value);
  }
}
