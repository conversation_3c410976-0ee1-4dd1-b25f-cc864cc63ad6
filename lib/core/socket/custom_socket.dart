import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
// import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/notification/custom_local_notification.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/authentication/bloc/auth_bloc/auth_bloc.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';
import 'package:yalla_gai_driver/features/booking/bloc/chat/chats_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/ride_request/ride_request_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/secutity-circles/security_circle_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/tech_support/tech_supports_cubit.dart';
import 'package:yalla_gai_driver/features/booking/bloc/trip_bloc/trip_bloc.dart';
import 'package:yalla_gai_driver/features/notification/bloc/cubit/notification_cubit.dart';
import 'package:yalla_gai_driver/features/notification/domain/entity/notification_model.dart';

class PusherEvents {
  final String? eventName;
  final String? channelName;
  final dynamic data;

  PusherEvents({
    required this.eventName,
    required this.channelName,
    required this.data,
  });

  factory PusherEvents.fromJson(Map<String, dynamic> json) {
    return PusherEvents(
      eventName: json['event'],
      channelName: json['channel'],
      data: jsonDecode(json['data']),
    );
  }

  @override
  String toString() => 'PusherEvents(eventName: $eventName, channelName: $channelName, data: $data)';
}

class CustomSocket {
  static Completer<bool> connectionCompleter = Completer();
  static bool connection = false;
  static WebSocketChannel? channel;
  static String? socketIds;
  static final List<String> channels = [];
  static Duration connectionTimeout = const Duration(seconds: 30);
  static Timer? _heartbeatTimer;

  static Future<void> connectAndListener({
    required BuildContext context,
  }) async {
    if (channel != null) return;
    connection = true;

    developer.log("Connecting to WebSocket...", name: "CustomSocket");

    final uri = Uri.parse(AppEnvironment.websocketUrl);

    // _channel = IOWebSocketChannel.connect(
    //   uri,
    //   headers: {
    //     'Authorization': 'Bearer ${LocalStorage.instance.accessToken}',
    //     'Accept': 'application/json',
    //   },
    // );

    final channelFuture = Future<WebSocketChannel>.delayed(
      Duration.zero,
      () => IOWebSocketChannel.connect(
        uri,
        headers: {
          'Authorization': 'Bearer ${LocalStorage.instance.accessToken}',
          'Accept': 'application/json',
        },
      ),
    );

    developer.log(
      "Connecting to WebSocket... token ${LocalStorage.instance.accessToken}",
      name: "CustomSocket",
    );

    channel = await channelFuture.timeout(
      connectionTimeout,
      onTimeout: () {
        throw TimeoutException('Connection timeout', connectionTimeout);
      },
    );
    
    developer.log("WebSocket connected", name: "CustomSocket");

    channel!.stream.listen(
      (message) async {
        developer.log("==>>> socket listening: $message");
        final decoded = json.decode(message);
        final event = decoded['event'];

        print('Received: $message');
_startHeartbeat();
        //pong feature
        //----->>>
          // if (decoded['event'] == 'pusher:ping') {
          //   // الرد برسالة pong
          //   channel?.sink.add(jsonEncode({
          //     "event": "pusher:pong",
          //   }),);
          // }
        //----->>>
        if (event == 'pusher:connection_established') {
          socketIds = json.decode(decoded['data'])['socket_id'];
          developer.log(
            "Connected with socketId: $socketIds",
            name: "CustomSocket",
          );
          await _subscribeInitialChannels(context, socketIds!);
          connectionCompleter.complete(true);
        } else if (decoded.containsKey('channel')) {
          final pusherEvent = PusherEvents.fromJson(decoded);
          await _handleEvent(pusherEvent, context);
        }
      },
      onError: (error) {
        onError("WebSocket error", null, error);
      },
      onDone: () {
        developer.log("WebSocket closed", name: "CustomSocket");
        channel = null;
        connection = false;
        // connectAndListener(context: context);
      },
    );
  }

   static void _startHeartbeat() {
    _heartbeatTimer?.cancel();
    _heartbeatTimer = Timer.periodic(Duration(seconds: 30), (_) {
      developer.log('WebSocket: Sending heartbeat ping');
      channel?.sink.add(jsonEncode({
              "event": "pusher:pong",
            }),);
    });
  }
  static Future<void> disconnect() async {
    developer.log("disconnect", name: "CustomSocket");
    await channel?.sink.close();
    channel = null;
    channels.clear();
  }

  static Future<void> _subscribeInitialChannels(
    BuildContext context,
    String socketId,
  ) async {
    String city = LocalStorage.instance.getString("cityId") ?? '';
    var userId = globalDriverProfile.user?.id;

    // reSubscribeChannel(
    //   channelName: "ban-user_$userId",
    //   socketId: socketId,
    // );
    // reSubscribeChannel(
    //   channelName: "un-ban-user_$userId",
    //   socketId: socketId,
    // );
    reSubscribeChannel(
      channelName: "private-ban-user_$userId",
      socketId: socketId,
    );
    reSubscribeChannel(
      channelName: "private-un-ban-user_$userId",
      socketId: socketId,
    );

    reSubscribeChannel(
      channelName: "private-update-location-channel",
      socketId: socketId,
    );
    reSubscribeChannel(
      channelName: "private-accept-request-channel_$city",
      socketId: socketId,
    );
    reSubscribeChannel(
      channelName:
          "private-dashboard-technical-support-channel_${LocalStorage.instance.userId}",
      socketId: socketId,
    );
    reSubscribeChannel(
      channelName:
          "dashboard-technical-support-channel_${LocalStorage.instance.userId}",
      socketId: socketId,
    );
    reSubscribeChannel(
      channelName: "private-send-notification-to-driver-channel_$city",
      socketId: socketId,
    );
    reSubscribeChannel(
      channelName: "send-notification-to-driver-channel_$city",
      socketId: socketId,
    );
    

    if (globalDriverProfile.user?.avalabilityStatus?.toLowerCase() == "on") {
      await subscribeNewReqChannel(userId ?? '');
    }

    context.read<SecurityCircleCubit>().subscribe();
  }

  static Future<void> subscribeNewReqChannel(String userId) async {
    await reSubscribeChannel(
      channelName: "private-enduser-send-new-request-channel_$userId",
    );
    await reSubscribeChannel(
      channelName: "private-enduser-send-new-request-event$userId",
    );
    await reSubscribeChannel(
      channelName: "enduser-send-new-request-channel_$userId",
    );
    await reSubscribeChannel(
      channelName: "enduser-send-new-request-event$userId",
    );
  }

  static Future<void> reSubscribeChannel({
    required String channelName,
    String? socketId,
  }) async {


    
    if (!channels.contains(channelName)) {
      await onAuthorizer(channelName, socketId ?? socketIds ?? '', (authData) {
        final payload = {
          "event": "pusher:subscribe",
          "data": {"channel": channelName, 'auth': authData},
        };
        channel?.sink.add(jsonEncode(payload));
        channels.add(channelName);
        developer.log("Subscribed => $channelName", name: "CustomSocket");
      });
    }
  }

  static Future<void> unsubscribeChannel({required String channelName}) async {
    final payload = {
      "event": "pusher:unsubscribe",
      "data": {"channel": channelName},
    };
    channel?.sink.add(jsonEncode(payload));
    channels.remove(channelName);
    developer.log("Unsubscribed => $channelName", name: "CustomSocket");
  }

  static Future<void> _handleEvent(
    PusherEvents event,
    BuildContext context,
  ) async {
    final channel = event.channelName ?? '';
    final data = event.data;
developer.log('--> $event  == data => ${event.data} ', name: "CustomSocket");
    if (channel ==
        'dashboard-technical-support-channel_${LocalStorage.instance.userId}') {
      context.read<TechSupportsCubit>().newTechSupportsEvent(event);
      return;
    }

    if (channel.contains("ban-user_${globalDriverProfile.user?.id}")) {
      if (data['message'] != null) {
        context.read<AuthBloc>().add(const AuthGetProfile());
      }
    }
    if (channel.contains("unban-ban-user_${globalDriverProfile.user?.id}")) {
      if (data['message'] != null) {
        context.read<AuthBloc>().add(const AuthGetProfile());
      }
    }

    context.read<SecurityCircleCubit>().onReceiveLocations(event);

    final newRequest = context.read<RideRequestCubit>().requestData;

    if (channel.contains("accept-request-done-channel_${newRequest?.id}") &&
        data['message'] != null) {
      context.read<RideRequestCubit>().accepted(event: event);
    }
    if (channel.contains("private-accept-request-done-channel_${newRequest?.id}") &&
        data['message'] != null) {
      context.read<RideRequestCubit>().accepted(event: event);
    }

    if (channel.contains(
      'user-send-message-channel_${globalDriverProfile.user?.id}',
    )) {
      context.read<ChatsCubit>().newChatEvent(event);
    }

    final city = LocalStorage.instance.getString("cityId") ?? '';
    if (channel.contains('send-notification-to-driver-channel_$city')) {
        
      }
      final res = data['message'];
      if (res!=null && res.isNotEmpty ) {
      developer.log(
        "send-notification-to-driver-channel_$city: ${res.runtimeType}",
        name: "CustomSocket",
      );
      final newData = NotificationMessage.fromJson(res);
      context.read<NotificationCubit>().onAcceptEvent(event);
      if ((newData.userId?.isEmpty ?? true) ||
          newData.userId == globalDriverProfile.user?.id) {
        CustomLocalNotification.showDriverNotification(
          newData,
          Localizations.localeOf(context),
        );
      }
    }

    developer.log(
      "enduser-send-new-request-channel_${globalDriverProfile.user?.id} ==== $channel",
      name: "CustomSocket",
    );

    if (channel.contains( 
      'enduser-send-new-request-channel_${globalDriverProfile.user?.id}',
    )) {
      print("New request event: $event");
      context.read<RideRequestCubit>().newRequestEvent(event);
    }

    if (channel.contains("cancel-ride-channel_${newRequest?.userId}") &&
        data["message"] != null) {
      final cancelledBy = data["message"]["cancelled_by"];
      if (cancelledBy == newRequest?.userId) {
        // FlutterOverlayWindow.closeOverlay();
        context.read<RideRequestCubit>().requestCancelled(event);
        context.read<TripBloc>().add(GetActiveTrip());
        // await unsubscribeChannel(channelName: channel);
      }
    }
  }

  static void onError(String message, int? code, dynamic error) {
    developer.log(
      "Error: $message | Code: $code | $error",
      name: "CustomSocket",
    );
  }

  static Future<void> onAuthorizer(
    String channelName,
    String socketId,
    Function(dynamic authData) onDone,
  ) async {

if(socketId == ''){
  developer.log(
      "socketId is empty ==> $channelName ",

      name: "CustomSocket",
    );
  return;
}

    final reqBody =jsonEncode({
        'channel_name': channelName,
        'socket_id': socketId,
      });
    final response = await http.post(
      Uri.parse(AppEnvironment.authSocketUrl),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${LocalStorage.instance.accessToken}',
      },
      body: reqBody,
    );

    developer.log(
      "Authorizing channel: $channelName with socketId: $socketId",
      name: "CustomSocket",
    );

    if (response.statusCode == 200) {
      developer.log(
        "Authorization success => $channelName",
        name: "CustomSocket",
      );
      final authResponse = jsonDecode(response.body);
      final authData = authResponse['auth'];
      onDone(authData);
    } else {
      developer.log(
        "Authorization failed => $channelName    \n Body ==>${response.body} \n reqBody ==>$reqBody",
        name: "CustomSocket",
      );
      throw Exception('Auth failed');
    }
  }

  Future<void> updateLocation(double latitude, double longitude) async {
    developer.log(
      "Update location: lat=$latitude, lng=$longitude",
      name: "CustomSocket",
    );
    // Here you could trigger a custom event if needed
  }
}
