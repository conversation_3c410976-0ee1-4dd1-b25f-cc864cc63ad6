import 'package:intl/intl.dart';

class ServerException implements Exception {
  final String message;

  ServerException({required this.message});

  @override
  String toString() {
    return message;
  }
}

class UnauthorizedException implements Exception {
  UnauthorizedException();
}

class CacheException implements Exception {}

class InsecureNetworkException implements Exception {
  InsecureNetworkException();

  @override
  String toString() {
    return Intl.getCurrentLocale().contains("ar") ? "فشل في الاتصال" : "Failed to connect!";
  }
}

class NetworkException implements Exception {
  NetworkException();

  @override
  String toString() {
    return Intl.getCurrentLocale().contains("ar")
        ? "تعذر الاتصال بخادمنا! يرجى التحقق من اتصالك بالإنترنت!"
        : "Unable to connect our server! Please check your internet connection!";
  }
}

class ValidationException implements Exception {}

class UnknownException implements Exception {
  UnknownException();

  @override
  String toString() {
    return Intl.getCurrentLocale().contains("ar")
        ? "حدث خطأ ما! يرجى المحاولة بعد فترة."
        : "Something went wrong! Please try after sometime.";
  }
}
