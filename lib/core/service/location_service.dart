import 'dart:async';
import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:geolocator/geolocator.dart' as geolocator;
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:stream_transform/stream_transform.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/utils/colors.dart';

class LocationService extends ChangeNotifier with WidgetsBindingObserver {
  StreamSubscription<Position>? _locationSubscription;
  final StreamController<Position> _locationStreamController = StreamController.broadcast();
  Position? _currentLocation;
  bool _started = false;

  Position? get currentLocation => _currentLocation;

  Stream<Position> get locationStream => _locationStreamController.stream;

  Stream<geolocator.ServiceStatus> get locationServiceStream => Geolocator.getServiceStatusStream();

  Future<bool> get locationServiceEnabled => Geolocator.isLocationServiceEnabled();

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.detached:
        break;
      case AppLifecycleState.resumed:
        if (_started) _shouldStart();
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.hidden:
        // if (_started) _shouldPause();
        break;
      case AppLifecycleState.paused:
        break;
    }
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    _locationStreamController.close();
    _started = false;
    super.dispose();
  }

  Future<void> start() async {
    _started = true;
    _shouldStart();
    _log("Started");
  }

  Future<Position> getCurrentLocation() async {
    LocationPermission permissionStatus = await checkPermission();
    if (permissionStatus == LocationPermission.denied) {
      permissionStatus = await requestPermission();
      if (permissionStatus != LocationPermission.always || permissionStatus != LocationPermission.whileInUse) {
        throw Exception("Location permission denied");
      }
    }

    final position = await Geolocator.getCurrentPosition(
      locationSettings: LocationSettings(accuracy: LocationAccuracy.bestForNavigation),
    );
    _onLocationUpdated(position);
    return position;
  }

  Future<void> _shouldStart() async {
    final permissionStatus = await Geolocator.checkPermission();
    switch (permissionStatus) {
      case LocationPermission.unableToDetermine:
      case LocationPermission.denied:
      case LocationPermission.deniedForever:
        break;
      case LocationPermission.whileInUse || LocationPermission.always:
        if (_locationSubscription == null) _listenLocationChanges();
    }
  }

  Future<LocationPermission> requestPermission() async {
    final permissionStatus = await Geolocator.requestPermission();
    return permissionStatus;
  }

  Future<LocationPermission> checkPermission() async {
    final permissionStatus = await Geolocator.checkPermission();
    return permissionStatus;
  }

  /*Future<void> _shouldPause() async {
    _locationSubscription?.cancel();
    _locationSubscription = null;
  }*/

  Future<void> _getCurrentLocation() async {
    final position = await Geolocator.getCurrentPosition(
      locationSettings: LocationSettings(accuracy: LocationAccuracy.bestForNavigation),
    );
    _onLocationUpdated(position);
  }

  void _listenLocationChanges() {
    const locationSettings = LocationSettings(
      accuracy: LocationAccuracy.bestForNavigation,
      distanceFilter: 1,
    );
    _locationSubscription = Geolocator.getPositionStream(locationSettings: locationSettings)
        .throttle(const Duration(seconds: 2, milliseconds: 500), trailing: true)
        .listen(_onLocationUpdated);
  }

  void _onLocationUpdated(Position position) {
    _log("latitude: ${position.latitude} longitude: ${position.longitude} heading: ${position.heading} ");
    _currentLocation = position;
    _locationStreamController.add(position);
    notifyListeners();
  }

  void _log(Object content) {
    developer.log(content.toString(), name: 'LocationService');
  }
}

mixin LocationMixin<S extends StatefulWidget> on State<S> {
  Position? _currentPosition;
  StreamSubscription<Position>? _locationSubscription;
  StreamSubscription<geolocator.ServiceStatus>? _locationServiceSubscription;

  Position? get currentPosition => _currentPosition;

  late final LocationService _locationService;

  @override
  void initState() {
    super.initState();
    _locationService = context.read<LocationService>();
    _currentPosition = _locationService.currentLocation;
    _locationSubscription = _locationService.locationStream.listen(onLocationUpdated);
    if (!_locationService._started) {
      resolveLocationPermission().then((value) {
        if (value) onLocationPermissionGranted();
      });
    }
  }

  @override
  void dispose() {
    _locationSubscription?.cancel();
    _locationServiceSubscription?.cancel();
    super.dispose();
  }

  @protected
  @mustCallSuper
  void onLocationUpdated(Position position) {
    setState(() {
      _currentPosition = position;
    });
  }

  @mustCallSuper
  Future<void> onLocationPermissionGranted() async {
    await _locationService._getCurrentLocation().catchError((error) {});

    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (serviceEnabled) {
      onLocationServiceEnabled();
    }

    if (_locationServiceSubscription != null) return;
    _locationServiceSubscription = _locationService.locationServiceStream.listen((event) {
      if (event == geolocator.ServiceStatus.enabled) {
        onLocationServiceEnabled();
      }
    });
  }

  @mustCallSuper
  void onLocationServiceEnabled() {
    _locationService.start();
  }

  Future<bool> resolveLocationPermission() async {
    PermissionStatus permissionStatus = await Permission.locationWhenInUse.status;

    switch (permissionStatus) {
      case PermissionStatus.denied:
        final result = await _requestLocationPermission();
        return result;
      case PermissionStatus.permanentlyDenied:
        _handlePermissionDenied();
        break;
      case PermissionStatus.limited || PermissionStatus.granted:
        if (LocalStorage.instance.askLocationPermission ?? true) {
          await _requestLocationAlwaysUsagePermission();
        }
        break;
      case PermissionStatus.provisional:
      case PermissionStatus.restricted:
        break;
    }

    return permissionStatus == PermissionStatus.granted || permissionStatus == PermissionStatus.limited;
  }

  Future<bool> _requestLocationPermission() async {
    PermissionStatus permissionStatus = await Permission.locationWhenInUse.request();

    switch (permissionStatus) {
      case PermissionStatus.denied || PermissionStatus.permanentlyDenied:
        _handlePermissionDenied();
        return false;
      case PermissionStatus.granted || PermissionStatus.limited:
        if (LocalStorage.instance.askLocationPermission ?? true) {
          await _requestLocationAlwaysUsagePermission();
        }
      case PermissionStatus.provisional:
      case PermissionStatus.restricted:
        break;
    }

    return permissionStatus == PermissionStatus.limited || permissionStatus == PermissionStatus.granted;
  }

  Future<bool> _requestLocationAlwaysUsagePermission() async {
    PermissionStatus permissionStatus = await Permission.locationAlways.status;
    if (permissionStatus == PermissionStatus.limited || permissionStatus == PermissionStatus.granted) {
      return true;
    }

    if (!mounted) return false;

    bool? shouldContinue = await showDialog<bool>(
      context: context,
      builder: (context) => _LocationPermissionAlert(),
      barrierDismissible: false,
    );

    LocalStorage.instance.askLocationPermission = false;
    if (shouldContinue ?? false) {
      PermissionStatus permissionStatus = await Permission.locationAlways.request();
      return permissionStatus == PermissionStatus.limited || permissionStatus == PermissionStatus.granted;
    }

    return false;
  }

  Future<void> _handlePermissionDenied() async {
    await showDialog<bool>(
      context: context,
      builder: (context) => _LocationPermissionDeniedAlert(),
    );
  }
}

class _LocationPermissionAlert extends StatelessWidget {
  const _LocationPermissionAlert();

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context)!;

    return Dialog(
      clipBehavior: Clip.hardEdge,
      insetPadding: EdgeInsets.all(24),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Material(
            color: primaryColor,
            child: SizedBox(
              height: 120,
              child: Icon(Icons.location_on_outlined, size: 64, color: Colors.white),
            ),
          ),
          Padding(
            padding: EdgeInsets.all(16),
            child: Text(
              localizations.location_permission_consent,
              style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              alignment: WrapAlignment.end,
              children: [
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: dangerColor),
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    localizations.cancel,
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: primaryColor),
                  onPressed: () => Navigator.of(context).pop(true),
                  child: Text(
                    localizations.continues,
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}

class _LocationPermissionDeniedAlert extends StatelessWidget {
  const _LocationPermissionDeniedAlert();

  @override
  Widget build(BuildContext context) {
    final AppLocalizations localizations = AppLocalizations.of(context)!;

    return Dialog(
      clipBehavior: Clip.hardEdge,
      insetPadding: EdgeInsets.all(24),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.all(16),
            child: Column(
              children: [
                Text(
                  localizations.location_permission_denied,
                  style: TextStyle(fontWeight: FontWeight.w600, fontSize: 18),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 16),
                Text(
                  localizations.location_permission_denied_message,
                  style: TextStyle(fontWeight: FontWeight.w500, fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Wrap(
              alignment: WrapAlignment.end,
              children: [
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: dangerColor),
                  onPressed: () => Navigator.of(context).pop(false),
                  child: Text(
                    localizations.cancel,
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
                TextButton(
                  style: TextButton.styleFrom(foregroundColor: primaryColor),
                  onPressed: () {
                    Geolocator.openAppSettings();
                    Navigator.of(context).pop(true);
                  },
                  child: Text(
                    localizations.open_settings,
                    style: TextStyle(fontWeight: FontWeight.w600),
                  ),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
