import 'dart:convert';
import 'dart:developer';

import 'package:http/http.dart';
import 'package:yalla_gai_driver/core/constants/end_points.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/network/api_client.dart';
import 'package:yalla_gai_driver/environment.dart';

Future<void> getBaseUrls() async {
  List<BaseUrl> urls = [];
  try {
    final response = await get(
      EndPoints.fetchBaseUrls,
      headers: {'Accept': 'application/json'},
    );
    if (response.isSuccess) {
      final responseData = jsonDecode(response.body)['data'];
      for (var info in responseData) {
        urls.add(BaseUrl.fromJson(info));
      }
      LocalStorage.instance.setString("base-urls", jsonEncode(urls.map((e) => e.toJson()).toList()));
      return;
    } else {
      final localUrlsString = LocalStorage.instance.getString("base-urls");
      if (localUrlsString != null) {
        final localUrls = (jsonDecode(localUrlsString) as Iterable).map((e) => BaseUrl.fromJson(e)).toList();
        for (var baseUrl in localUrls) {
          try {
            final response = await get(
              Uri.parse("${baseUrl.domain}/common/domains/list?role_id=4"),
              headers: {'Accept': 'application/json'},
            );
            if (response.isSuccess) {
              AppEnvironment.apiBaseUrl = "${baseUrl.domain}/";
              break;
            }
          } catch (e) {
            log("Exception:$e");
          }
        }
      }
    }
  } catch (e) {
    log("getBaseUrls error: $e");
  }
}

class BaseUrl {
  String? id;
  String? domain;
  String? ip;
  String? name;

  BaseUrl({
    this.id,
    this.domain,
    this.ip,
    this.name,
  });

  BaseUrl.fromJson(Map<String, dynamic> json) {
    id = json['id']?.toString();
    domain = json['domain'];
    ip = json['ip'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['domain'] = domain;
    data['ip'] = ip;
    data['name'] = name;
    return data;
  }
}
