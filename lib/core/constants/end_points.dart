import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/authentication/repository/auth_repository.dart';

class EndPoints {
  static Uri get fetchBaseUrls => Uri.parse("${AppEnvironment.apiBaseUrl}common/domains/list?role_id=4");

  static Uri get sendOtp => Uri.parse("${AppEnvironment.apiBaseUrl}driver/send-otp");

  static Uri get verifyOtp => Uri.parse("${AppEnvironment.apiBaseUrl}driver/verify-otp");

  static Uri get profilestore => Uri.parse("${AppEnvironment.apiBaseUrl}driver/profile/store");

  static Uri get profileUpdate => Uri.parse("${AppEnvironment.apiBaseUrl}driver/profile/update");

  static Uri get profileUpdateAccountType =>
      Uri.parse("${AppEnvironment.apiBaseUrl}driver/profile/update-account-type");

  static Uri get getCarBrand => Uri.parse("${AppEnvironment.apiBaseUrl}common/cars/models");

  static Uri get getCarModel => Uri.parse("${AppEnvironment.apiBaseUrl}common/cars/brands");

  static Uri getCarCategory(id) =>
      Uri.parse("${AppEnvironment.apiBaseUrl}common/cars/categories?city_id=$id&service_id=");

  static Uri get getProfile => Uri.parse("${AppEnvironment.apiBaseUrl}driver/profile/show");

  static Uri get getLanguage => Uri.parse("${AppEnvironment.apiBaseUrl}common/language/list");

  static Uri get updateLanguage => Uri.parse("${AppEnvironment.apiBaseUrl}common/language/update");

  static Uri get getGeneralInformation => Uri.parse("${AppEnvironment.apiBaseUrl}driver/profile/information");

  static Uri get getUserTripInformation =>
      Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/accepted-rejected-numbers");

  static Uri get changeAvaliablity =>
      Uri.parse("${AppEnvironment.apiBaseUrl}driver/profile/change-availability-status");

  static Uri get contactUrl => Uri.parse("${AppEnvironment.apiBaseUrl}common/contact-us");

  static Uri get changePhoneNumber => Uri.parse("${AppEnvironment.apiBaseUrl}driver/mobile-number/change");

  static Uri get verifyPhoneChange => Uri.parse("${AppEnvironment.apiBaseUrl}driver/mobile-number/verify");

  static Uri getPrivacyPolicy(String id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/privacy-policy/show/$id");
  }

  static Uri getTermsAndConditions() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/conditions");
  }

  static Uri generalLinks(String roleId) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/app_rating/list?role_id=$roleId");
  }

  static Uri getTips(String roleId) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/tip/list?role_id=$roleId");
  }

  static Uri updateFCMToken() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/notification/update-fmc-token");
  }

  static Uri getMyWallet(String driverID) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/wallet/?driver_id=$driverID");
  }

  static Uri getMyTransaction() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/wallet/transactions");
  }

  static Uri rechargeCard(id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/payment-card/recharge-card?recharge_card_id=$id");
  }

  static Uri rechargeCardCopy() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/wallet/wallet-to-wallet");
  }

  static Uri getStartTrip(String id, String newRequestId) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/start/$id?new_request_id=$newRequestId");
  }

  static Uri endtrip(String id, String newRequestId) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/end/$id?new_request_id=$newRequestId");
  }

  static Uri storeRadius() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/new-request/store-radius");
  }

  static Uri updateLocation({bool byGoogle = false}) {
    return byGoogle
        ? Uri.parse("${AppEnvironment.apiBaseUrl}common/location/update-by-google")
        : Uri.parse("${AppEnvironment.apiBaseUrl}common/location/update");
  }

  static Uri rateCustomer(String tripId) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/rate-enduser/$tripId");
  }

  static Uri getPreviousPassenger() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/previous-passenger");
  }

  static Uri startCounter() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/counter/start");
  }

  static Uri getServices() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/service/list");
  }

  static Uri changeSeviceStatus(String id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/service/change-status/$id");
  }

  static Uri endCounter(String id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/counter/end/$id");
  }

  static Uri previousTrips() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/previous-trips");
  }

  static Uri getActiveTrip() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/current-trip");
  }

  static Uri acceptNewRequest(String id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/new-request/accept/$id");
  }

  static Uri rejectTrip(String tripID) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/cancel-ride/$tripID");
  }

  static Uri rejectTrip2(String tripID) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/reject/$tripID");
  }

  static Uri rejectTripReason() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/cancel-reasons");
  }

  static Uri changeTripStatus() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/trip/change-status");
  }

  static Uri getNeighbourhood() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/neighbourhood");
  }

  static Uri getSingleNeighbourhood(String id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/neighbourhood/show/$id");
  }

  static Uri chooseDistance() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/distance/update");
  }

  static Uri suggestionComplaint() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/suggestion-complaint/store");
  }

  static Uri getTechSupportChats() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/technical-support/get-messages");
  }

  static Uri getNotifications() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/notification/list");
  }

  static Uri sendTechSupportChats() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/technical-support/send-message");
  }

  static Uri sendChat() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/send-message");
  }

  static Uri fetchChat(receiverID, userId) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/get-messages/$receiverID?sender_id=$userId");
  }

  static Uri getAccountType({cityId}) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/account-type/list?city_id=$cityId");
  }

  static Uri fetchCards() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/payment-card");
  }

  static Uri deleteCards(id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/payment-card/delete/$id");
  }

  static fetchOffersUrl(id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/offers-discounts?role_id=$id");
  }

  static redeemOffer(id) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/offer-and-discount/redeem-point/$id");
  }

  static Uri addCard() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/payment-card/store");
  }

  static Uri payCard() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/payment-card/pay");
  }

  static Uri transfer() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/payment-card/transfer");
  }

  static Uri updateGender() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/profile/change-clients-gender");
  }

  /// added endpoints
  static Uri getWaitTime(String type) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/waiting-time?type=$type");
  }

  static Uri getNearByUsers() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}driver/end-user/nearby");
  }

  static Uri getFees(String type) {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/fee?type=$type");
  }

  static Uri get deleteAccount {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/users/delete");
  }

  static Uri appVersion() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/versions/list?role_id=3");
  }

  static Uri rateApp() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/rate-app");
  }

  static Uri systemServicesList() {
    return Uri.parse("${AppEnvironment.apiBaseUrl}common/system-service/list");
  }
}
