import 'dart:async';
import 'dart:developer' as developer;
import 'dart:isolate';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:flutter_foreground_task/flutter_foreground_task.dart';
import 'package:flutter_libphonenumber/flutter_libphonenumber.dart';
// import 'package:flutter_overlay_window/flutter_overlay_window.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:provider/provider.dart';
import 'package:responsive_sizer/responsive_sizer.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:yalla_gai_driver/app_locale.dart';
import 'package:yalla_gai_driver/core/local_storage/local_storage.dart';
import 'package:yalla_gai_driver/core/notification/custom_local_notification.dart';
import 'package:yalla_gai_driver/core/service/location_service.dart';
import 'package:yalla_gai_driver/core/utils/theme.dart';
import 'package:yalla_gai_driver/environment.dart';
import 'package:yalla_gai_driver/features/booking/model/ride_request.dart';
import 'package:yalla_gai_driver/features/booking/presentation/screens/accept_ride.dart';
import 'package:yalla_gai_driver/features/booking/presentation/widgets/accept_trip_bottom_sheet.dart';
import 'package:yalla_gai_driver/overlay_window.dart';

import 'core/constants/url_model.dart';
import 'core/routes/router_config.dart';
import 'core/utils/bloc_observer.dart';
import 'core/utils/bloc_providers.dart';
import 'features/background/location_sending_task.dart';
import 'features/booking/repository/trip_repository.dart';
import 'firebase_options.dart';
import 'notifications/firebase_push_notifications_helper.dart';
import 'package:yalla_gai_driver/l10n/app_localizations.dart';

import 'package:yalla_gai_driver/l10n/app_localizations.dart';

void overlayEntry(SendPort mainSendPort) {
  // Simulate sending data to main app after some async task
  Future.delayed(Duration(seconds: 2), () {
    mainSendPort.send("Hello from overlay!");
  });
}

@pragma("vm:entry-point")
void overlayMain() {
  WidgetsFlutterBinding.ensureInitialized();

  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      PushNotificationHelper.initialized();
      FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(!kDebugMode);
      FirebaseAnalytics.instance.logAppOpen();

      await LocalStorage.instance.init();
      await _initEnvironment();
      await getBaseUrls();
      await CustomLocalNotification().init();

      var defaultFlutterErrorHandler = FlutterError.onError;
      FlutterError.onError = (details) {
        FirebaseCrashlytics.instance.recordFlutterError(details);
        defaultFlutterErrorHandler?.call(details);
      };

      // Record async errors in firebase crashlytics
      var defaultPlatformErrorHandler = PlatformDispatcher.instance.onError;
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack);
        return defaultPlatformErrorHandler?.call(error, stack) ?? false;
      };

      await init();
      // await getBaseUrls();
      Stripe.publishableKey = AppEnvironment.stripePublicKey;
      Bloc.observer = MyGlobalObserver();
      runApp(
        ChangeNotifierProvider(
          create: (context) => LocationService(),
          child: MultiBlocProvider(
            providers: getAllBlocProviders(),
            child: ChangeNotifierProvider(
              create: (context) => AppLocale(),
              builder: (context, child) {
                final provider = Provider.of<AppLocale>(context);

                return MaterialApp(
                  debugShowCheckedModeBanner: false,
                  theme: appTheme,
                  locale: provider.locale,
                  // locale: Locale("ar"),
                  supportedLocales: AppLocalizations.supportedLocales,
                  localizationsDelegates:
                      AppLocalizations.localizationsDelegates,
                  // home: TrueCallerOverlay(),
                );
              },
            ),
          ),
        ),
      );
    },
    (error, stackTrace) {
      FirebaseCrashlytics.instance.recordError(error, stackTrace);
    },
  );

  // runApp(
  //   ChangeNotifierProvider(
  //     create: (context) => LocationService(),
  //     child: MultiBlocProvider(
  //       providers: getAllBlocProviders(),
  //       child: ChangeNotifierProvider(
  //         create: (context) => AppLocale(),
  //         builder: (context, child) {
  //           final provider = Provider.of<AppLocale>(context);

  //           return MaterialApp(
  //             debugShowCheckedModeBanner: false,
  //             theme: appTheme,
  //             locale: provider.locale,
  //             // locale: Locale("ar"),
  //             supportedLocales: AppLocalizations.supportedLocales,
  //             localizationsDelegates: AppLocalizations.localizationsDelegates,
  //             home: TrueCallerOverlay(),
  //           );
  //         },
  //       ),
  //     ),
  //   ),
  // );
}

// class TrueCallerOverlay extends StatefulWidget {
//   const TrueCallerOverlay({super.key});

//   @override
//   State<TrueCallerOverlay> createState() => _TrueCallerOverlayState();
// }

// class _TrueCallerOverlayState extends State<TrueCallerOverlay> {
//   ComingRideRequest? requestData;
//   @override
//   Widget build(BuildContext context) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 40),
//       child: Builder(
//         builder: (context) {
//           developer.log(
//             "Request data from builder: ${requestData?.toJson()}",
//             name: "RequestData",
//           );
//           return requestData != null
//               ? OverlayWindow(rideRequest: requestData!)
//               : CircularProgressIndicator();
//         },
//       ),
//     );
//   }

//   @override
//   void initState() {
//     WidgetsBinding.instance.addPostFrameCallback(
//       (timeStamp) async {
//         developer.log(
//           "Overlay event received:",
//           name: "OverlayEvent",
//         );
//         FlutterOverlayWindow.closeOverlay();
//         // Future.delayed(
//         //   const Duration(seconds: 1),
//         //   () async {
//         //     await FlutterOverlayWindow.showOverlay(
//         //       enableDrag: true,
//         //       height: 570,
//         //     );
//         //     setState(() {
//         //       requestData = ComingRideRequest.fromJson(data);
//         //     });
//         //   },
//         // );

//         // Future.delayed(
//         //   const Duration(seconds: 20),
//         //   () async {
//         //     try {
//         //       const url = 'myapp://open';
//         //       if (await canLaunchUrl(Uri.parse(url))) {
//         //         await launchUrl(Uri.parse(url));
//         //       } else {
//         //         debugPrint('Cannot launch $url');
//         //       }
//         //       final platform = MethodChannel('overlay_channel');

//         //       await platform.invokeMethod('openAppFromOverlay');
//         //     } catch (e) {
//         //       developer.log(
//         //         "Error hiding overlay: $e",
//         //         name: "OverlayError",
//         //       );
//         //     }
//         //   },
//         // );
//         FlutterOverlayWindow.overlayListener.listen(
//           (event) async {
//             developer.log(
//               "Overlay event received: $event",
//               name: "OverlayEvent",
//             );
//             if (event != null) {
//               await FlutterOverlayWindow.showOverlay(
//                 enableDrag: true,
//                 height: 570,
//               );
//             }

//             print("object ===== ${requestData?.toJson()}");
//           },
//         );

//         // await LocalStorage.instance.init().then(
//         //   (val) {
//         //     final value = LocalStorage.instance.getString("key");
//         //     print("object ===== $value");
//         //   },
//         // );
//       },
//     );
//     super.initState();
//   }
// }

// .
void main() async {
  // await dotenv.load();
  // await injectionInit();

  runZonedGuarded(
    () async {
      WidgetsFlutterBinding.ensureInitialized();

      await Firebase.initializeApp(
        options: DefaultFirebaseOptions.currentPlatform,
      );
      PushNotificationHelper.initialized();
      FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(!kDebugMode);
      FirebaseAnalytics.instance.logAppOpen();

      await LocalStorage.instance.init();
      await _initEnvironment();
      await getBaseUrls();
      await CustomLocalNotification().init();

      var defaultFlutterErrorHandler = FlutterError.onError;
      FlutterError.onError = (details) {
        FirebaseCrashlytics.instance.recordFlutterError(details);
        defaultFlutterErrorHandler?.call(details);
      };

      // Record async errors in firebase crashlytics
      var defaultPlatformErrorHandler = PlatformDispatcher.instance.onError;
      PlatformDispatcher.instance.onError = (error, stack) {
        FirebaseCrashlytics.instance.recordError(error, stack);
        return defaultPlatformErrorHandler?.call(error, stack) ?? false;
      };

      await init();
      // await getBaseUrls();
      Stripe.publishableKey = AppEnvironment.stripePublicKey;
      Bloc.observer = MyGlobalObserver();
      runApp(
        ResponsiveSizer(
          builder: (context, orientation, screenType) {
            return ChangeNotifierProvider(
              create: (context) => LocationService(),
              child: MultiBlocProvider(
                providers: getAllBlocProviders(),
                child: const YallaGai(),
              ),
            );
          },
        ),
      );
    },
    (error, stackTrace) {
      FirebaseCrashlytics.instance.recordError(error, stackTrace);
    },
  );
}

// void backgroundTaskEntryPoint() {
//   FlutterForegroundTask.setTaskHandler(MyBackgroundTask());
// }

// The callback function should always be a top-level or static function.
@pragma('vm:entry-point')
Future<void> startCallback() async {
  WidgetsFlutterBinding.ensureInitialized();
  await LocalStorage.instance.init();
  // await getBaseUrls();
  FlutterForegroundTask.setTaskHandler(LocationTaskHandler());
}

Future<void> _initEnvironment() async {
  try {
    await FirebaseRemoteConfig.instance.ensureInitialized();
    await FirebaseRemoteConfig.instance.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: Durations.extralong4,
        minimumFetchInterval: Durations.extralong4,
      ),
    );
    await FirebaseRemoteConfig.instance
        .fetchAndActivate()
        .catchError((error, stackTrace) {
      developer.log("error:", error: error, stackTrace: stackTrace);
      FirebaseCrashlytics.instance.recordError(error, stackTrace);
      return false;
    });

    final apiBaseUrl = FirebaseRemoteConfig.instance.getString(
      "api_base_url".getTest,
    );

    final publicBaseUrl = FirebaseRemoteConfig.instance.getString(
      "public_base_url".getTest,
    );
    print("===>>>>  base url $apiBaseUrl === ${"public_base_url".getTest}");
    final googleMapsApiKey =
        FirebaseRemoteConfig.instance.getString("google_maps_api_key");
    final macSecretKey =
        FirebaseRemoteConfig.instance.getString("twilio".getTest);
    final stripePublicKey =
        FirebaseRemoteConfig.instance.getString("stripe_public_key");
    final stripeSecretKey =
        FirebaseRemoteConfig.instance.getString("stripe_secret_key");
    final pusherApiKey =
        FirebaseRemoteConfig.instance.getString("pusher_api_key".getTest);
    final pusherCluster =
        FirebaseRemoteConfig.instance.getString("pusher_cluster".getTest);
    final webSocketUrl =
        FirebaseRemoteConfig.instance.getString("websocket_url".getTest);
    final authSocketUrl =
        FirebaseRemoteConfig.instance.getString("auth_socket".getTest);

    if (apiBaseUrl.isNotEmpty) AppEnvironment.apiBaseUrl = apiBaseUrl;
    if (publicBaseUrl.isNotEmpty) AppEnvironment.publicBaseUrl = publicBaseUrl;
    if (webSocketUrl.isNotEmpty) AppEnvironment.websocketUrl = webSocketUrl;
    if (authSocketUrl.isNotEmpty) AppEnvironment.authSocketUrl = authSocketUrl;
    if (googleMapsApiKey.isNotEmpty) {
      AppEnvironment.googleMapsApiKey = googleMapsApiKey;
    }
    if (macSecretKey.isNotEmpty) AppEnvironment.macSecretKey = macSecretKey;
    if (stripePublicKey.isNotEmpty) {
      AppEnvironment.stripePublicKey = stripePublicKey;
    }
    if (stripeSecretKey.isNotEmpty) {
      AppEnvironment.stripeSecretKey = stripeSecretKey;
    }
    if (pusherApiKey.isNotEmpty) AppEnvironment.pusherApiKey = pusherApiKey;
    if (pusherCluster.isNotEmpty) AppEnvironment.pusherCluster = pusherCluster;
  } catch (error, stackTrace) {
    developer.log(
      "Unable to init remote config",
      error: error,
      stackTrace: stackTrace,
    );
  }
}

extension Strings on String {
  String get getTest => this + (AppEnvironment.apiEnvLocal ? '_test' : '');
}

class YallaGai extends StatefulWidget {
  const YallaGai({super.key});

  @override
  State<YallaGai> createState() => _YallaGaiState();
}

class _YallaGaiState extends State<YallaGai> {
  late final LocationService _locationService;

  @override
  void initState() {
    super.initState();
    _locationService = context.read<LocationService>();
    WidgetsBinding.instance.addObserver(_locationService);
    TripRepository().getWatingTime('waiting').then((value) {
      Timer.periodic(Duration(seconds: value.toInt()), (timer) async {
        await flutterLocalNotificationsPlugin.cancelAll();
      });
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(_locationService);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const AppRouter();
  }
}

class CircleAvatars extends StatelessWidget {
  /// Creates a circle that represents a user.
  const CircleAvatars({
    super.key,
    this.child,
    this.backgroundColor,
    this.backgroundImage,
    this.foregroundImage,
    this.onBackgroundImageError,
    this.onForegroundImageError,
    this.foregroundColor,
    this.radius,
    this.minRadius,
    this.maxRadius,
  })  : assert(radius == null || (minRadius == null && maxRadius == null)),
        assert(backgroundImage != null || onBackgroundImageError == null),
        assert(foregroundImage != null || onForegroundImageError == null);

  /// The widget below this widget in the tree.
  ///
  /// Typically a [Text] widget. If the [CircleAvatar] is to have an image, use
  /// [backgroundImage] instead.
  final Widget? child;

  /// The color with which to fill the circle. Changing the background
  /// color will cause the avatar to animate to the new color.
  ///
  /// If a [backgroundColor] is not specified and [ThemeData.useMaterial3] is true,
  /// [ColorScheme.primaryContainer] will be used, otherwise the theme's
  /// [ThemeData.primaryColorLight] is used with dark foreground colors, and
  /// [ThemeData.primaryColorDark] with light foreground colors.
  final Color? backgroundColor;

  /// The default text color for text in the circle.
  ///
  /// Defaults to the primary text theme color if no [backgroundColor] is
  /// specified.
  ///
  /// If a [foregroundColor] is not specified and [ThemeData.useMaterial3] is true,
  /// [ColorScheme.onPrimaryContainer] will be used, otherwise the theme's
  /// [ThemeData.primaryColorLight] for dark background colors, and
  /// [ThemeData.primaryColorDark] for light background colors.
  final Color? foregroundColor;

  /// The background image of the circle. Changing the background
  /// image will cause the avatar to animate to the new image.
  ///
  /// Typically used as a fallback image for [foregroundImage].
  ///
  /// If the [CircleAvatar] is to have the user's initials, use [child] instead.
  final ImageProvider? backgroundImage;

  /// The foreground image of the circle.
  ///
  /// Typically used as profile image. For fallback use [backgroundImage].
  final ImageProvider? foregroundImage;

  /// An optional error callback for errors emitted when loading
  /// [backgroundImage].
  final ImageErrorListener? onBackgroundImageError;

  /// An optional error callback for errors emitted when loading
  /// [foregroundImage].
  final ImageErrorListener? onForegroundImageError;

  /// The size of the avatar, expressed as the radius (half the diameter).
  ///
  /// If [radius] is specified, then neither [minRadius] nor [maxRadius] may be
  /// specified. Specifying [radius] is equivalent to specifying a [minRadius]
  /// and [maxRadius], both with the value of [radius].
  ///
  /// If neither [minRadius] nor [maxRadius] are specified, defaults to 20
  /// logical pixels. This is the appropriate size for use with
  /// [ListTile.leading].
  ///
  /// Changes to the [radius] are animated (including changing from an explicit
  /// [radius] to a [minRadius]/[maxRadius] pair or vice versa).
  final double? radius;

  /// The minimum size of the avatar, expressed as the radius (half the
  /// diameter).
  ///
  /// If [minRadius] is specified, then [radius] must not also be specified.
  ///
  /// Defaults to zero.
  ///
  /// Constraint changes are animated, but size changes due to the environment
  /// itself changing are not. For example, changing the [minRadius] from 10 to
  /// 20 when the [CircleAvatar] is in an unconstrained environment will cause
  /// the avatar to animate from a 20 pixel diameter to a 40 pixel diameter.
  /// However, if the [minRadius] is 40 and the [CircleAvatar] has a parent
  /// [SizedBox] whose size changes instantaneously from 20 pixels to 40 pixels,
  /// the size will snap to 40 pixels instantly.
  final double? minRadius;

  /// The maximum size of the avatar, expressed as the radius (half the
  /// diameter).
  ///
  /// If [maxRadius] is specified, then [radius] must not also be specified.
  ///
  /// Defaults to [double.infinity].
  ///
  /// Constraint changes are animated, but size changes due to the environment
  /// itself changing are not. For example, changing the [maxRadius] from 10 to
  /// 20 when the [CircleAvatar] is in an unconstrained environment will cause
  /// the avatar to animate from a 20 pixel diameter to a 40 pixel diameter.
  /// However, if the [maxRadius] is 40 and the [CircleAvatar] has a parent
  /// [SizedBox] whose size changes instantaneously from 20 pixels to 40 pixels,
  /// the size will snap to 40 pixels instantly.
  final double? maxRadius;

  // The default radius if nothing is specified.
  static const double _defaultRadius = 20.0;

  // The default min if only the max is specified.
  static const double _defaultMinRadius = 0.0;

  // The default max if only the min is specified.
  static const double _defaultMaxRadius = double.infinity;

  double get _minDiameter {
    if (radius == null && minRadius == null && maxRadius == null) {
      return _defaultRadius * 2.0;
    }
    return 2.0 * (radius ?? minRadius ?? _defaultMinRadius);
  }

  double get _maxDiameter {
    if (radius == null && minRadius == null && maxRadius == null) {
      return _defaultRadius * 2.0;
    }
    return 2.0 * (radius ?? maxRadius ?? _defaultMaxRadius);
  }

  @override
  Widget build(BuildContext context) {
    assert(debugCheckHasMediaQuery(context));
    final ThemeData theme = Theme.of(context);
    final Color? effectiveForegroundColor = foregroundColor ??
        (theme.useMaterial3 ? theme.colorScheme.onPrimaryContainer : null);
    final TextStyle effectiveTextStyle = theme.useMaterial3
        ? theme.textTheme.titleMedium!
        : theme.primaryTextTheme.titleMedium!;
    TextStyle textStyle =
        effectiveTextStyle.copyWith(color: effectiveForegroundColor);
    Color? effectiveBackgroundColor = backgroundColor ??
        (theme.useMaterial3 ? theme.colorScheme.primaryContainer : null);
    if (effectiveBackgroundColor == null) {
      effectiveBackgroundColor =
          switch (ThemeData.estimateBrightnessForColor(textStyle.color!)) {
        Brightness.dark => theme.primaryColorLight,
        Brightness.light => theme.primaryColorDark,
      };
    } else if (effectiveForegroundColor == null) {
      textStyle =
          switch (ThemeData.estimateBrightnessForColor(backgroundColor!)) {
        Brightness.dark => textStyle.copyWith(color: theme.primaryColorLight),
        Brightness.light => textStyle.copyWith(color: theme.primaryColorDark),
      };
    }
    final double minDiameter = _minDiameter;
    final double maxDiameter = _maxDiameter;
    return AnimatedContainer(
      constraints: BoxConstraints(
        minHeight: minDiameter,
        minWidth: minDiameter,
        maxWidth: maxDiameter,
        maxHeight: maxDiameter,
      ),
      duration: kThemeChangeDuration,
      decoration: BoxDecoration(
        color: effectiveBackgroundColor,
        image: backgroundImage != null
            ? DecorationImage(
                image: backgroundImage!,
                onError: onBackgroundImageError,
                fit: BoxFit.cover,
              )
            : null,
      ),
      foregroundDecoration: foregroundImage != null
          ? BoxDecoration(
              image: DecorationImage(
                image: foregroundImage!,
                onError: onForegroundImageError,
                fit: BoxFit.cover,
              ),
              shape: BoxShape.circle,
            )
          : null,
      child: child == null
          ? null
          : Center(
              // Need to disable text scaling here so that the text doesn't
              // escape the avatar when the textScaleFactor is large.
              child: MediaQuery.withNoTextScaling(
                child: IconTheme(
                  data: theme.iconTheme.copyWith(color: textStyle.color),
                  child: DefaultTextStyle(
                    style: textStyle,
                    child: child!,
                  ),
                ),
              ),
            ),
    );
  }
}

Map<String, dynamic> data = {
  "id": 178,
  "user_id": "8fd910e8-e429-40ed-aa89-4f770582b9a6",
  "driver_id": null,
  "car_type_id": null,
  "coupon_id": null,
  "pickup_location": "test_1",
  "pickup_latitude": 21.247122,
  "pickup_longitude": 72.91632,
  "destination_location": "HG23",
  "destination_latitude": null,
  "destination_longitude": null,
  "date_time": null,
  "fare": null,
  "status": "pending",
  "type": "general",
  "commission_paid_amount": null,
  "payment_method": "wallet",
  "reference_id": null,
  "created_at": "2025-06-10T15:56:52.000",
  "updated_at": "2025-06-10T15:56:52.000",
};
